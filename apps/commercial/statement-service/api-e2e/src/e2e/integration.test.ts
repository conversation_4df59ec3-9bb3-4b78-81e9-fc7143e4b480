import { HttpResponse, http } from 'msw';
import { createServer } from '@mswjs/http-middleware';
import { describeGroupsModule } from '@experience/commercial/statement-service/nest/groups-module/specs';
import { describeInvoiceModule } from '@experience/commercial/statement-service/nest/invoice-module/specs';
import { describeSitesModule } from '@experience/commercial/statement-service/nest/sites-module/specs';
import { describeStatementModule } from '@experience/commercial/statement-service/nest/statement-module/specs';
import { describeStripeModule } from '@experience/commercial/statement-service/nest/stripe-module/specs';
import { describeWebhookModule } from '@experience/commercial/statement-service/nest/webhook-module/specs';
import { describeWorkItemModule } from '@experience/commercial/statement-service/nest/work-item-module/specs';
import axios from 'axios';

const webappHandlers = [
  http.get(`/statements/:statementId`, () =>
    HttpResponse.html('<p>£10.00 £100.00</p>')
  ),
  http.get(`/statements/invoices/:invoiceId`, () =>
    HttpResponse.html('<p>£10.00</p>')
  ),
];

createServer(...webappHandlers).listen(5101);

const stripeHandlers = [
  http.post('/v1/account_links', () =>
    HttpResponse.json({
      url: 'http://localhost:5102/health',
    })
  ),
  http.post('/v1/accounts', () =>
    HttpResponse.json({ id: 'acc_hfJJeHsFdaV2H3s' })
  ),

  http.post('/v1/customers', () =>
    HttpResponse.json({
      id: 'cus_hfJJeHsFdaV2H3s',
    })
  ),
  http.get('/v1/customers/*', () =>
    HttpResponse.json({
      id: 'cus_hfJJeHsFdaV2H3s',
      name: 'Gallagher Shopping',
      invoice_settings: {
        default_payment_method: 'jdksdfksdn',
      },
    })
  ),
  http.get('/v1/customers/*/payment_methods/*', () =>
    HttpResponse.json({
      type: 'bacs_debit',
    })
  ),
  http.get('/v1/prices', () =>
    HttpResponse.json({
      data: [
        { id: 'price_1', lookup_key: 'site_management_service_fee_monthly' },
      ],
    })
  ),
  http.post('/v1/prices', () => HttpResponse.json({ id: 'price_1' })),
  http.post('/v1/invoices', () =>
    HttpResponse.json({
      id: 'in_hfJJeHsFdaV2H3s',
    })
  ),
  http.get('/v1/invoices', () =>
    HttpResponse.json({
      data: [
        {
          amount_due: 50,
          created: 12345,
          customer_email: '<EMAIL>',
          due_date: 67890,
          hosted_invoice_url: 'https://stripe.com/invoice?id=in_NggdGfEfND3Bfs',
          id: 'in_NggdGfEfND3Bfs',
          invoice_pdf: 'http://localhost:7777/invoice',
          number: 'ADF-1001',
          status: 'paid',
        },
      ],
    })
  ),
  http.post('/v1/invoiceitems', () =>
    HttpResponse.json({ id: 'invoiceItemId' })
  ),
  http.post('/v1/invoices/*/finalize', () =>
    HttpResponse.json({ invoice_pdf: 'http://localhost:7777/invoice' })
  ),
  http.post('/v1/invoices/*/pay', () =>
    HttpResponse.json({ id: 'in_NggdGfEfND3Bfs' })
  ),
  http.post('/v1/invoices/*/void', () =>
    HttpResponse.json({ id: 'in_NggdGfEfND3Bfs' })
  ),
  http.post('/v1/subscriptions/*', () =>
    HttpResponse.json({ id: 'sub_hfJJeHsFdaV2H3s' })
  ),
  http.post('/v1/subscriptions', () =>
    HttpResponse.json({ id: 'sub_hfJJeHsFdaV2H3s', status: 'active' })
  ),
  http.get('/v1/subscriptions/*', () =>
    HttpResponse.json({
      items: {
        data: [
          {
            id: 'sub_item_1',
            price: {
              id: 'price_1',
              lookup_key: 'site_management_service_fee_monthly',
            },
            quantity: 15,
          },
        ],
      },
    })
  ),
  http.get('invoice', () =>
    HttpResponse.arrayBuffer(Buffer.from('invoice.pdf'))
  ),
];

createServer(...stripeHandlers).listen(7777);

const PORT = 5102;
const BASE_URL = `http://localhost:${PORT}`;

describe('statement service api', () => {
  jest.setTimeout(180_000);

  describe('api', () => {
    it('should perform a health check', async () => {
      const response = await axios.get(`${BASE_URL}/health`);
      expect(response.status).toEqual(200);
      expect(response.data).toMatchSnapshot();
    });

    it('should expose open api specification', async () => {
      const response = await axios.get(`${BASE_URL}/api-json`);
      expect(response.status).toEqual(200);
      expect(response.data).toMatchSnapshot();
    });

    describeSitesModule(BASE_URL);
    describeGroupsModule(BASE_URL);
    describeWorkItemModule(BASE_URL);
    describeStatementModule(BASE_URL);
    describeInvoiceModule(BASE_URL);
    describeStripeModule(BASE_URL);
    describeWebhookModule(BASE_URL);
  });
});
