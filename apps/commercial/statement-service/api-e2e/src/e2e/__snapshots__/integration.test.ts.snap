// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`statement service api api groups module group documents controller should find a list of documents for a group 1`] = `
[
  {
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "name": "Test Document",
    "startDate": "2023-01-01T00:00:00.000Z",
    "type": "CONTRACT_SUMMARY",
    "uploadDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\(\\?:\\\\\\.\\\\d\\+\\)\\?\\(\\?:Z\\|\\[\\+-\\]\\\\d\\{2\\}:\\\\d\\{2\\}\\)\\$/,
    "url": "https://example.com/test-document.pdf",
  },
  {
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "name": "Sample Document",
    "startDate": "2023-02-01T00:00:00.000Z",
    "type": "CONTRACT",
    "uploadDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\(\\?:\\\\\\.\\\\d\\+\\)\\?\\(\\?:Z\\|\\[\\+-\\]\\\\d\\{2\\}:\\\\d\\{2\\}\\)\\$/,
    "url": "https://example.com/sample-document.docx",
  },
]
`;

exports[`statement service api api groups module group documents controller should upload a document for a group 1`] = `
[
  {
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "name": "Test Document",
    "startDate": "2023-01-01T00:00:00.000Z",
    "type": "CONTRACT_SUMMARY",
    "uploadDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\(\\?:\\\\\\.\\\\d\\+\\)\\?\\(\\?:Z\\|\\[\\+-\\]\\\\d\\{2\\}:\\\\d\\{2\\}\\)\\$/,
    "url": "https://example.com/test-document.pdf",
  },
  {
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "name": "Sample Document",
    "startDate": "2023-02-01T00:00:00.000Z",
    "type": "CONTRACT",
    "uploadDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\(\\?:\\\\\\.\\\\d\\+\\)\\?\\(\\?:Z\\|\\[\\+-\\]\\\\d\\{2\\}:\\\\d\\{2\\}\\)\\$/,
    "url": "https://example.com/sample-document.docx",
  },
  {
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "name": "Test Contract Document.pdf",
    "startDate": "2023-10-01T00:00:00.000Z",
    "type": "CONTRACT",
    "uploadDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\(\\?:\\\\\\.\\\\d\\+\\)\\?\\(\\?:Z\\|\\[\\+-\\]\\\\d\\{2\\}:\\\\d\\{2\\}\\)\\$/,
    "url": "https://statement-service-group-docs.s3.amazonaws.com/Group Inc./Test Contract Document.pdf",
  },
]
`;

exports[`statement service api api groups module groups controller should create a group 1`] = `
{
  "accountRef": "*********",
  "groupId": "036c0720-f908-45fc-ae7c-031a47c2e278",
  "groupName": "Test Group Inc.",
  "hasContractDocument": false,
  "sites": [],
  "transfersEnabled": false,
  "vatRegistered": true,
}
`;

exports[`statement service api api groups module groups controller should find a group by group id 1`] = `
{
  "accountRef": "",
  "addressLine1": "Flat 1",
  "addressLine2": "1 Test Street",
  "businessName": "Group Inc Invoice office",
  "county": "Test County",
  "groupId": "45c9ef22-b44b-4edd-bb66-392e923950af",
  "groupName": "Group Inc.",
  "hasContractDocument": true,
  "postcode": "TE1 1ST",
  "town": "Test Town",
  "transfersEnabled": false,
  "vatRegistered": true,
}
`;

exports[`statement service api api groups module groups controller should find a group by group id including sites 1`] = `
{
  "accountRef": "",
  "addressLine1": "Flat 1",
  "addressLine2": "1 Test Street",
  "businessName": "Group Inc Invoice office",
  "county": "Test County",
  "groupId": "45c9ef22-b44b-4edd-bb66-392e923950af",
  "groupName": "Group Inc.",
  "hasContractDocument": true,
  "postcode": "TE1 1ST",
  "sites": [
    {
      "automated": false,
      "emails": [
        "<EMAIL>",
        "<EMAIL>",
      ],
      "groupId": "45c9ef22-b44b-4edd-bb66-392e923950af",
      "id": "a2a8cfe6-2aa6-4ea3-aab2-d1246011f8e6",
      "siteId": "715",
      "siteName": "Group Inc HQ",
    },
  ],
  "town": "Test Town",
  "transfersEnabled": false,
  "vatRegistered": true,
}
`;

exports[`statement service api api groups module groups controller should find a list of groups 1`] = `
[
  {
    "accountRef": "",
    "groupId": "6c938bd0-39cd-4658-bef8-c3e063d9f674",
    "groupName": "BAM Group",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "35d492a5-545e-44ba-acd4-356e12567876",
    "groupName": "The Belfry Hotel & Golf Resort",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "c4014380-753b-432a-b042-f07a9c0a4fc7",
    "groupName": "British Sugar PLC",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "11933c51-5a7e-4cac-95db-5315ec7a1eae",
    "groupName": "Budgens - Darsham",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "6380d6b1-1c55-485c-bbd0-a3d364550826",
    "groupName": "Cumbria County Council",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "798cce60-4242-4ee7-92be-11465fc0238e",
    "groupName": "DPD",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "0bf68286-7bdb-4f65-9286-bdbe647dafdf",
    "groupName": "Fernden Heights",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "c693bc3c-70b2-439b-a3e8-3eaccfd86273",
    "groupName": "Hinckley & Bosworth Borough Council",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "3efc3c1b-068d-4728-ab29-cf8651ce3ecf",
    "groupName": "Lidl/Wickes Maidstone",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "a0be8d82-804e-4b32-bc96-9c7e4af94bcf",
    "groupName": "Liverpool University Hospital",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "********-ae6f-4dec-95e8-55ff0af3581a",
    "groupName": "Mildren Construction",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "e56afad7-bd93-4540-989d-05f066b30e27",
    "groupName": "Millbrook Parish Council",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "066e9b1b-511f-42b9-aa78-709a08ce990d",
    "groupName": "Pall Europe Ltd",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "8066c276-6ef4-4b30-9a9e-2299a0e1cd1f",
    "groupName": "Pattonair",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "3e3f1ad7-9605-4bea-be74-d7da3599f780",
    "groupName": "Pure Lake New Homes",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "980dc7e6-bec7-458a-a4e7-7bbc9b6a0536",
    "groupName": "Rendall and Rittner - The Waterson Building",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "fd68f18d-7c66-4282-b0f6-d30de7619caf",
    "groupName": "Rosevine Holiday Cottages",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "9cc8f1b3-5843-4649-983b-756bda3bc2c4",
    "groupName": "Seven Group",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "8b5b27f0-7285-4add-949e-02a723f5642b",
    "groupName": "Sharpsmart Ltd",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "833c51c2-28ed-4b9e-a15a-877a7647c5c2",
    "groupName": "Tesco Campus",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "********-53ac-4cd0-b666-f834df9ea9ee",
    "groupName": "Tesco Esso Alliance",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "5d5c2996-d7e5-43c6-a572-545b450ea1c2",
    "groupName": "Tesco Stores Ltd - Not Asset 1",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "51509f43-b48a-4c48-a0bf-acce2a7d8258",
    "groupName": "Warner Bros. Studios",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "d0190d60-8da7-4705-ad50-0aae0a9ee288",
    "groupName": "Forest Holidays",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "e8729adf-d17e-4155-a66f-0f4fe2d13cdd",
    "groupName": "Rock Village Institute",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "5b4b7e46-9708-4c07-aa5f-4e56fc8b4d62",
    "groupName": "Haig Housing",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "71f1e455-56ac-40bd-8cfe-0f54d8f5114b",
    "groupName": "Govia Thameslink Railway",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "ca59e592-ecec-493f-95d2-0bb7081415e2",
    "groupName": "Westmorland & Furness Council",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "8a236b54-de69-4573-8cda-14dd027eae2b",
    "groupName": "The Courtyard (Stanmer House) Estate Management Company Limited",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "addressLine1": "Flat 1",
    "addressLine2": "1 Test Street",
    "businessName": "Group Inc Invoice office",
    "county": "Test County",
    "groupId": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "groupName": "Group Inc.",
    "hasContractDocument": false,
    "postcode": "TE1 1ST",
    "town": "Test Town",
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "90cc3c5f-072d-4ab0-aa9f-9d4cbc07d00f",
    "groupName": "Group Corp.",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "69bef2ae-54a5-457b-a1b0-a83a0c3607d7",
    "groupName": "Group Ltd.",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "25f33a1c-d44a-44e8-8855-a2b81e1453ad",
    "groupName": "Group Plc.",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "TESTREF",
    "groupId": "94b8ef80-821f-409c-82fd-ec1fbf5590b4",
    "groupName": "Queue test group",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "e54240af-608d-45f3-a687-851b9582739e",
    "groupName": "Deferred payout status group",
    "hasContractDocument": false,
    "transfersEnabled": true,
    "vatRegistered": true,
  },
  {
    "accountRef": "123456",
    "groupId": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "groupName": "Tesco Stores Ltd",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "",
    "groupId": "b2de66af-834c-4dd6-b119-99d94ffc2abe",
    "groupName": "Coliseum Shopping Park",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "account1",
    "groupId": "725fead1-a43b-468b-a500-a279d8a47f95",
    "groupName": "Group 1",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  {
    "accountRef": "ref1",
    "groupId": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "groupName": "Gallagher Shopping",
    "hasContractDocument": false,
    "stripeConnectedAccountId": "acct_1IEAaZG16qv9bVQKQ0Sk0qHg",
    "stripeCustomerId": "cus_123",
    "stripeSubscriptionId": "sub_1MowQVLkdIwHu7ixeRlqHVzs",
    "stripeSubscriptionStatus": "incomplete",
    "transfersEnabled": true,
    "vatRegistered": true,
  },
  {
    "accountRef": "ROS",
    "groupId": "********-6e7e-4464-baca-65defa021a8b",
    "groupName": "Registers of Scotland",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
]
`;

exports[`statement service api api groups module groups controller should find a list of site admin groups 1`] = `
[
  {
    "activatedOn": "2023-01-01T00:00:00.000Z",
    "contactName": "Joe Bloggs",
    "contactNumber": "**********",
    "id": 1,
    "name": "Group 1",
    "stats": {
      "chargingDuration": 123455,
      "co2Savings": 1234,
      "energyCost": 100,
      "energyDelivered": 12345,
      "numberOfChargers": 123,
      "numberOfCharges": 123,
      "numberOfSites": 1,
      "revenueGenerated": 1234,
    },
    "type": "podPoint",
    "uid": "e2185341-c094-4c76-bc1d-b443f6759c4f",
  },
]
`;

exports[`statement service api api groups module groups controller should update a group by group id 1`] = `
{
  "accountRef": "*********",
  "addressLine1": "1 Sports Street",
  "addressLine2": "1 Test Street",
  "businessEmail": "<EMAIL>",
  "businessName": "JJB Sports",
  "county": "Greater Manchester",
  "groupId": "45c9ef22-b44b-4edd-bb66-392e923950af",
  "groupName": "Group Inc.",
  "hasContractDocument": true,
  "poNumber": "UK10010001",
  "postcode": "UP1 1UP",
  "sites": [
    {
      "automated": false,
      "emails": [
        "<EMAIL>",
        "<EMAIL>",
      ],
      "groupId": "45c9ef22-b44b-4edd-bb66-392e923950af",
      "id": "a2a8cfe6-2aa6-4ea3-aab2-d1246011f8e6",
      "siteId": "715",
      "siteName": "Group Inc HQ",
    },
  ],
  "town": "Wigan",
  "transfersEnabled": false,
  "vatRegistered": true,
}
`;

exports[`statement service api api groups module groups statements controller should find a list of statements for a group 1`] = `
[
  {
    "adjustedFees": [
      {
        "fee": 0.99,
        "ppid": "PG-86983",
      },
      {
        "fee": 0.42,
        "ppid": "PG-70500",
      },
    ],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 112.3,
      "energyDelivered": 123.45,
      "paidEnergyDelivered": 110,
    },
    "fees": {
      "gross": 10,
      "net": 8,
      "vat": 2,
    },
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "4287245c-6a15-4891-afe7-7fcb7dd9fa7f",
    "invoice": {
      "id": "24e0ad84-64ea-41fe-bf40-869268d83b21",
      "invoiceNumber": "AF0000001",
      "stripeInvoiceId": "in_NggdGfEfND3Bfs",
      "stripeInvoiceNumber": "ADF-1001",
      "stripeInvoiceStatus": "open",
    },
    "numberOfCharges": 20,
    "reference": "DDB102023",
    "revenue": {
      "gross": 2,
      "net": 1,
      "vat": 1,
    },
    "siteAddress": "Barrhead Road, Glasgow, G53 6AG",
    "workItem": {
      "automated": false,
      "groupName": "Tesco Stores Ltd",
      "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      "id": "90cc3c5f-072d-4ab0-aa9f-9d4cbc07d00f",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "75710",
      "siteName": "Tesco Extra - Silverburn",
      "status": "Sent",
    },
  },
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "f57eabda-6692-4888-becb-f45d6adbbf07",
    "numberOfCharges": 32,
    "payoutStatus": "TRANSFERRED",
    "reference": "DDB102024",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "Last Month Statement House",
    "workItem": {
      "automated": false,
      "groupName": "Tesco Stores Ltd",
      "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      "id": "d2c08249-95d5-45b9-99b1-2fffc7ca3047",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "75710",
      "siteName": "Tesco Extra - Silverburn",
      "status": "Sent",
    },
  },
]
`;

exports[`statement service api api invoice module should find a invoice 1`] = `
{
  "group": {
    "accountRef": "123456",
    "groupId": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "groupName": "Tesco Stores Ltd",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
  "invoiceDate": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
  "invoiceNumber": "AF0000001",
  "quoteNumber": "AF001",
  "site": {
    "automated": false,
    "emails": [
      "<EMAIL>",
    ],
    "groupId": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "siteId": "75710",
    "siteName": "Tesco Extra - Silverburn",
  },
  "statement": {
    "adjustedFees": [
      {
        "fee": 0.99,
        "ppid": "PG-86983",
      },
      {
        "fee": 0.42,
        "ppid": "PG-70500",
      },
    ],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 112.3,
      "energyDelivered": 123.45,
      "paidEnergyDelivered": 110,
    },
    "fees": {
      "gross": 10,
      "net": 8,
      "vat": 2,
    },
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "numberOfCharges": 20,
    "reference": "DDB102023",
    "revenue": {
      "gross": 2,
      "net": 1,
      "vat": 1,
    },
    "siteAddress": "Barrhead Road, Glasgow, G53 6AG",
    "workItem": {
      "automated": false,
      "groupName": "Tesco Stores Ltd",
      "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      "id": "90cc3c5f-072d-4ab0-aa9f-9d4cbc07d00f",
      "month": "2023-11-01",
      "siteId": "75710",
      "siteName": "Tesco Extra - Silverburn",
      "status": "Sent",
    },
  },
  "stripeInvoiceId": "in_NggdGfEfND3Bfs",
  "stripeInvoiceStatus": "open",
}
`;

exports[`statement service api api should expose open api specification 1`] = `
{
  "components": {
    "schemas": {
      "AdjustedFee": {
        "properties": {
          "fee": {
            "type": "number",
          },
          "ppid": {
            "type": "string",
          },
        },
        "required": [
          "ppid",
        ],
        "type": "object",
      },
      "AssignUserToWorkItemRequest": {
        "properties": {
          "email": {
            "type": "string",
          },
          "name": {
            "type": "string",
          },
        },
        "required": [
          "email",
          "name",
        ],
        "type": "object",
      },
      "Charge": {
        "properties": {
          "chargingDuration": {
            "type": "string",
          },
          "co2Savings": {
            "type": "string",
          },
          "confirmed": {
            "type": "boolean",
          },
          "confirmedBy": {
            "enum": [
              "driver",
              "other",
            ],
            "type": "string",
          },
          "door": {
            "type": "string",
          },
          "endedAt": {
            "type": "string",
          },
          "energyCost": {
            "type": "string",
          },
          "energyUsage": {
            "type": "string",
          },
          "locationId": {
            "type": "number",
          },
          "pluggedIn": {
            "type": "string",
          },
          "podName": {
            "type": "string",
          },
          "ppid": {
            "type": "string",
          },
          "revenueGenerated": {
            "type": "string",
          },
          "siteName": {
            "type": "string",
          },
          "startedAt": {
            "type": "string",
          },
          "totalDuration": {
            "type": "string",
          },
          "userEmail": {
            "type": "string",
          },
          "userName": {
            "type": "string",
          },
          "vehicle": {
            "type": "string",
          },
        },
        "required": [
          "chargingDuration",
          "confirmed",
          "co2Savings",
          "energyCost",
          "energyUsage",
          "pluggedIn",
          "revenueGenerated",
          "startedAt",
        ],
        "type": "object",
      },
      "ChargeSummary": {
        "properties": {
          "chargingDuration": {
            "type": "number",
          },
          "claimedEnergyUsage": {
            "type": "number",
          },
          "co2Savings": {
            "type": "number",
          },
          "energyCost": {
            "type": "number",
          },
          "energyDelivered": {
            "type": "number",
          },
          "energyUsage": {
            "type": "number",
          },
          "numberOfCharges": {
            "type": "number",
          },
          "revenueGenerated": {
            "type": "number",
          },
          "revenueGeneratingClaimedUsage": {
            "type": "number",
          },
        },
        "required": [
          "chargingDuration",
          "claimedEnergyUsage",
          "co2Savings",
          "energyCost",
          "energyDelivered",
          "energyUsage",
          "numberOfCharges",
          "revenueGenerated",
          "revenueGeneratingClaimedUsage",
        ],
        "type": "object",
      },
      "ChargerWithPod": {
        "properties": {
          "fee": {
            "maximum": 0.99,
            "minimum": 0,
            "nullable": true,
            "type": "number",
          },
          "pod": {
            "$ref": "#/components/schemas/Pod",
          },
          "ppid": {
            "type": "string",
          },
          "siteId": {
            "type": "string",
          },
        },
        "required": [
          "pod",
          "fee",
          "ppid",
          "siteId",
        ],
        "type": "object",
      },
      "Coordinates": {
        "properties": {
          "latitude": {
            "type": "number",
          },
          "longitude": {
            "type": "number",
          },
        },
        "type": "object",
      },
      "CreateDocumentRequest": {
        "properties": {
          "name": {
            "type": "string",
          },
          "startDate": {
            "type": "string",
          },
          "type": {
            "enum": [
              "CONTRACT",
              "CONTRACT_SUMMARY",
              "FRAMEWORK_CONTRACT",
              "FRAMEWORK_ORDER",
            ],
            "type": "string",
          },
        },
        "required": [
          "name",
          "startDate",
          "type",
        ],
        "type": "object",
      },
      "CreateGroupRequest": {
        "properties": {
          "accountRef": {
            "type": "string",
          },
          "groupId": {
            "type": "string",
          },
          "groupName": {
            "type": "string",
          },
        },
        "required": [
          "groupName",
          "groupId",
          "accountRef",
        ],
        "type": "object",
      },
      "CreateOrUpdateStripeSubscriptionRequest": {
        "properties": {
          "groupId": {
            "type": "string",
          },
          "socketQuantity": {
            "minimum": 1,
            "type": "number",
          },
        },
        "required": [
          "groupId",
          "socketQuantity",
        ],
        "type": "object",
      },
      "CreateStatementRequest": {
        "properties": {
          "adjustedFees": {
            "items": {
              "$ref": "#/components/schemas/AdjustedFee",
            },
            "type": "array",
          },
          "date": {
            "type": "string",
          },
          "groupUid": {
            "type": "string",
          },
          "siteId": {
            "type": "string",
          },
          "workItemId": {
            "type": "string",
          },
        },
        "required": [
          "adjustedFees",
          "date",
          "groupUid",
          "siteId",
          "workItemId",
        ],
        "type": "object",
      },
      "CreateStatementResponse": {
        "properties": {
          "statementId": {
            "type": "string",
          },
        },
        "required": [
          "statementId",
        ],
        "type": "object",
      },
      "CreateStripeConnectedAccountAccountLinkRequest": {
        "properties": {},
        "type": "object",
      },
      "CreateStripeConnectedAccountRequest": {
        "properties": {},
        "type": "object",
      },
      "CreateStripeCustomerRequest": {
        "properties": {},
        "type": "object",
      },
      "Document": {
        "properties": {
          "id": {
            "type": "string",
          },
          "name": {
            "type": "string",
          },
          "startDate": {
            "type": "string",
          },
          "type": {
            "enum": [
              "CONTRACT",
              "CONTRACT_SUMMARY",
              "FRAMEWORK_CONTRACT",
              "FRAMEWORK_ORDER",
            ],
            "type": "string",
          },
          "uploadDate": {
            "type": "string",
          },
          "url": {
            "type": "string",
          },
        },
        "required": [
          "id",
          "name",
          "startDate",
          "type",
          "uploadDate",
          "url",
        ],
        "type": "object",
      },
      "Email": {
        "properties": {
          "email": {
            "format": "email",
            "maxLength": 255,
            "type": "string",
          },
        },
        "required": [
          "email",
        ],
        "type": "object",
      },
      "Group": {
        "properties": {
          "accountRef": {
            "type": "string",
          },
          "addressLine1": {
            "type": "string",
          },
          "addressLine2": {
            "type": "string",
          },
          "businessEmail": {
            "type": "string",
          },
          "businessName": {
            "type": "string",
          },
          "county": {
            "type": "string",
          },
          "groupId": {
            "type": "string",
          },
          "groupName": {
            "type": "string",
          },
          "hasContractDocument": {
            "type": "boolean",
          },
          "poNumber": {
            "type": "string",
          },
          "postcode": {
            "type": "string",
          },
          "sites": {
            "items": {
              "$ref": "#/components/schemas/Site",
            },
            "type": "array",
          },
          "stripeConnectedAccountId": {
            "type": "string",
          },
          "stripeCustomerId": {
            "type": "string",
          },
          "stripeSubscriptionId": {
            "type": "string",
          },
          "stripeSubscriptionStatus": {
            "type": "string",
          },
          "town": {
            "type": "string",
          },
          "transfersEnabled": {
            "type": "boolean",
          },
          "vatRegistered": {
            "type": "boolean",
          },
        },
        "required": [
          "groupId",
          "groupName",
          "transfersEnabled",
          "vatRegistered",
        ],
        "type": "object",
      },
      "GroupChargeSummary": {
        "properties": {
          "chargingDuration": {
            "type": "number",
          },
          "co2Savings": {
            "type": "number",
          },
          "energyCost": {
            "type": "number",
          },
          "energyDelivered": {
            "type": "number",
          },
          "numberOfChargers": {
            "type": "number",
          },
          "numberOfCharges": {
            "type": "number",
          },
          "numberOfDrivers": {
            "type": "number",
          },
          "numberOfSites": {
            "type": "number",
          },
          "revenueGenerated": {
            "type": "number",
          },
        },
        "required": [
          "chargingDuration",
          "co2Savings",
          "energyCost",
          "energyDelivered",
          "numberOfChargers",
          "numberOfCharges",
          "numberOfDrivers",
          "numberOfSites",
          "revenueGenerated",
        ],
        "type": "object",
      },
      "InvoiceDto": {
        "properties": {
          "group": {
            "$ref": "#/components/schemas/Group",
          },
          "hostedInvoiceUrl": {
            "type": "string",
          },
          "id": {
            "type": "string",
          },
          "invoiceDate": {
            "type": "string",
          },
          "invoiceNumber": {
            "type": "string",
          },
          "quoteNumber": {
            "type": "string",
          },
          "site": {
            "$ref": "#/components/schemas/Site",
          },
          "statement": {
            "$ref": "#/components/schemas/Statement",
          },
          "stripeInvoiceId": {
            "type": "string",
          },
          "stripeInvoiceNumber": {
            "type": "string",
          },
          "stripeInvoiceStatus": {
            "type": "string",
          },
        },
        "required": [
          "id",
          "invoiceNumber",
          "quoteNumber",
          "invoiceDate",
          "group",
          "site",
          "statement",
        ],
        "type": "object",
      },
      "Pod": {
        "properties": {
          "ageYears": {
            "type": "number",
          },
          "chargeSummary": {
            "$ref": "#/components/schemas/ChargeSummary",
          },
          "confirmChargeEnabled": {
            "type": "boolean",
          },
          "coordinates": {
            "$ref": "#/components/schemas/Coordinates",
          },
          "description": {
            "type": "string",
          },
          "id": {
            "type": "number",
          },
          "installDate": {
            "type": "string",
          },
          "isEvZone": {
            "type": "boolean",
          },
          "isPublic": {
            "type": "boolean",
          },
          "lastContact": {
            "type": "string",
          },
          "model": {
            "type": "string",
          },
          "mostRecentCharge": {
            "$ref": "#/components/schemas/Charge",
          },
          "name": {
            "type": "string",
          },
          "ppid": {
            "type": "string",
          },
          "recentCharges": {
            "items": {
              "$ref": "#/components/schemas/Charge",
            },
            "type": "array",
          },
          "schedules": {
            "items": {
              "$ref": "#/components/schemas/Schedule",
            },
            "type": "array",
          },
          "schemes": {
            "items": {
              "$ref": "#/components/schemas/Group",
            },
            "type": "array",
          },
          "site": {
            "$ref": "#/components/schemas/Site",
          },
          "sockets": {
            "items": {
              "$ref": "#/components/schemas/Socket",
            },
            "type": "array",
          },
          "status": {
            "type": "string",
          },
          "supportsConfirmCharge": {
            "type": "boolean",
          },
          "supportsContactless": {
            "type": "boolean",
          },
          "supportsEnergyTariff": {
            "type": "boolean",
          },
          "supportsOcpp": {
            "type": "boolean",
          },
          "supportsPerKwh": {
            "type": "boolean",
          },
          "supportsRfid": {
            "type": "boolean",
          },
          "supportsTariffs": {
            "type": "boolean",
          },
          "tariff": {
            "$ref": "#/components/schemas/TariffSummary",
          },
        },
        "required": [
          "confirmChargeEnabled",
          "coordinates",
          "description",
          "id",
          "isEvZone",
          "isPublic",
          "model",
          "ppid",
          "schemes",
          "sockets",
          "status",
          "supportsContactless",
          "supportsEnergyTariff",
          "supportsPerKwh",
          "supportsTariffs",
        ],
        "type": "object",
      },
      "ReissueStripeInvoicesRequest": {
        "properties": {},
        "type": "object",
      },
      "Schedule": {
        "properties": {
          "endDay": {
            "type": "number",
          },
          "endTime": {
            "type": "string",
          },
          "isActive": {
            "type": "boolean",
          },
          "startDay": {
            "type": "number",
          },
          "startTime": {
            "type": "string",
          },
        },
        "required": [
          "endDay",
          "endTime",
          "isActive",
          "startDay",
          "startTime",
        ],
        "type": "object",
      },
      "Site": {
        "properties": {
          "automated": {
            "type": "boolean",
          },
          "emails": {
            "items": {
              "type": "string",
            },
            "type": "array",
          },
          "groupId": {
            "type": "string",
          },
          "id": {
            "type": "string",
          },
          "siteId": {
            "type": "string",
          },
          "siteName": {
            "type": "string",
          },
        },
        "required": [
          "automated",
          "emails",
          "groupId",
          "id",
          "siteId",
          "siteName",
        ],
        "type": "object",
      },
      "Socket": {
        "properties": {
          "door": {
            "type": "string",
          },
          "firmwareVersion": {
            "type": "string",
          },
          "isUpdateAvailable": {
            "type": "boolean",
          },
          "lastContact": {
            "type": "string",
          },
          "serialNumber": {
            "type": "string",
          },
          "status": {
            "enum": [
              "Available",
              "Charging",
              "Offline",
              "Unavailable",
            ],
            "type": "string",
          },
        },
        "required": [
          "door",
          "firmwareVersion",
          "isUpdateAvailable",
          "serialNumber",
          "status",
        ],
        "type": "object",
      },
      "Statement": {
        "properties": {
          "adjustedFees": {
            "items": {
              "$ref": "#/components/schemas/AdjustedFee",
            },
            "type": "array",
          },
          "automaticPayout": {
            "type": "boolean",
          },
          "emails": {
            "type": "string",
          },
          "energy": {
            "type": "object",
          },
          "fees": {
            "type": "object",
          },
          "groupUid": {
            "type": "string",
          },
          "id": {
            "type": "string",
          },
          "invoice": {
            "type": "object",
          },
          "numberOfCharges": {
            "type": "number",
          },
          "payoutStatus": {
            "type": "string",
          },
          "reference": {
            "type": "string",
          },
          "revenue": {
            "type": "object",
          },
          "siteAddress": {
            "type": "string",
          },
          "workItem": {
            "$ref": "#/components/schemas/WorkItem",
          },
        },
        "required": [
          "id",
          "adjustedFees",
          "automaticPayout",
          "emails",
          "energy",
          "fees",
          "groupUid",
          "numberOfCharges",
          "reference",
          "revenue",
          "siteAddress",
        ],
        "type": "object",
      },
      "Stats": {
        "properties": {
          "automated": {
            "type": "number",
          },
          "manual": {
            "type": "number",
          },
          "total": {
            "type": "number",
          },
        },
        "required": [
          "manual",
          "automated",
          "total",
        ],
        "type": "object",
      },
      "StatsWorkItems": {
        "properties": {
          "workitems": {
            "$ref": "#/components/schemas/WorkItemStatuses",
          },
        },
        "required": [
          "workitems",
        ],
        "type": "object",
      },
      "SubscriptionCharger": {
        "properties": {
          "ppid": {
            "type": "string",
          },
          "socket": {
            "enum": [
              "A",
              "B",
            ],
            "type": "string",
          },
        },
        "required": [
          "ppid",
          "socket",
        ],
        "type": "object",
      },
      "SubscriptionChargerWithAdditionalInfo": {
        "properties": {
          "hasSubscription": {
            "type": "boolean",
          },
          "name": {
            "type": "string",
          },
          "ppid": {
            "type": "string",
          },
          "siteAddress": {
            "type": "string",
          },
          "socket": {
            "enum": [
              "A",
              "B",
            ],
            "type": "string",
          },
        },
        "required": [
          "ppid",
          "socket",
          "hasSubscription",
        ],
        "type": "object",
      },
      "SubscriptionInvoiceDto": {
        "properties": {
          "amount": {
            "type": "number",
          },
          "created": {
            "type": "string",
          },
          "due": {
            "type": "string",
          },
          "email": {
            "type": "string",
          },
          "hostedInvoiceUrl": {
            "type": "string",
          },
          "id": {
            "type": "string",
          },
          "invoiceNumber": {
            "type": "string",
          },
          "invoicePdfUrl": {
            "type": "string",
          },
          "status": {
            "type": "string",
          },
        },
        "required": [
          "id",
          "amount",
          "created",
          "due",
          "email",
          "hostedInvoiceUrl",
          "invoiceNumber",
          "status",
        ],
        "type": "object",
      },
      "TariffSummary": {
        "properties": {
          "id": {
            "type": "number",
          },
          "name": {
            "type": "string",
          },
        },
        "required": [
          "id",
          "name",
        ],
        "type": "object",
      },
      "UpdateAutomatedStatusRequest": {
        "properties": {
          "automated": {
            "type": "boolean",
          },
        },
        "required": [
          "automated",
        ],
        "type": "object",
      },
      "UpdateChargerRequest": {
        "properties": {
          "chargers": {
            "items": {
              "$ref": "#/components/schemas/ChargerWithPod",
            },
            "type": "array",
          },
        },
        "required": [
          "chargers",
        ],
        "type": "object",
      },
      "UpdateGroupRequest": {
        "properties": {
          "accountRef": {
            "type": "string",
          },
          "addressLine1": {
            "type": "string",
          },
          "addressLine2": {
            "type": "string",
          },
          "businessEmail": {
            "format": "email",
            "type": "string",
          },
          "businessName": {
            "type": "string",
          },
          "county": {
            "type": "string",
          },
          "poNumber": {
            "type": "string",
          },
          "postcode": {
            "type": "string",
          },
          "town": {
            "type": "string",
          },
        },
        "required": [
          "businessName",
          "businessEmail",
          "accountRef",
          "addressLine1",
          "town",
          "county",
          "postcode",
        ],
        "type": "object",
      },
      "UpdateSiteRequest": {
        "properties": {
          "automated": {
            "type": "boolean",
          },
          "emails": {
            "items": {
              "$ref": "#/components/schemas/Email",
            },
            "minItems": 1,
            "type": "array",
          },
        },
        "required": [
          "emails",
          "automated",
        ],
        "type": "object",
      },
      "UpdateWorkItemStatusRequest": {
        "properties": {
          "status": {
            "enum": [
              "New",
              "Ready",
              "Generating",
              "Generated",
              "Sending",
              "Sent",
              "Cancelled",
            ],
            "type": "string",
          },
        },
        "required": [
          "status",
        ],
        "type": "object",
      },
      "WorkItem": {
        "properties": {
          "automated": {
            "type": "boolean",
          },
          "groupName": {
            "type": "string",
          },
          "groupUid": {
            "type": "string",
          },
          "id": {
            "type": "string",
          },
          "month": {
            "type": "string",
          },
          "previousStatement": {
            "$ref": "#/components/schemas/Statement",
          },
          "siteId": {
            "type": "string",
          },
          "siteName": {
            "type": "string",
          },
          "statement": {
            "$ref": "#/components/schemas/Statement",
          },
          "status": {
            "enum": [
              "New",
              "Ready",
              "Generating",
              "Generated",
              "Sending",
              "Sent",
              "Cancelled",
            ],
            "type": "string",
          },
          "userId": {
            "type": "string",
          },
          "userName": {
            "type": "string",
          },
        },
        "required": [
          "automated",
          "groupUid",
          "groupName",
          "id",
          "month",
          "siteId",
          "siteName",
          "status",
        ],
        "type": "object",
      },
      "WorkItemStatuses": {
        "properties": {
          "generated": {
            "$ref": "#/components/schemas/Stats",
          },
          "new": {
            "$ref": "#/components/schemas/Stats",
          },
          "ready": {
            "$ref": "#/components/schemas/Stats",
          },
          "sent": {
            "$ref": "#/components/schemas/Stats",
          },
        },
        "required": [
          "new",
          "ready",
          "generated",
          "sent",
        ],
        "type": "object",
      },
    },
  },
  "info": {
    "contact": {},
    "description": "",
    "title": "",
    "version": "1.0.0",
  },
  "openapi": "3.0.0",
  "paths": {
    "/groups": {
      "get": {
        "operationId": "GroupsController_findAllGroups",
        "parameters": [],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Group",
                  },
                  "type": "array",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Groups",
        ],
      },
      "post": {
        "operationId": "GroupsController_addGroup",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateGroupRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Groups",
        ],
      },
    },
    "/groups/site-admin": {
      "get": {
        "operationId": "GroupsController_findSiteAdminGroups",
        "parameters": [],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Group",
                  },
                  "type": "array",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Groups",
        ],
      },
    },
    "/groups/{groupId}": {
      "get": {
        "operationId": "GroupsController_findGroup",
        "parameters": [
          {
            "in": "path",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "includeSites",
            "required": true,
            "schema": {
              "type": "boolean",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Group",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Groups",
        ],
      },
      "put": {
        "operationId": "GroupsController_updateGroup",
        "parameters": [
          {
            "in": "path",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateGroupRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Groups",
        ],
      },
    },
    "/groups/{groupId}/chargers": {
      "get": {
        "operationId": "ChargersController_findChargersByGroupId",
        "parameters": [
          {
            "in": "path",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/SubscriptionChargerWithAdditionalInfo",
                  },
                  "type": "array",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Chargers",
        ],
      },
    },
    "/groups/{groupId}/documents": {
      "get": {
        "operationId": "DocumentsController_findDocumentsByGroupId",
        "parameters": [
          {
            "in": "path",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Document",
                  },
                  "type": "array",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Documents",
        ],
      },
      "post": {
        "operationId": "DocumentsController_uploadDocument",
        "parameters": [
          {
            "in": "path",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateDocumentRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Documents",
        ],
      },
    },
    "/groups/{groupId}/documents/{documentId}/download": {
      "get": {
        "operationId": "DocumentsController_downloadDocument",
        "parameters": [
          {
            "in": "path",
            "name": "documentId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Documents",
        ],
      },
    },
    "/groups/{groupId}/statements": {
      "get": {
        "operationId": "GroupsStatementsController_findStatementsByGroupId",
        "parameters": [
          {
            "in": "path",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Statement",
                  },
                  "type": "array",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "GroupsStatements",
        ],
      },
    },
    "/groups/{groupId}/subscription/chargers": {
      "get": {
        "operationId": "SubscriptionController_findSubscriptionChargersByGroupId",
        "parameters": [
          {
            "in": "path",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/SubscriptionCharger",
                  },
                  "type": "array",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Subscription",
        ],
      },
    },
    "/health": {
      "get": {
        "operationId": "HealthController_check",
        "parameters": [],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "properties": {
                    "details": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                      },
                      "type": "object",
                    },
                    "error": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {},
                      "nullable": true,
                      "type": "object",
                    },
                    "info": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                      },
                      "nullable": true,
                      "type": "object",
                    },
                    "status": {
                      "example": "ok",
                      "type": "string",
                    },
                  },
                  "type": "object",
                },
              },
            },
            "description": "The Health Check is successful",
          },
          "503": {
            "content": {
              "application/json": {
                "schema": {
                  "properties": {
                    "details": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                        "redis": {
                          "message": "Could not connect",
                          "status": "down",
                        },
                      },
                      "type": "object",
                    },
                    "error": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "redis": {
                          "message": "Could not connect",
                          "status": "down",
                        },
                      },
                      "nullable": true,
                      "type": "object",
                    },
                    "info": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                      },
                      "nullable": true,
                      "type": "object",
                    },
                    "status": {
                      "example": "error",
                      "type": "string",
                    },
                  },
                  "type": "object",
                },
              },
            },
            "description": "The Health Check is not successful",
          },
        },
        "tags": [
          "Health",
        ],
      },
    },
    "/invoices/export/ifs": {
      "get": {
        "operationId": "InvoiceController_generateIfsInvoicesExport",
        "parameters": [
          {
            "in": "query",
            "name": "date",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Invoice",
        ],
      },
    },
    "/invoices/reissue": {
      "post": {
        "operationId": "InvoiceController_reissueStripeInvoices",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ReissueStripeInvoicesRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Invoice",
        ],
      },
    },
    "/invoices/{invoiceId}": {
      "get": {
        "operationId": "InvoiceController_findInvoiceById",
        "parameters": [
          {
            "in": "path",
            "name": "invoiceId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/InvoiceDto",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Invoice",
        ],
      },
    },
    "/invoices/{invoiceId}/pdf": {
      "get": {
        "operationId": "InvoiceController_getInvoicePdf",
        "parameters": [
          {
            "in": "path",
            "name": "invoiceId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Invoice",
        ],
      },
    },
    "/invoices/{invoiceId}/status": {
      "put": {
        "operationId": "InvoiceController_updateInvoiceStatus",
        "parameters": [
          {
            "in": "path",
            "name": "invoiceId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Invoice",
        ],
      },
    },
    "/sites/{siteId}": {
      "put": {
        "operationId": "SitesController_updateSiteConfig",
        "parameters": [
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateSiteRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Sites",
        ],
      },
    },
    "/sites/{siteId}/chargers": {
      "get": {
        "operationId": "SitesController_findChargerConfigsWithPods",
        "parameters": [
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/ChargerWithPod",
                  },
                  "type": "array",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Sites",
        ],
      },
      "put": {
        "operationId": "SitesController_updateChargerConfig",
        "parameters": [
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateChargerRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Sites",
        ],
      },
    },
    "/statements": {
      "get": {
        "operationId": "StatementController_findAllStatements",
        "parameters": [
          {
            "in": "query",
            "name": "month",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "workItemStatus",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/Statement",
                  },
                  "type": "array",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Statement",
        ],
      },
      "post": {
        "operationId": "StatementController_createStatement",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateStatementRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CreateStatementResponse",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Statement",
        ],
      },
    },
    "/statements/{statementId}": {
      "get": {
        "operationId": "StatementController_findStatement",
        "parameters": [
          {
            "in": "path",
            "name": "statementId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Statement",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Statement",
        ],
      },
    },
    "/statements/{statementId}/pdf": {
      "get": {
        "operationId": "StatementController_getStatementPdf",
        "parameters": [
          {
            "in": "path",
            "name": "statementId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Statement",
        ],
      },
    },
    "/stripe/connected-accounts": {
      "delete": {
        "operationId": "StripeController_removeStripeConnectedAccountForGroup",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateStripeConnectedAccountRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Stripe",
        ],
      },
      "post": {
        "operationId": "StripeController_createStripeConnectedAccountForGroup",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateStripeConnectedAccountRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Stripe",
        ],
      },
    },
    "/stripe/connected-accounts/account-links": {
      "post": {
        "operationId": "StripeController_createStripeConnectedAccountLink",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateStripeConnectedAccountAccountLinkRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "content": {
              "application/json": {
                "schema": {
                  "type": "string",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Stripe",
        ],
      },
    },
    "/stripe/customers": {
      "post": {
        "operationId": "StripeController_createStripeCustomerForGroup",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateStripeCustomerRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Stripe",
        ],
      },
    },
    "/stripe/subscriptions": {
      "post": {
        "operationId": "StripeController_createStripeSubscriptionForGroup",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateOrUpdateStripeSubscriptionRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Stripe",
        ],
      },
      "put": {
        "operationId": "StripeController_updateStripeSubscriptionForGroup",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateOrUpdateStripeSubscriptionRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Stripe",
        ],
      },
    },
    "/stripe/subscriptions/{subscriptionId}": {
      "get": {
        "operationId": "StripeController_getSubscription",
        "parameters": [
          {
            "in": "path",
            "name": "subscriptionId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Stripe",
        ],
      },
    },
    "/stripe/subscriptions/{subscriptionId}/invoices": {
      "get": {
        "operationId": "StripeController_getInvoicesBySubscriptionId",
        "parameters": [
          {
            "in": "path",
            "name": "subscriptionId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/SubscriptionInvoiceDto",
                  },
                  "type": "array",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Stripe",
        ],
      },
    },
    "/stripe/subscriptions/{subscriptionId}/invoices/{invoiceId}": {
      "get": {
        "operationId": "StripeController_getSubscriptionInvoice",
        "parameters": [
          {
            "in": "path",
            "name": "invoiceId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "subscriptionId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/SubscriptionInvoiceDto",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "Stripe",
        ],
      },
    },
    "/stripe/subscriptions/{subscriptionId}/invoices/{invoiceId}/pdf": {
      "get": {
        "operationId": "StripeController_getSubscriptionInvoicePdf",
        "parameters": [
          {
            "in": "path",
            "name": "invoiceId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "subscriptionId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Stripe",
        ],
      },
    },
    "/stripe/webhooks": {
      "post": {
        "operationId": "WebhookController_webhook",
        "parameters": [
          {
            "in": "header",
            "name": "stripe-signature",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Webhook",
        ],
      },
    },
    "/stripe/webhooks/connect": {
      "post": {
        "operationId": "WebhookController_connectWebhook",
        "parameters": [
          {
            "in": "header",
            "name": "stripe-signature",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Webhook",
        ],
      },
    },
    "/work-items": {
      "get": {
        "operationId": "WorkItemController_findAllWorkItems",
        "parameters": [],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "items": {
                    "$ref": "#/components/schemas/WorkItem",
                  },
                  "type": "array",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "WorkItem",
        ],
      },
    },
    "/work-items/stats": {
      "get": {
        "operationId": "WorkItemController_countWorkItems",
        "parameters": [],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/StatsWorkItems",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "WorkItem",
        ],
      },
    },
    "/work-items/{workItemId}": {
      "get": {
        "operationId": "WorkItemController_findWorkItem",
        "parameters": [
          {
            "in": "path",
            "name": "workItemId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WorkItem",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "WorkItem",
        ],
      },
    },
    "/work-items/{workItemId}/automated": {
      "put": {
        "operationId": "WorkItemController_updateAutomatedStatus",
        "parameters": [
          {
            "in": "path",
            "name": "workItemId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateAutomatedStatusRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "WorkItem",
        ],
      },
    },
    "/work-items/{workItemId}/statement": {
      "put": {
        "operationId": "WorkItemController_updateStatementByWorkItemId",
        "parameters": [
          {
            "in": "path",
            "name": "workItemId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateSiteRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "WorkItem",
        ],
      },
    },
    "/work-items/{workItemId}/status": {
      "put": {
        "operationId": "WorkItemController_updateWorkItemStatus",
        "parameters": [
          {
            "in": "path",
            "name": "workItemId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateWorkItemStatusRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "WorkItem",
        ],
      },
    },
    "/work-items/{workItemId}/user": {
      "delete": {
        "operationId": "WorkItemController_removeUserFromWorkItem",
        "parameters": [
          {
            "in": "path",
            "name": "workItemId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "WorkItem",
        ],
      },
      "post": {
        "operationId": "WorkItemController_assignUserToWorkItem",
        "parameters": [
          {
            "in": "path",
            "name": "workItemId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AssignUserToWorkItemRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "WorkItem",
        ],
      },
    },
  },
  "servers": [],
  "tags": [],
}
`;

exports[`statement service api api should perform a health check 1`] = `
{
  "details": {
    "statements": {
      "status": "up",
    },
  },
  "error": {},
  "info": {
    "statements": {
      "status": "up",
    },
  },
  "status": "ok",
}
`;

exports[`statement service api api should return 403 forbidden for reissuing a Stripe invoice endpoint if the user doesnt have the correct permissions 1`] = `
{
  "error": "Forbidden",
  "message": "Forbidden resource",
  "statusCode": 403,
}
`;

exports[`statement service api api sites module sites controller should get all the chargers by site id 1`] = `
{
  "fee": null,
  "pod": Any<Object>,
  "ppid": Any<String>,
  "siteId": "963",
}
`;

exports[`statement service api api sites module sites controller should update a site by site id 1`] = `
{
  "accountRef": "",
  "addressLine1": "Flat 1",
  "addressLine2": "1 Test Street",
  "businessName": "Group Inc Invoice office",
  "county": "Test County",
  "groupId": "45c9ef22-b44b-4edd-bb66-392e923950af",
  "groupName": "Group Inc.",
  "hasContractDocument": true,
  "postcode": "TE1 1ST",
  "sites": [
    {
      "automated": false,
      "emails": [
        "<EMAIL>",
        "<EMAIL>",
      ],
      "groupId": "45c9ef22-b44b-4edd-bb66-392e923950af",
      "id": "a2a8cfe6-2aa6-4ea3-aab2-d1246011f8e6",
      "siteId": "715",
      "siteName": "Group Inc HQ",
    },
  ],
  "town": "Test Town",
  "transfersEnabled": false,
  "vatRegistered": true,
}
`;

exports[`statement service api api sites module sites controller should update the chargers by site id 1`] = `
{
  "fee": null,
  "pod": Any<Object>,
  "ppid": Any<String>,
  "siteId": "75710",
}
`;

exports[`statement service api api statement module statement controller should create a fresh statement 1`] = `
{
  "adjustedFees": [
    {
      "fee": 0.1,
      "ppid": "test",
    },
  ],
  "automaticPayout": false,
  "emails": "-",
  "energy": {
    "claimedEnergyDelivered": 123,
    "energyDelivered": 125,
    "paidEnergyDelivered": 1,
  },
  "fees": {
    "gross": 12,
    "net": 10,
    "vat": 2,
  },
  "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
  "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
  "invoice": {
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "invoiceNumber": "AF0000001",
  },
  "numberOfCharges": 2,
  "reference": "TST-123",
  "revenue": {
    "gross": 120,
    "net": 100,
    "vat": 20,
  },
  "siteAddress": "123 Fake Street FK1 1AA",
  "workItem": {
    "automated": false,
    "groupName": "Registers of Scotland",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "0a69aa8f-fa98-4488-a113-4ea8aca29e5d",
    "month": "2023-11-01",
    "siteId": "715",
    "siteName": "Discovery House",
    "status": "Generated",
  },
}
`;

exports[`statement service api api statement module statement controller should create an admin fee invoice 1`] = `
{
  "adjustedFees": [
    {
      "fee": 0.1,
      "ppid": "test",
    },
  ],
  "automaticPayout": false,
  "emails": "-",
  "energy": {
    "claimedEnergyDelivered": 123,
    "energyDelivered": 125,
    "paidEnergyDelivered": 1,
  },
  "fees": {
    "gross": 12,
    "net": 10,
    "vat": 2,
  },
  "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
  "invoice": {
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "invoiceNumber": "AF0000005",
  },
  "numberOfCharges": 2,
  "reference": "TST-123",
  "revenue": {
    "gross": 120,
    "net": 100,
    "vat": 20,
  },
  "siteAddress": "123 Fake Street FK1 1AA",
  "workItem": {
    "automated": false,
    "groupName": "Registers of Scotland",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "********-fac8-4c2a-9daa-a83f89d71410",
    "month": "2023-10-01",
    "siteId": "978",
    "siteName": "Discovery House",
    "status": "Generated",
    "userId": "fd29a74c-a92d-4e8b-979d-fbd8e5752d56",
    "userName": "Testy McTesterson",
  },
}
`;

exports[`statement service api api statement module statement controller should create an admin fee invoice 2`] = `
{
  "group": {
    "accountRef": "123456",
    "groupId": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "groupName": "Tesco Stores Ltd",
    "hasContractDocument": false,
    "transfersEnabled": false,
    "vatRegistered": true,
  },
  "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
  "invoiceDate": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
  "invoiceNumber": "AF0000005",
  "quoteNumber": "AF005",
  "site": {
    "automated": false,
    "emails": [
      "<EMAIL>",
    ],
    "groupId": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "siteId": "978",
    "siteName": "Barrhead",
  },
  "statement": {
    "adjustedFees": [
      {
        "fee": 0.1,
        "ppid": "test",
      },
    ],
    "automaticPayout": false,
    "emails": "-",
    "energy": {
      "claimedEnergyDelivered": 123,
      "energyDelivered": 125,
      "paidEnergyDelivered": 1,
    },
    "fees": {
      "gross": 12,
      "net": 10,
      "vat": 2,
    },
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "numberOfCharges": 2,
    "reference": "TST-123",
    "revenue": {
      "gross": 120,
      "net": 100,
      "vat": 20,
    },
    "siteAddress": "123 Fake Street FK1 1AA",
    "workItem": {
      "automated": false,
      "groupName": "Registers of Scotland",
      "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      "id": "********-fac8-4c2a-9daa-a83f89d71410",
      "month": "2023-10-01",
      "siteId": "978",
      "siteName": "Discovery House",
      "status": "Generated",
      "userId": "fd29a74c-a92d-4e8b-979d-fbd8e5752d56",
      "userName": "Testy McTesterson",
    },
  },
}
`;

exports[`statement service api api statement module statement controller should create an admin fee invoice for a stripe customer 1`] = `
{
  "adjustedFees": [
    {
      "fee": 0.1,
      "ppid": "test",
    },
  ],
  "automaticPayout": true,
  "emails": "-",
  "energy": {
    "claimedEnergyDelivered": 123,
    "energyDelivered": 125,
    "paidEnergyDelivered": 1,
  },
  "fees": {
    "gross": 12,
    "net": 10,
    "vat": 2,
  },
  "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
  "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
  "invoice": {
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "invoiceNumber": "AF0000006",
    "stripeInvoiceId": "in_hfJJeHsFdaV2H3s",
  },
  "numberOfCharges": 2,
  "payoutStatus": "PENDING",
  "reference": "TST-123",
  "revenue": {
    "gross": 120,
    "net": 100,
    "vat": 20,
  },
  "siteAddress": "123 Fake Street FK1 1AA",
  "workItem": {
    "automated": false,
    "groupName": "Registers of Scotland",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "69bef2ae-54a5-457b-a1b0-a83a0c3607d7",
    "month": "2023-11-01",
    "siteId": "1106",
    "siteName": "Discovery House",
    "status": "Generated",
  },
}
`;

exports[`statement service api api statement module statement controller should create an admin fee invoice for a stripe customer 2`] = `
{
  "group": {
    "accountRef": "ref1",
    "groupId": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "groupName": "Gallagher Shopping",
    "hasContractDocument": false,
    "stripeConnectedAccountId": "acct_1IEAaZG16qv9bVQKQ0Sk0qHg",
    "stripeCustomerId": "cus_123",
    "stripeSubscriptionId": "sub_1MowQVLkdIwHu7ixeRlqHVzs",
    "stripeSubscriptionStatus": "incomplete",
    "transfersEnabled": true,
    "vatRegistered": true,
  },
  "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
  "invoiceDate": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
  "invoiceNumber": "AF0000006",
  "quoteNumber": "AF006",
  "site": {
    "automated": false,
    "emails": [
      "<EMAIL>",
    ],
    "groupId": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "siteId": "1106",
    "siteName": "Gallagher Shopping",
  },
  "statement": {
    "adjustedFees": [
      {
        "fee": 0.1,
        "ppid": "test",
      },
    ],
    "automaticPayout": true,
    "emails": "-",
    "energy": {
      "claimedEnergyDelivered": 123,
      "energyDelivered": 125,
      "paidEnergyDelivered": 1,
    },
    "fees": {
      "gross": 12,
      "net": 10,
      "vat": 2,
    },
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "numberOfCharges": 2,
    "payoutStatus": "PENDING",
    "reference": "TST-123",
    "revenue": {
      "gross": 120,
      "net": 100,
      "vat": 20,
    },
    "siteAddress": "123 Fake Street FK1 1AA",
    "workItem": {
      "automated": false,
      "groupName": "Registers of Scotland",
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "69bef2ae-54a5-457b-a1b0-a83a0c3607d7",
      "month": "2023-11-01",
      "siteId": "1106",
      "siteName": "Discovery House",
      "status": "Generated",
    },
  },
  "stripeInvoiceId": "in_hfJJeHsFdaV2H3s",
}
`;

exports[`statement service api api statement module statement controller should find a list of statements 1`] = `
[
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "f57eabda-6692-4888-becb-f45d6adbbf07",
    "numberOfCharges": 32,
    "payoutStatus": "TRANSFERRED",
    "reference": "DDB102024",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "Last Month Statement House",
    "workItem": {
      "automated": false,
      "groupName": "Tesco Stores Ltd",
      "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      "id": "d2c08249-95d5-45b9-99b1-2fffc7ca3047",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "75710",
      "siteName": "Tesco Extra - Silverburn",
      "status": "Sent",
    },
  },
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "645083c1-8243-432e-ad17-b8d31b69de25",
    "numberOfCharges": 32,
    "payoutStatus": "PENDING",
    "reference": "DDB102024",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "The Simpson House",
    "workItem": {
      "automated": false,
      "groupName": "Gallagher shopping",
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "13924af1-f523-480a-b694-1989f339efd3",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "1106",
      "siteName": "Gallaghers shopping",
      "status": "Ready",
      "userId": "fd29a74c-a92d-4e8b-979d-fbd8e5752d56",
      "userName": "Testy McTesterson",
    },
  },
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "e1720628-5c2b-4fe0-b1ed-6fdcec064975",
    "invoice": {
      "id": "9a9a669f-c25e-41da-ab94-44a18e8587c3",
      "invoiceNumber": "AF0000002",
    },
    "numberOfCharges": 32,
    "payoutStatus": "TRANSFERRED",
    "reference": "DDB102024",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "The Simpson House",
    "workItem": {
      "automated": false,
      "groupName": "Gallagher shopping",
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "c97a0b5d-28a6-4a36-9540-84372bd2a00e",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "1105",
      "siteName": "Gallaghers shopping",
      "status": "Ready",
      "userId": "fd29a74c-a92d-4e8b-979d-fbd8e5752d56",
      "userName": "Testy McTesterson",
    },
  },
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
    "id": "83a5a130-1405-4ce3-b69f-96a92dbd9c72",
    "numberOfCharges": 32,
    "payoutStatus": "PENDING",
    "reference": "DeFFeRRed",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "Deferred street",
    "workItem": {
      "automated": false,
      "groupName": "Deferred group",
      "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
      "id": "1f2e698e-a07c-4f17-be9b-a20907d24ffb",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "12345",
      "siteName": "New Site",
      "status": "Generated",
    },
  },
  {
    "adjustedFees": [
      {
        "fee": 0.99,
        "ppid": "PG-86983",
      },
      {
        "fee": 0.42,
        "ppid": "PG-70500",
      },
    ],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 112.3,
      "energyDelivered": 123.45,
      "paidEnergyDelivered": 110,
    },
    "fees": {
      "gross": 10,
      "net": 8,
      "vat": 2,
    },
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "4287245c-6a15-4891-afe7-7fcb7dd9fa7f",
    "invoice": {
      "id": "24e0ad84-64ea-41fe-bf40-869268d83b21",
      "invoiceNumber": "AF0000001",
      "stripeInvoiceId": "in_NggdGfEfND3Bfs",
      "stripeInvoiceNumber": "ADF-1001",
      "stripeInvoiceStatus": "open",
    },
    "numberOfCharges": 20,
    "reference": "DDB102023",
    "revenue": {
      "gross": 2,
      "net": 1,
      "vat": 1,
    },
    "siteAddress": "Barrhead Road, Glasgow, G53 6AG",
    "workItem": {
      "automated": false,
      "groupName": "Tesco Stores Ltd",
      "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      "id": "90cc3c5f-072d-4ab0-aa9f-9d4cbc07d00f",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "75710",
      "siteName": "Tesco Extra - Silverburn",
      "status": "Sent",
    },
  },
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": "0843529a-9376-11ee-b9d1-0242ac120002",
    "numberOfCharges": 32,
    "reference": "DDB102024",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "The Simpson House",
    "workItem": {
      "automated": false,
      "groupName": "Pod Point - Software Team",
      "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
      "id": "9562ebb3-0c6e-4e6f-a7d4-a05ed1ba403f",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "964",
      "siteName": "The Simpson House",
      "status": "Ready",
      "userId": "fd29a74c-a92d-4e8b-979d-fbd8e5752d56",
      "userName": "Testy McTesterson",
    },
  },
]
`;

exports[`statement service api api statement module statement controller should find a list of statements from a given month 1`] = `
[
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": "0843529a-9376-11ee-b9d1-0242ac120002",
    "numberOfCharges": 32,
    "reference": "DDB102024",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "The Simpson House",
    "workItem": {
      "automated": false,
      "groupName": "Pod Point - Software Team",
      "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
      "id": "9562ebb3-0c6e-4e6f-a7d4-a05ed1ba403f",
      "month": "2023-10-01",
      "siteId": "964",
      "siteName": "The Simpson House",
      "status": "Ready",
      "userId": "fd29a74c-a92d-4e8b-979d-fbd8e5752d56",
      "userName": "Testy McTesterson",
    },
  },
]
`;

exports[`statement service api api statement module statement controller should find a list of statements with a given status 1`] = `
[
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "f57eabda-6692-4888-becb-f45d6adbbf07",
    "numberOfCharges": 32,
    "payoutStatus": "TRANSFERRED",
    "reference": "DDB102024",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "Last Month Statement House",
    "workItem": {
      "automated": false,
      "groupName": "Tesco Stores Ltd",
      "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      "id": "d2c08249-95d5-45b9-99b1-2fffc7ca3047",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "75710",
      "siteName": "Tesco Extra - Silverburn",
      "status": "Sent",
    },
  },
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "645083c1-8243-432e-ad17-b8d31b69de25",
    "numberOfCharges": 32,
    "payoutStatus": "PENDING",
    "reference": "DDB102024",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "The Simpson House",
    "workItem": {
      "automated": false,
      "groupName": "Gallagher shopping",
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "13924af1-f523-480a-b694-1989f339efd3",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "1106",
      "siteName": "Gallaghers shopping",
      "status": "Ready",
      "userId": "fd29a74c-a92d-4e8b-979d-fbd8e5752d56",
      "userName": "Testy McTesterson",
    },
  },
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "e1720628-5c2b-4fe0-b1ed-6fdcec064975",
    "invoice": {
      "id": "9a9a669f-c25e-41da-ab94-44a18e8587c3",
      "invoiceNumber": "AF0000002",
    },
    "numberOfCharges": 32,
    "payoutStatus": "TRANSFERRED",
    "reference": "DDB102024",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "The Simpson House",
    "workItem": {
      "automated": false,
      "groupName": "Gallagher shopping",
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "c97a0b5d-28a6-4a36-9540-84372bd2a00e",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "1105",
      "siteName": "Gallaghers shopping",
      "status": "Ready",
      "userId": "fd29a74c-a92d-4e8b-979d-fbd8e5752d56",
      "userName": "Testy McTesterson",
    },
  },
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
    "id": "83a5a130-1405-4ce3-b69f-96a92dbd9c72",
    "numberOfCharges": 32,
    "payoutStatus": "PENDING",
    "reference": "DeFFeRRed",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "Deferred street",
    "workItem": {
      "automated": false,
      "groupName": "Deferred group",
      "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
      "id": "1f2e698e-a07c-4f17-be9b-a20907d24ffb",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "12345",
      "siteName": "New Site",
      "status": "Generated",
    },
  },
  {
    "adjustedFees": [
      {
        "fee": 0.99,
        "ppid": "PG-86983",
      },
      {
        "fee": 0.42,
        "ppid": "PG-70500",
      },
    ],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 112.3,
      "energyDelivered": 123.45,
      "paidEnergyDelivered": 110,
    },
    "fees": {
      "gross": 10,
      "net": 8,
      "vat": 2,
    },
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "4287245c-6a15-4891-afe7-7fcb7dd9fa7f",
    "invoice": {
      "id": "24e0ad84-64ea-41fe-bf40-869268d83b21",
      "invoiceNumber": "AF0000001",
      "stripeInvoiceId": "in_NggdGfEfND3Bfs",
      "stripeInvoiceNumber": "ADF-1001",
      "stripeInvoiceStatus": "open",
    },
    "numberOfCharges": 20,
    "reference": "DDB102023",
    "revenue": {
      "gross": 2,
      "net": 1,
      "vat": 1,
    },
    "siteAddress": "Barrhead Road, Glasgow, G53 6AG",
    "workItem": {
      "automated": false,
      "groupName": "Tesco Stores Ltd",
      "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      "id": "90cc3c5f-072d-4ab0-aa9f-9d4cbc07d00f",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "75710",
      "siteName": "Tesco Extra - Silverburn",
      "status": "Sent",
    },
  },
  {
    "adjustedFees": [],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 172.2,
      "energyDelivered": 213.63,
      "paidEnergyDelivered": 156,
    },
    "fees": {
      "gross": 15,
      "net": 12,
      "vat": 5,
    },
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": "0843529a-9376-11ee-b9d1-0242ac120002",
    "numberOfCharges": 32,
    "reference": "DDB102024",
    "revenue": {
      "gross": 3,
      "net": 2,
      "vat": 2,
    },
    "siteAddress": "The Simpson House",
    "workItem": {
      "automated": false,
      "groupName": "Pod Point - Software Team",
      "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
      "id": "9562ebb3-0c6e-4e6f-a7d4-a05ed1ba403f",
      "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
      "siteId": "964",
      "siteName": "The Simpson House",
      "status": "Ready",
      "userId": "fd29a74c-a92d-4e8b-979d-fbd8e5752d56",
      "userName": "Testy McTesterson",
    },
  },
]
`;

exports[`statement service api api statement module statement controller should find a statement 1`] = `
{
  "adjustedFees": [
    {
      "fee": 0.99,
      "ppid": "PG-86983",
    },
    {
      "fee": 0.42,
      "ppid": "PG-70500",
    },
  ],
  "automaticPayout": false,
  "emails": "<EMAIL>",
  "energy": {
    "claimedEnergyDelivered": 112.3,
    "energyDelivered": 123.45,
    "paidEnergyDelivered": 110,
  },
  "fees": {
    "gross": 10,
    "net": 8,
    "vat": 2,
  },
  "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  "id": "4287245c-6a15-4891-afe7-7fcb7dd9fa7f",
  "invoice": {
    "id": "24e0ad84-64ea-41fe-bf40-869268d83b21",
    "invoiceNumber": "AF0000001",
    "stripeInvoiceId": "in_NggdGfEfND3Bfs",
    "stripeInvoiceNumber": "ADF-1001",
    "stripeInvoiceStatus": "open",
  },
  "numberOfCharges": 20,
  "reference": "DDB102023",
  "revenue": {
    "gross": 2,
    "net": 1,
    "vat": 1,
  },
  "siteAddress": "Barrhead Road, Glasgow, G53 6AG",
  "workItem": {
    "automated": false,
    "groupName": "Tesco Stores Ltd",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "90cc3c5f-072d-4ab0-aa9f-9d4cbc07d00f",
    "month": "2023-11-01",
    "siteId": "75710",
    "siteName": "Tesco Extra - Silverburn",
    "status": "Sent",
  },
}
`;

exports[`statement service api api statement module statement controller should overwrite an existing statement 1`] = `
{
  "adjustedFees": [
    {
      "fee": 0.1,
      "ppid": "test",
    },
  ],
  "automaticPayout": false,
  "emails": "-",
  "energy": {
    "claimedEnergyDelivered": 123,
    "energyDelivered": 125,
    "paidEnergyDelivered": 1,
  },
  "fees": {
    "gross": 12,
    "net": 10,
    "vat": 2,
  },
  "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
  "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
  "invoice": {
    "id": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "invoiceNumber": "AF0000003",
  },
  "numberOfCharges": 2,
  "reference": "TST-123",
  "revenue": {
    "gross": 120,
    "net": 100,
    "vat": 20,
  },
  "siteAddress": "123 Fake Street FK1 1AA",
  "workItem": {
    "automated": false,
    "groupName": "Registers of Scotland",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "0a69aa8f-fa98-4488-a113-4ea8aca29e5d",
    "month": "2023-11-01",
    "siteId": "715",
    "siteName": "Discovery House",
    "status": "Generated",
  },
}
`;

exports[`statement service api api stripe module stripe controller should create a connected account for a group 1`] = `
{
  "accountRef": "*********",
  "addressLine1": "1 Sports Street",
  "addressLine2": "1 Test Street",
  "businessEmail": "<EMAIL>",
  "businessName": "JJB Sports",
  "county": "Greater Manchester",
  "groupId": "45c9ef22-b44b-4edd-bb66-392e923950af",
  "groupName": "Group Inc.",
  "hasContractDocument": true,
  "poNumber": "UK10010001",
  "postcode": "UP1 1UP",
  "stripeConnectedAccountId": "acc_hfJJeHsFdaV2H3s",
  "stripeCustomerId": "cus_hfJJeHsFdaV2H3s",
  "town": "Wigan",
  "transfersEnabled": false,
  "vatRegistered": true,
}
`;

exports[`statement service api api stripe module stripe controller should create a stripe connected account link for a group 1`] = `"http://localhost:5102/health"`;

exports[`statement service api api stripe module stripe controller should create a stripe customer for a group 1`] = `
{
  "accountRef": "*********",
  "addressLine1": "1 Sports Street",
  "addressLine2": "1 Test Street",
  "businessEmail": "<EMAIL>",
  "businessName": "JJB Sports",
  "county": "Greater Manchester",
  "groupId": "45c9ef22-b44b-4edd-bb66-392e923950af",
  "groupName": "Group Inc.",
  "hasContractDocument": true,
  "poNumber": "UK10010001",
  "postcode": "UP1 1UP",
  "stripeCustomerId": "cus_hfJJeHsFdaV2H3s",
  "town": "Wigan",
  "transfersEnabled": false,
  "vatRegistered": true,
}
`;

exports[`statement service api api stripe module stripe controller should create a stripe subscription for a group 1`] = `
{
  "accountRef": "*********",
  "addressLine1": "1 Sports Street",
  "addressLine2": "1 Test Street",
  "businessEmail": "<EMAIL>",
  "businessName": "JJB Sports",
  "county": "Greater Manchester",
  "groupId": "45c9ef22-b44b-4edd-bb66-392e923950af",
  "groupName": "Group Inc.",
  "hasContractDocument": true,
  "poNumber": "UK10010001",
  "postcode": "UP1 1UP",
  "stripeConnectedAccountId": "acc_hfJJeHsFdaV2H3s",
  "stripeCustomerId": "cus_hfJJeHsFdaV2H3s",
  "stripeSubscriptionId": "sub_hfJJeHsFdaV2H3s",
  "town": "Wigan",
  "transfersEnabled": false,
  "vatRegistered": true,
}
`;

exports[`statement service api api stripe module stripe controller should get a list of invoices for a given subscription id 1`] = `
[
  {
    "amount": 50,
    "created": "1970-01-01",
    "due": "1970-01-01",
    "email": "<EMAIL>",
    "hostedInvoiceUrl": "https://stripe.com/invoice?id=in_NggdGfEfND3Bfs",
    "id": "in_NggdGfEfND3Bfs",
    "invoiceNumber": "ADF-1001",
    "invoicePdfUrl": "http://localhost:7777/invoice",
    "status": "paid",
  },
]
`;

exports[`statement service api api stripe module stripe controller should get an invoice by subscription id and invoice id 1`] = `
{
  "amount": 50,
  "created": "1970-01-01",
  "due": "1970-01-01",
  "email": "<EMAIL>",
  "hostedInvoiceUrl": "https://stripe.com/invoice?id=in_NggdGfEfND3Bfs",
  "id": "in_NggdGfEfND3Bfs",
  "invoiceNumber": "ADF-1001",
  "invoicePdfUrl": "http://localhost:7777/invoice",
  "status": "paid",
}
`;

exports[`statement service api api webhook controller should update a connected account and statement payout status when receiving an account.updated event 1`] = `
{
  "accountRef": "ref1",
  "groupId": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
  "groupName": "Gallagher Shopping",
  "hasContractDocument": false,
  "stripeConnectedAccountId": "acct_1IEAaZG16qv9bVQKQ0Sk0qHg",
  "stripeCustomerId": "cus_123",
  "stripeSubscriptionId": "sub_1MowQVLkdIwHu7ixeRlqHVzs",
  "stripeSubscriptionStatus": "active",
  "transfersEnabled": false,
  "vatRegistered": true,
}
`;

exports[`statement service api api webhook controller should update the subscription status when receiving an customer.subscription event 1`] = `
{
  "accountRef": "ref1",
  "groupId": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
  "groupName": "Gallagher Shopping",
  "hasContractDocument": false,
  "stripeConnectedAccountId": "acct_1IEAaZG16qv9bVQKQ0Sk0qHg",
  "stripeCustomerId": "cus_123",
  "stripeSubscriptionId": "sub_1MowQVLkdIwHu7ixeRlqHVzs",
  "stripeSubscriptionStatus": "active",
  "transfersEnabled": true,
  "vatRegistered": true,
}
`;

exports[`statement service api api work item module work item controller should assign a user to a work item 1`] = `
[
  {
    "automated": false,
    "groupName": "Registers of Scotland",
    "groupUid": "********-6e7e-4464-baca-65defa021a8b",
    "id": "f0c6efc7-b664-42d7-ae46-6b2bdb3eae20",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "7115",
    "siteName": "Discovery House",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "bc890451-f839-42f3-ab8a-4a046e0a5115",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "Sending",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "f937f91c-80e1-4aae-9962-3641ada24167",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "Generating",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": "9562ebb3-0c6e-4e6f-a7d4-a05ed1ba403f",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "964",
    "siteName": "The Simpson House",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
      "id": "0843529a-9376-11ee-b9d1-0242ac120002",
      "numberOfCharges": 32,
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Tesco",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "********-fac8-4c2a-9daa-a83f89d71410",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "978",
    "siteName": "Barrhead",
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Coliseum Shopping Park",
    "groupUid": "b2de66af-834c-4dd6-b119-99d94ffc2abe",
    "id": "25f33a1c-d44a-44e8-8855-a2b81e1453ad",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "963",
    "siteName": "Coliseum Shopping Park",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Gallagher Shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "69bef2ae-54a5-457b-a1b0-a83a0c3607d7",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallagher Shopping",
    "status": "Generated",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "0a69aa8f-fa98-4488-a113-4ea8aca29e5d",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "New",
  },
  {
    "automated": false,
    "groupName": "Tesco Stores Ltd",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "1f70f8ff-058a-4a67-aa7c-2f0be5e5a104",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "75710",
    "siteName": "Tesco Extra - Silverburn",
    "status": "Generated",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Steve Sandman",
  },
  {
    "automated": false,
    "groupName": "Queue test group",
    "groupUid": "94b8ef80-821f-409c-82fd-ec1fbf5590b4",
    "id": "3df89f29-89c9-4efb-b5ee-85766ae345f7",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1001",
    "siteName": "Site 1",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "25c02b88-a12a-485e-872f-08191fe8f8b9",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallaghers shopping",
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Deferred group",
    "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
    "id": "1f2e698e-a07c-4f17-be9b-a20907d24ffb",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "12345",
    "siteName": "New Site",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
      "id": "83a5a130-1405-4ce3-b69f-96a92dbd9c72",
      "numberOfCharges": 32,
      "payoutStatus": "PENDING",
      "reference": "DeFFeRRed",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "Deferred street",
    },
    "status": "Generated",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "13924af1-f523-480a-b694-1989f339efd3",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallaghers shopping",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "645083c1-8243-432e-ad17-b8d31b69de25",
      "numberOfCharges": 32,
      "payoutStatus": "PENDING",
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "c97a0b5d-28a6-4a36-9540-84372bd2a00e",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1105",
    "siteName": "Gallaghers shopping",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "e1720628-5c2b-4fe0-b1ed-6fdcec064975",
      "invoice": {
        "id": "9a9a669f-c25e-41da-ab94-44a18e8587c3",
        "invoiceNumber": "AF0000002",
      },
      "numberOfCharges": 32,
      "payoutStatus": "TRANSFERRED",
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
]
`;

exports[`statement service api api work item module work item controller should find a list of stats for work items 1`] = `
{
  "workitems": {
    "generated": {
      "automated": 0,
      "manual": 3,
      "total": 3,
    },
    "new": {
      "automated": 0,
      "manual": 1,
      "total": 1,
    },
    "ready": {
      "automated": 0,
      "manual": 8,
      "total": 8,
    },
    "sent": {
      "automated": 0,
      "manual": 1,
      "total": 1,
    },
  },
}
`;

exports[`statement service api api work item module work item controller should find a list of work items 1`] = `
[
  {
    "automated": false,
    "groupName": "Registers of Scotland",
    "groupUid": "********-6e7e-4464-baca-65defa021a8b",
    "id": "f0c6efc7-b664-42d7-ae46-6b2bdb3eae20",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "7115",
    "siteName": "Discovery House",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "bc890451-f839-42f3-ab8a-4a046e0a5115",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "Sending",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "f937f91c-80e1-4aae-9962-3641ada24167",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "Generating",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": "9562ebb3-0c6e-4e6f-a7d4-a05ed1ba403f",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "964",
    "siteName": "The Simpson House",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
      "id": "0843529a-9376-11ee-b9d1-0242ac120002",
      "numberOfCharges": 32,
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Tesco",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "********-fac8-4c2a-9daa-a83f89d71410",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "978",
    "siteName": "Barrhead",
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Coliseum Shopping Park",
    "groupUid": "b2de66af-834c-4dd6-b119-99d94ffc2abe",
    "id": "25f33a1c-d44a-44e8-8855-a2b81e1453ad",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "963",
    "siteName": "Coliseum Shopping Park",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Gallagher Shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "69bef2ae-54a5-457b-a1b0-a83a0c3607d7",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallagher Shopping",
    "status": "Generated",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "0a69aa8f-fa98-4488-a113-4ea8aca29e5d",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "New",
  },
  {
    "automated": false,
    "groupName": "Tesco Stores Ltd",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "1f70f8ff-058a-4a67-aa7c-2f0be5e5a104",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "75710",
    "siteName": "Tesco Extra - Silverburn",
    "status": "Generated",
  },
  {
    "automated": false,
    "groupName": "Queue test group",
    "groupUid": "94b8ef80-821f-409c-82fd-ec1fbf5590b4",
    "id": "3df89f29-89c9-4efb-b5ee-85766ae345f7",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1001",
    "siteName": "Site 1",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "25c02b88-a12a-485e-872f-08191fe8f8b9",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallaghers shopping",
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Deferred group",
    "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
    "id": "1f2e698e-a07c-4f17-be9b-a20907d24ffb",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "12345",
    "siteName": "New Site",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
      "id": "83a5a130-1405-4ce3-b69f-96a92dbd9c72",
      "numberOfCharges": 32,
      "payoutStatus": "PENDING",
      "reference": "DeFFeRRed",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "Deferred street",
    },
    "status": "Generated",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "13924af1-f523-480a-b694-1989f339efd3",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallaghers shopping",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "645083c1-8243-432e-ad17-b8d31b69de25",
      "numberOfCharges": 32,
      "payoutStatus": "PENDING",
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "c97a0b5d-28a6-4a36-9540-84372bd2a00e",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1105",
    "siteName": "Gallaghers shopping",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "e1720628-5c2b-4fe0-b1ed-6fdcec064975",
      "invoice": {
        "id": "9a9a669f-c25e-41da-ab94-44a18e8587c3",
        "invoiceNumber": "AF0000002",
      },
      "numberOfCharges": 32,
      "payoutStatus": "TRANSFERRED",
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
]
`;

exports[`statement service api api work item module work item controller should find a work item 1`] = `
{
  "automated": false,
  "groupName": "Tesco Stores Ltd",
  "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  "id": "90cc3c5f-072d-4ab0-aa9f-9d4cbc07d00f",
  "month": "2023-11-01",
  "previousStatement": {
    "adjustedFees": [
      {
        "fee": 0.99,
        "ppid": "PG-86983",
      },
      {
        "fee": 0.42,
        "ppid": "PG-70500",
      },
    ],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 112.3,
      "energyDelivered": 123.45,
      "paidEnergyDelivered": 110,
    },
    "fees": {
      "gross": 10,
      "net": 8,
      "vat": 2,
    },
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "4287245c-6a15-4891-afe7-7fcb7dd9fa7f",
    "invoice": {
      "id": "24e0ad84-64ea-41fe-bf40-869268d83b21",
      "invoiceNumber": "AF0000001",
      "stripeInvoiceId": "in_NggdGfEfND3Bfs",
      "stripeInvoiceNumber": "ADF-1001",
      "stripeInvoiceStatus": "open",
    },
    "numberOfCharges": 20,
    "reference": "DDB102023",
    "revenue": {
      "gross": 2,
      "net": 1,
      "vat": 1,
    },
    "siteAddress": "Barrhead Road, Glasgow, G53 6AG",
    "workItem": {
      "automated": false,
      "groupName": "Tesco Stores Ltd",
      "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      "id": "90cc3c5f-072d-4ab0-aa9f-9d4cbc07d00f",
      "month": "2023-11-01",
      "siteId": "75710",
      "siteName": "Tesco Extra - Silverburn",
      "status": "Sent",
    },
  },
  "siteId": "75710",
  "siteName": "Tesco Extra - Silverburn",
  "statement": {
    "adjustedFees": [
      {
        "fee": 0.99,
        "ppid": "PG-86983",
      },
      {
        "fee": 0.42,
        "ppid": "PG-70500",
      },
    ],
    "automaticPayout": false,
    "emails": "<EMAIL>",
    "energy": {
      "claimedEnergyDelivered": 112.3,
      "energyDelivered": 123.45,
      "paidEnergyDelivered": 110,
    },
    "fees": {
      "gross": 10,
      "net": 8,
      "vat": 2,
    },
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "4287245c-6a15-4891-afe7-7fcb7dd9fa7f",
    "invoice": {
      "id": "24e0ad84-64ea-41fe-bf40-869268d83b21",
      "invoiceNumber": "AF0000001",
      "stripeInvoiceId": "in_NggdGfEfND3Bfs",
      "stripeInvoiceNumber": "ADF-1001",
      "stripeInvoiceStatus": "open",
    },
    "numberOfCharges": 20,
    "reference": "DDB102023",
    "revenue": {
      "gross": 2,
      "net": 1,
      "vat": 1,
    },
    "siteAddress": "Barrhead Road, Glasgow, G53 6AG",
  },
  "status": "Sent",
}
`;

exports[`statement service api api work item module work item controller should update a work item's status 1`] = `
[
  {
    "automated": false,
    "groupName": "Registers of Scotland",
    "groupUid": "********-6e7e-4464-baca-65defa021a8b",
    "id": "f0c6efc7-b664-42d7-ae46-6b2bdb3eae20",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "7115",
    "siteName": "Discovery House",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "bc890451-f839-42f3-ab8a-4a046e0a5115",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "Sending",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "f937f91c-80e1-4aae-9962-3641ada24167",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "Generating",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": "9562ebb3-0c6e-4e6f-a7d4-a05ed1ba403f",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "964",
    "siteName": "The Simpson House",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
      "id": "0843529a-9376-11ee-b9d1-0242ac120002",
      "numberOfCharges": 32,
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Tesco",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "********-fac8-4c2a-9daa-a83f89d71410",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "978",
    "siteName": "Barrhead",
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Coliseum Shopping Park",
    "groupUid": "b2de66af-834c-4dd6-b119-99d94ffc2abe",
    "id": "25f33a1c-d44a-44e8-8855-a2b81e1453ad",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "963",
    "siteName": "Coliseum Shopping Park",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "0a69aa8f-fa98-4488-a113-4ea8aca29e5d",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "New",
  },
  {
    "automated": false,
    "groupName": "Tesco Stores Ltd",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "1f70f8ff-058a-4a67-aa7c-2f0be5e5a104",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "75710",
    "siteName": "Tesco Extra - Silverburn",
    "status": "Generated",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Steve Sandman",
  },
  {
    "automated": false,
    "groupName": "Queue test group",
    "groupUid": "94b8ef80-821f-409c-82fd-ec1fbf5590b4",
    "id": "3df89f29-89c9-4efb-b5ee-85766ae345f7",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1001",
    "siteName": "Site 1",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "25c02b88-a12a-485e-872f-08191fe8f8b9",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallaghers shopping",
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Deferred group",
    "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
    "id": "1f2e698e-a07c-4f17-be9b-a20907d24ffb",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "12345",
    "siteName": "New Site",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
      "id": "83a5a130-1405-4ce3-b69f-96a92dbd9c72",
      "numberOfCharges": 32,
      "payoutStatus": "PENDING",
      "reference": "DeFFeRRed",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "Deferred street",
    },
    "status": "Generated",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "13924af1-f523-480a-b694-1989f339efd3",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallaghers shopping",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "645083c1-8243-432e-ad17-b8d31b69de25",
      "numberOfCharges": 32,
      "payoutStatus": "PENDING",
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "c97a0b5d-28a6-4a36-9540-84372bd2a00e",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1105",
    "siteName": "Gallaghers shopping",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "e1720628-5c2b-4fe0-b1ed-6fdcec064975",
      "invoice": {
        "id": "9a9a669f-c25e-41da-ab94-44a18e8587c3",
        "invoiceNumber": "AF0000002",
      },
      "numberOfCharges": 32,
      "payoutStatus": "TRANSFERRED",
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
]
`;

exports[`statement service api api work item module work item controller should update automated flag for a site config 1`] = `
[
  {
    "automated": false,
    "groupName": "Registers of Scotland",
    "groupUid": "********-6e7e-4464-baca-65defa021a8b",
    "id": "f0c6efc7-b664-42d7-ae46-6b2bdb3eae20",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "7115",
    "siteName": "Discovery House",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "bc890451-f839-42f3-ab8a-4a046e0a5115",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "Sending",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "f937f91c-80e1-4aae-9962-3641ada24167",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "Generating",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": "9562ebb3-0c6e-4e6f-a7d4-a05ed1ba403f",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "964",
    "siteName": "The Simpson House",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
      "id": "0843529a-9376-11ee-b9d1-0242ac120002",
      "numberOfCharges": 32,
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Tesco",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "********-fac8-4c2a-9daa-a83f89d71410",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "978",
    "siteName": "Barrhead",
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Coliseum Shopping Park",
    "groupUid": "b2de66af-834c-4dd6-b119-99d94ffc2abe",
    "id": "25f33a1c-d44a-44e8-8855-a2b81e1453ad",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "963",
    "siteName": "Coliseum Shopping Park",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "0a69aa8f-fa98-4488-a113-4ea8aca29e5d",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "New",
  },
  {
    "automated": false,
    "groupName": "Tesco Stores Ltd",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "1f70f8ff-058a-4a67-aa7c-2f0be5e5a104",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "75710",
    "siteName": "Tesco Extra - Silverburn",
    "status": "Generated",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Steve Sandman",
  },
  {
    "automated": false,
    "groupName": "Queue test group",
    "groupUid": "94b8ef80-821f-409c-82fd-ec1fbf5590b4",
    "id": "3df89f29-89c9-4efb-b5ee-85766ae345f7",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1001",
    "siteName": "Site 1",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "25c02b88-a12a-485e-872f-08191fe8f8b9",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallaghers shopping",
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Deferred group",
    "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
    "id": "1f2e698e-a07c-4f17-be9b-a20907d24ffb",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "12345",
    "siteName": "New Site",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
      "id": "83a5a130-1405-4ce3-b69f-96a92dbd9c72",
      "numberOfCharges": 32,
      "payoutStatus": "PENDING",
      "reference": "DeFFeRRed",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "Deferred street",
    },
    "status": "Generated",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "13924af1-f523-480a-b694-1989f339efd3",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallaghers shopping",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "645083c1-8243-432e-ad17-b8d31b69de25",
      "numberOfCharges": 32,
      "payoutStatus": "PENDING",
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "c97a0b5d-28a6-4a36-9540-84372bd2a00e",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1105",
    "siteName": "Gallaghers shopping",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "e1720628-5c2b-4fe0-b1ed-6fdcec064975",
      "invoice": {
        "id": "9a9a669f-c25e-41da-ab94-44a18e8587c3",
        "invoiceNumber": "AF0000002",
      },
      "numberOfCharges": 32,
      "payoutStatus": "TRANSFERRED",
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
]
`;

exports[`statement service api api work item module work item controller should update site config 1`] = `
[
  {
    "automated": false,
    "groupName": "Registers of Scotland",
    "groupUid": "********-6e7e-4464-baca-65defa021a8b",
    "id": "f0c6efc7-b664-42d7-ae46-6b2bdb3eae20",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "7115",
    "siteName": "Discovery House",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "bc890451-f839-42f3-ab8a-4a046e0a5115",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "Sending",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "f937f91c-80e1-4aae-9962-3641ada24167",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "Generating",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": "9562ebb3-0c6e-4e6f-a7d4-a05ed1ba403f",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "964",
    "siteName": "The Simpson House",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
      "id": "0843529a-9376-11ee-b9d1-0242ac120002",
      "numberOfCharges": 32,
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Tesco",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "********-fac8-4c2a-9daa-a83f89d71410",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "978",
    "siteName": "Barrhead",
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Coliseum Shopping Park",
    "groupUid": "b2de66af-834c-4dd6-b119-99d94ffc2abe",
    "id": "25f33a1c-d44a-44e8-8855-a2b81e1453ad",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "963",
    "siteName": "Coliseum Shopping Park",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Group 1",
    "groupUid": "725fead1-a43b-468b-a500-a279d8a47f95",
    "id": "0a69aa8f-fa98-4488-a113-4ea8aca29e5d",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "715",
    "siteName": "Site 1",
    "status": "New",
  },
  {
    "automated": true,
    "groupName": "Tesco Stores Ltd",
    "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    "id": "1f70f8ff-058a-4a67-aa7c-2f0be5e5a104",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "75710",
    "siteName": "Tesco Extra - Silverburn",
    "status": "Generated",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Steve Sandman",
  },
  {
    "automated": false,
    "groupName": "Queue test group",
    "groupUid": "94b8ef80-821f-409c-82fd-ec1fbf5590b4",
    "id": "3df89f29-89c9-4efb-b5ee-85766ae345f7",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1001",
    "siteName": "Site 1",
    "status": "Ready",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "25c02b88-a12a-485e-872f-08191fe8f8b9",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallaghers shopping",
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Deferred group",
    "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
    "id": "1f2e698e-a07c-4f17-be9b-a20907d24ffb",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "12345",
    "siteName": "New Site",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "e54240af-608d-45f3-a687-851b9582739e",
      "id": "83a5a130-1405-4ce3-b69f-96a92dbd9c72",
      "numberOfCharges": 32,
      "payoutStatus": "PENDING",
      "reference": "DeFFeRRed",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "Deferred street",
    },
    "status": "Generated",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "13924af1-f523-480a-b694-1989f339efd3",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1106",
    "siteName": "Gallaghers shopping",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "645083c1-8243-432e-ad17-b8d31b69de25",
      "numberOfCharges": 32,
      "payoutStatus": "PENDING",
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
  {
    "automated": false,
    "groupName": "Gallagher shopping",
    "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    "id": "c97a0b5d-28a6-4a36-9540-84372bd2a00e",
    "month": StringMatching /\\^\\\\d\\{4\\}-\\(0\\[1-9\\]\\|1\\[012\\]\\)-\\(0\\[1-9\\]\\|\\[12\\]\\[0-9\\]\\|3\\[01\\]\\)\\$/,
    "siteId": "1105",
    "siteName": "Gallaghers shopping",
    "statement": {
      "adjustedFees": [],
      "automaticPayout": false,
      "emails": "<EMAIL>",
      "energy": {
        "claimedEnergyDelivered": 172.2,
        "energyDelivered": 213.63,
        "paidEnergyDelivered": 156,
      },
      "fees": {
        "gross": 15,
        "net": 12,
        "vat": 5,
      },
      "groupUid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      "id": "e1720628-5c2b-4fe0-b1ed-6fdcec064975",
      "invoice": {
        "id": "9a9a669f-c25e-41da-ab94-44a18e8587c3",
        "invoiceNumber": "AF0000002",
      },
      "numberOfCharges": 32,
      "payoutStatus": "TRANSFERRED",
      "reference": "DDB102024",
      "revenue": {
        "gross": 3,
        "net": 2,
        "vat": 2,
      },
      "siteAddress": "The Simpson House",
    },
    "status": "Ready",
    "userId": StringMatching /\\^\\[0-9A-F\\]\\{8\\}-\\[0-9A-F\\]\\{4\\}-4\\[0-9A-F\\]\\{3\\}-\\[89AB\\]\\[0-9A-F\\]\\{3\\}-\\[0-9A-F\\]\\{12\\}\\$/i,
    "userName": "Testy McTesterson",
  },
]
`;
