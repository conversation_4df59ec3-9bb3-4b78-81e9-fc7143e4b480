import {
  PodDrivePlanDTOAllowancePeriodEnum,
  PodDrivePlanDTOTypeEnum,
  PodDriveRewardsPlanDTOTypeEnum,
} from '@experience/mobile/subscriptions-api/axios';

export class SubscriptionDTO {
  id: string;
  plan: SubscriptionPlanDTO;
}

export class SubscriptionPlanDTO {
  id: string;
  type: PodDrivePlanDTOTypeEnum | PodDriveRewardsPlanDTOTypeEnum;
  allowanceMiles: number;
  allowancePeriod: PodDrivePlanDTOAllowancePeriodEnum;
  contractDurationMonths?: number;
  ratePencePerMile: number;
  rateMilesPerKwh: number;
}

export class SubscriptionPlanWithUserDTO {
  plan: SubscriptionPlanDTO;
  userId: string;
}
