import {
  BaseOrderEntity,
  UnpersistedSalesforceOrderEntity,
  UnpersistedSelfServiceOrderEntity,
} from './order.entity';
import {
  ParanoidEntityInterface,
  UnpersistedEntityHelper,
} from '@experience/mobile/clean-architecture';
import { PlanDetails, PlanEntity, UnpersistedPlanEntity } from './plan.entity';

export enum SubscriptionStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  CANCELLED = 'CANCELLED',
  SUSPENDED = 'SUSPENDED',
  ENDED = 'ENDED',
  REJECTED = 'REJECTED',
}

export class SubscriptionEntity<
  TOrder extends BaseOrderEntity,
  TPlanDetails extends PlanDetails
> implements ParanoidEntityInterface<string>
{
  constructor(params: SubscriptionEntity<TOrder, TPlanDetails>) {
    Object.assign(this, params);
  }

  id: string;
  userId: string; // firebase user id
  status: SubscriptionStatus;
  // TODO: this probably wants to removed from this entity
  order: TOrder;
  plan: PlanEntity<TPlanDetails>;
  activatedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}

interface Params {
  userId: string;
  status: SubscriptionStatus;
  plan: UnpersistedPlanEntity<PlanDetails>;
  order: UnpersistedSelfServiceOrderEntity | UnpersistedSalesforceOrderEntity;
  activatedAt: Date | null;
}

export class UnpersistedSubscriptionEntity<
  TOrder extends UnpersistedEntityHelper<BaseOrderEntity>,
  TPlanDetails extends PlanDetails
> implements
    UnpersistedEntityHelper<SubscriptionEntity<BaseOrderEntity, PlanDetails>>
{
  constructor(params: Params) {
    Object.assign(this, params);
  }

  userId: string;
  status: SubscriptionStatus;
  plan: UnpersistedPlanEntity<TPlanDetails>;
  order: TOrder;
  activatedAt: Date | null;
}

export type SubscriptionId = SubscriptionEntity<
  BaseOrderEntity,
  PlanDetails
>['id'];
