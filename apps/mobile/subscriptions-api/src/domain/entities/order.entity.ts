import {
  EntityInterface,
  EntityParams,
  UnpersistedEntityHelper,
} from '@experience/mobile/clean-architecture';

export enum OrderOrigin {
  SALESFORCE = 'SALESFORCE',
  SALESFORCE_TEST = 'SALESFORCE_TEST',
  POD_DRIVE_REWARDS_SELF_SERVE = 'POD_DRIVE_REWARDS_SELF_SERVE',
}

export abstract class BaseOrderEntity implements EntityInterface<string> {
  protected constructor(params: EntityParams<BaseOrderEntity>) {
    Object.assign(this, params);
  }

  id: string;
  origin: OrderOrigin;
  orderedAt: Date;
}

export class SalesforceOrderEntity extends BaseOrderEntity {
  constructor(params: EntityParams<SalesforceOrderEntity>) {
    super(params);
  }

  origin: OrderOrigin.SALESFORCE | OrderOrigin.SALESFORCE_TEST;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  address: {
    line1: string;
    line2: string | null;
    line3: string | null;
    postcode: string;
  };
  mpan: string;
  eCommerceId: string;
}

export class SelfServiceOrderEntity extends BaseOrderEntity {
  constructor(params: EntityParams<SelfServiceOrderEntity>) {
    super(params);
  }

  origin: OrderOrigin.POD_DRIVE_REWARDS_SELF_SERVE;
  ppid: string;
}

export class UnpersistedSalesforceOrderEntity
  implements UnpersistedEntityHelper<BaseOrderEntity>
{
  constructor(params: EntityParams<UnpersistedSalesforceOrderEntity>) {
    Object.assign(this, params);
  }

  id: string;
  origin: OrderOrigin.SALESFORCE | OrderOrigin.SALESFORCE_TEST;
  orderedAt: Date;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  address: {
    line1: string;
    line2: string | null;
    line3: string | null;
    postcode: string;
  };
  mpan: string;
  eCommerceId: string;
}

export class UnpersistedSelfServiceOrderEntity
  implements UnpersistedEntityHelper<BaseOrderEntity>
{
  constructor(params: EntityParams<UnpersistedSelfServiceOrderEntity>) {
    Object.assign(this, params);
  }

  origin: OrderOrigin.POD_DRIVE_REWARDS_SELF_SERVE;
  orderedAt: Date;
  ppid: string;
}
