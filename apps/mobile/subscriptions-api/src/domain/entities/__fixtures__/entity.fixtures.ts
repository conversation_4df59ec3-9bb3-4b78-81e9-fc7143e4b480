import {
  ActionEntity,
  ActionOwner,
  ActionStatus,
  ActionType,
  UnpersistedActionEntity,
} from '../action.entity';
import {
  OrderOrigin,
  SalesforceOrderEntity,
  SelfServiceOrderEntity,
} from '../order.entity';
import {
  PlanAllowancePeriod,
  PlanEntity,
  PlanType,
  PodDrivePlanDetails,
  PodDriveRewardsPlanDetails,
  UnpersistedPlanEntity,
} from '../plan.entity';
import {
  SubscriptionEntity,
  SubscriptionStatus,
  UnpersistedSubscriptionEntity,
} from '../subscription.entity';

export const MOCK_SUBSCRIPTION_ENTITY_ID =
  '78a28650-01c3-4fe4-9111-0c48ad227ef6';

export const MOCK_APPLICATION_ID = 1;

export const MOCK_POD_DRIVE_PLAN_ENTITY = new PlanEntity<PodDrivePlanDetails>({
  id: '82a26821-1d34-4af2-9aed-4f08ffe45af3',
  details: {
    type: PlanType.POD_DRIVE,
    productCode: 'pod_drive_product_01',
    allowanceMiles: 10_000,
    allowancePeriod: PlanAllowancePeriod.ANNUAL,
    upfrontFeePounds: 99,
    monthlyFeePounds: 35,
    contractDurationMonths: 18,
    ratePencePerMile: 2.28,
    rateMilesPerKwh: 3.5,
    milesRenewalDate: null,
  },
  subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
  createdAt: new Date('2025-03-05T16:40:00.000Z'),
  updatedAt: new Date('2025-03-05T16:40:00.000Z'),
  deletedAt: null,
});

export const MOCK_POD_DRIVE_REWARDS_PLAN_ENTITY =
  new PlanEntity<PodDriveRewardsPlanDetails>({
    id: '82a26821-1d34-4af2-9aed-4f08ffe45af3',
    details: {
      type: PlanType.POD_DRIVE_REWARDS,
      allowanceMiles: 10_000,
      allowancePeriod: PlanAllowancePeriod.ANNUAL,
      ratePencePerMile: 2.28,
      rateMilesPerKwh: 3.5,
      milesRenewalDate: null,
    },
    subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
    createdAt: new Date('2025-03-05T16:40:00.000Z'),
    updatedAt: new Date('2025-03-05T16:40:00.000Z'),
    deletedAt: null,
  });

export const MOCK_ORDER_ENTITY: SalesforceOrderEntity =
  new SalesforceOrderEntity({
    id: 'd4ce5a51-a6e6-4d2f-8433-d335b555330f',
    orderedAt: new Date('2025-03-05T16:40:00.000Z'),
    origin: OrderOrigin.SALESFORCE,
    address: {
      line1: "222 Gray's Inn Road",
      line2: null,
      line3: null,
      postcode: 'WC1X 8HB',
    },
    email: '<EMAIL>',
    firstName: 'Mobile',
    lastName: 'Tester',
    mpan: 'S 01 801 101 22 6130 5588 165',
    phoneNumber: '02072474114',
    eCommerceId: 'dr3l2e8dhu::2415',
  });

export const MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY = new ActionEntity({
  id: '3038082a-3ce5-45c8-9618-9bd2ea034942',
  subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
  owner: ActionOwner.USER,
  status: ActionStatus.PENDING,
  data: {
    type: ActionType.PAY_UPFRONT_FEE_V1,
  },
  dependsOn: [],
  createdAt: new Date('2025-03-05T16:40:00.000Z'),
  updatedAt: new Date('2025-03-05T16:40:00.000Z'),
  deletedAt: null,
});

export const MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY_SUCCESS = new ActionEntity({
  ...MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY,
  status: ActionStatus.SUCCESS,
});

export const MOCK_SURVEY_ACTION_ENTITY: ActionEntity = new ActionEntity({
  id: '28d6027c-ba71-4fe2-bcee-77bb6c274f73',
  subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
  owner: ActionOwner.USER,
  status: ActionStatus.PENDING,
  data: {
    type: ActionType.COMPLETE_HOME_SURVEY_V1,
    surveyUrl: 'http://www.example.com',
  },
  dependsOn: [MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY],
  createdAt: new Date('2025-03-05T16:40:00.000Z'),
  updatedAt: new Date('2025-03-05T16:40:00.000Z'),
  deletedAt: null,
});

export const MOCK_SURVEY_ACTION_ENTITY_SUCCESS = new ActionEntity({
  ...MOCK_SURVEY_ACTION_ENTITY,
  status: ActionStatus.SUCCESS,
});

export const MOCK_CHECK_AFFORDABILITY_ACTION: ActionEntity = new ActionEntity({
  id: 'f25baf59-fa95-45e6-8499-a70930548a36',
  subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
  owner: ActionOwner.SYSTEM,
  status: ActionStatus.PENDING,
  data: {
    type: ActionType.CHECK_AFFORDABILITY_V1,
    applicationId: null,
    loanId: null,
  },
  dependsOn: [MOCK_SURVEY_ACTION_ENTITY],
  createdAt: new Date('2025-03-05T16:40:00.000Z'),
  updatedAt: new Date('2025-03-05T16:40:00.000Z'),
  deletedAt: null,
});

export const MOCK_CHECK_AFFORDABILITY_ACTION_SUCCESS = new ActionEntity({
  ...MOCK_CHECK_AFFORDABILITY_ACTION,
  status: ActionStatus.SUCCESS,
});

export const MOCK_SETUP_DIRECT_DEBIT_ACTION: ActionEntity = new ActionEntity({
  id: '12ce3c19-2e05-4726-9f08-2d6416efc21f',
  subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
  owner: ActionOwner.USER,
  status: ActionStatus.PENDING,
  data: {
    type: ActionType.SETUP_DIRECT_DEBIT_V1,
  },
  dependsOn: [MOCK_CHECK_AFFORDABILITY_ACTION],
  createdAt: new Date('2025-03-05T16:40:00.000Z'),
  updatedAt: new Date('2025-03-05T16:40:00.000Z'),
  deletedAt: null,
});

export const MOCK_SETUP_DIRECT_DEBIT_ACTION_SUCCESS = new ActionEntity({
  ...MOCK_SETUP_DIRECT_DEBIT_ACTION,
  status: ActionStatus.SUCCESS,
});

export const MOCK_SIGN_REWARDS_TOS_ACTION: ActionEntity = new ActionEntity({
  id: 'e0224b8b-88cc-4189-986c-807291256336',
  subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
  owner: ActionOwner.USER,
  status: ActionStatus.PENDING,
  data: {
    type: ActionType.SIGN_REWARDS_TOS_V1,
    revision: null,
  },
  dependsOn: [],
  createdAt: new Date('2025-03-05T16:40:00.000Z'),
  updatedAt: new Date('2025-03-05T16:40:00.000Z'),
  deletedAt: null,
});

export const MOCK_SIGN_REWARDS_TOS_ACTION_SUCCESS = new ActionEntity({
  ...MOCK_SIGN_REWARDS_TOS_ACTION,
  status: ActionStatus.SUCCESS,
  data: {
    type: ActionType.SIGN_REWARDS_TOS_V1,
    revision: 'test',
  },
});

export const MOCK_LINK_EXISTING_CHARGER_ACTION: ActionEntity = new ActionEntity(
  {
    id: 'd4d1d239-a241-4868-b786-b58ba02d1389',
    subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
    owner: ActionOwner.USER,
    status: ActionStatus.PENDING,
    data: {
      type: ActionType.LINK_EXISTING_CHARGER_V1,
      ppid: null,
    },
    dependsOn: [],
    createdAt: new Date('2025-03-05T16:40:00.000Z'),
    updatedAt: new Date('2025-03-05T16:40:00.000Z'),
    deletedAt: null,
  }
);

export const MOCK_LINK_EXISTING_CHARGER_ACTION_SUCCESS: ActionEntity =
  new ActionEntity({
    ...MOCK_LINK_EXISTING_CHARGER_ACTION,
    status: ActionStatus.SUCCESS,
    data: {
      type: ActionType.LINK_EXISTING_CHARGER_V1,
      ppid: 'PSL-123456',
    },
  });

export const MOCK_SIGN_DOCUMENTS_ACTION_ENTITY: ActionEntity = new ActionEntity(
  {
    id: 'f7f1e0e0-1e6b-4e4b-8c7d-4b5f6f5b3b9f',
    subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
    owner: ActionOwner.SYSTEM,
    status: ActionStatus.PENDING,
    data: {
      type: ActionType.SIGN_DOCUMENTS_V1,
      documents: [],
    },
    dependsOn: [MOCK_SETUP_DIRECT_DEBIT_ACTION],
    createdAt: new Date('2025-03-05T16:40:00.000Z'),
    updatedAt: new Date('2025-03-05T16:40:00.000Z'),
    deletedAt: null,
  }
);

export const MOCK_SIGN_DOCUMENTS_ACTION_ENTITY_SUCCESS = new ActionEntity({
  ...MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
  status: ActionStatus.SUCCESS,
});

export const MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY: ActionEntity =
  new ActionEntity({
    id: '17e2ff20-881d-48a1-91c5-df2c6b2aadf7',
    subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
    owner: ActionOwner.SYSTEM,
    status: ActionStatus.PENDING,
    data: {
      type: ActionType.INSTALL_CHARGING_STATION_V1,
      ppid: null,
    },
    dependsOn: [MOCK_SIGN_DOCUMENTS_ACTION_ENTITY],
    createdAt: new Date('2025-03-05T16:40:00.000Z'),
    updatedAt: new Date('2025-03-05T16:40:00.000Z'),
    deletedAt: null,
  });

export const MOCK_INSTALL_CHARGING_STATION_ACTION_WITH_DATA_ENTITY: ActionEntity =
  new ActionEntity({
    ...MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
    data: {
      type: ActionType.INSTALL_CHARGING_STATION_V1,
      ppid: 'PSL-123456',
    },
  });

export const MOCK_SETUP_DIRECT_DEBIT_ACTION_WITH_DATA: ActionEntity =
  new ActionEntity({
    id: '12ce3c19-2e05-4726-9f08-2d6416efc21f',
    subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
    owner: ActionOwner.USER,
    status: ActionStatus.SUCCESS,
    data: {
      type: ActionType.SETUP_DIRECT_DEBIT_V1,
    },
    dependsOn: [],
    createdAt: new Date('2025-03-05T16:40:00.000Z'),
    updatedAt: new Date('2025-03-05T16:40:00.000Z'),
    deletedAt: null,
  });

export const MOCK_SETUP_DIRECT_DEBIT_ACTION_FAILED_WITH_DATA: ActionEntity =
  new ActionEntity({
    id: '12ce3c19-2e05-4726-9f08-2d6416efc21f',
    subscriptionId: MOCK_SUBSCRIPTION_ENTITY_ID,
    owner: ActionOwner.USER,
    status: ActionStatus.FAILURE,
    data: {
      type: ActionType.SETUP_DIRECT_DEBIT_V1,
    },
    dependsOn: [],
    createdAt: new Date('2025-03-05T16:40:00.000Z'),
    updatedAt: new Date('2025-03-05T16:40:00.000Z'),
    deletedAt: null,
  });

export const MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA: ActionEntity =
  new ActionEntity({
    ...MOCK_CHECK_AFFORDABILITY_ACTION,
    data: {
      type: ActionType.CHECK_AFFORDABILITY_V1,
      applicationId: MOCK_APPLICATION_ID,
      loanId: null,
    },
  });

export const MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_SUCCESS: ActionEntity =
  new ActionEntity({
    ...MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA,
    status: ActionStatus.SUCCESS,
  });

export const MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_FAILED: ActionEntity =
  new ActionEntity({
    ...MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA,
    status: ActionStatus.FAILURE,
  });

export const MOCK_SUBSCRIPTION_ENTITY = new SubscriptionEntity<
  SalesforceOrderEntity,
  PodDrivePlanDetails
>({
  id: MOCK_SUBSCRIPTION_ENTITY_ID,
  createdAt: new Date('2025-03-05T16:40:00.000Z'),
  updatedAt: new Date('2025-03-05T16:40:00.000Z'),
  deletedAt: null,
  activatedAt: null,
  status: SubscriptionStatus.PENDING,
  userId: 'c13f95c0-337d-4be7-a0d9-c300fd722a46',
  plan: MOCK_POD_DRIVE_PLAN_ENTITY,
  order: MOCK_ORDER_ENTITY,
});

export const MOCK_SCR_SUBSCRIPTION_ENTITY = new SubscriptionEntity<
  SelfServiceOrderEntity,
  PodDriveRewardsPlanDetails
>({
  ...MOCK_SUBSCRIPTION_ENTITY,
  plan: MOCK_POD_DRIVE_REWARDS_PLAN_ENTITY,
  order: {
    id: 'd4ce5a51-a6e6-4d2f-8433-d335b555330f',
    origin: OrderOrigin.POD_DRIVE_REWARDS_SELF_SERVE,
    orderedAt: new Date('2025-03-05T16:40:00.000Z'),
    ppid: 'PSL-123456',
  },
});

export const MOCK_CREATE_POD_DRIVE_PLAN_ENTITY =
  new UnpersistedPlanEntity<PodDrivePlanDetails>({
    details: MOCK_POD_DRIVE_PLAN_ENTITY.details,
  });

export const MOCK_CREATE_SURVEY_ACTION_ENTITY: UnpersistedActionEntity =
  new UnpersistedActionEntity({
    owner: ActionOwner.USER,
    status: ActionStatus.PENDING,
    data: {
      type: ActionType.COMPLETE_HOME_SURVEY_V1,
      surveyUrl: 'http://www.example.com',
    },
    subscriptionId: '',
    dependsOn: [],
  });

export const MOCK_CREATE_INSTALL_CHARGING_STATION_ENTITY: UnpersistedActionEntity =
  new UnpersistedActionEntity({
    owner: ActionOwner.SYSTEM,
    status: ActionStatus.PENDING,
    data: {
      type: ActionType.INSTALL_CHARGING_STATION_V1,
      ppid: null,
    },
    subscriptionId: '',
    dependsOn: [],
  });

export const MOCK_CREATE_SUBSCRIPTION_ENTITY =
  new UnpersistedSubscriptionEntity<SalesforceOrderEntity, PodDrivePlanDetails>(
    {
      plan: MOCK_CREATE_POD_DRIVE_PLAN_ENTITY,
      userId: MOCK_SUBSCRIPTION_ENTITY.userId,
      order: MOCK_ORDER_ENTITY,
      status: SubscriptionStatus.PENDING,
      activatedAt: null,
    }
  );

// Subscription Sorting
export const MOCK_ACTIVE_NEWEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: 'f4fe18a2-5283-47f8-9cfb-976d4fa4ee0b',
  status: SubscriptionStatus.ACTIVE,
  createdAt: new Date('2025-02-01T00:00:00.000Z'),
  updatedAt: new Date('2025-02-01T00:00:00.000Z'),
};

export const MOCK_ACTIVE_OLDEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: 'f8465fc0-bf96-4d68-97da-94d648e88d34',
  status: SubscriptionStatus.ACTIVE,
  createdAt: new Date('2025-01-01T00:00:00.000Z'),
  updatedAt: new Date('2025-01-01T00:00:00.000Z'),
};

export const MOCK_PENDING_NEWEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: '3eb02645-ed5d-4e61-b6bb-184cc8e33f01',
  status: SubscriptionStatus.PENDING,
  createdAt: new Date('2025-02-01T00:00:00.000Z'),
  updatedAt: new Date('2025-02-01T00:00:00.000Z'),
};

export const MOCK_PENDING_OLDEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: '0838ee04-5d22-4cc7-bd63-c1913027a0dd',
  status: SubscriptionStatus.PENDING,
  createdAt: new Date('2025-01-01T00:00:00.000Z'),
  updatedAt: new Date('2025-01-01T00:00:00.000Z'),
};

export const MOCK_SUSPENDED_NEWEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: 'b6b1ca20-bd85-4640-bf4d-19632668b42f',
  status: SubscriptionStatus.SUSPENDED,
  createdAt: new Date('2025-02-01T00:00:00.000Z'),
  updatedAt: new Date('2025-02-01T:0:00:00.000Z'),
};

export const MOCK_SUSPENDED_OLDEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: 'dfc629d4-f5ce-41d7-a521-6443f99b03f1',
  status: SubscriptionStatus.SUSPENDED,
  createdAt: new Date('2025-01-01T00:00:00.000Z'),
  updatedAt: new Date('2025-01-01T00:00:00.000Z'),
};

export const MOCK_ENDED_NEWEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: 'fcf18f29-51d4-4a8a-9f91-d50876f3041e',
  status: SubscriptionStatus.ENDED,
  createdAt: new Date('2025-02-01T00:00:00.000Z'),
  updatedAt: new Date('2025-02-01T00:00:00.000Z'),
};

export const MOCK_ENDED_OLDEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: '5872f679-a201-4172-b7e8-e6e7fc79a420',
  status: SubscriptionStatus.ENDED,
  createdAt: new Date('2025-01-01T00:00:00.000Z'),
  updatedAt: new Date('2025-01-01T00:00:00.000Z'),
};

export const MOCK_CANCELLED_NEWEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: '680d1196-9ece-4ddc-89cb-0ed835dab27c',
  status: SubscriptionStatus.CANCELLED,
  createdAt: new Date('2025-02-01T00:00:00.000Z'),
  updatedAt: new Date('2025-02-01T00:00:00.000Z'),
};

export const MOCK_CANCELLED_OLDEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: 'dee2bbd5-369a-4d20-9444-65645b121864',
  status: SubscriptionStatus.CANCELLED,
  createdAt: new Date('2025-01-01T00:00:00.000Z'),
  updatedAt: new Date('2025-01-01T00:00:00.000Z'),
};

export const MOCK_REJECTED_NEWEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: '680d1196-9ece-4ddc-89cb-0ed835dab27c',
  status: SubscriptionStatus.REJECTED,
  createdAt: new Date('2025-02-01T00:00:00.000Z'),
  updatedAt: new Date('2025-02-01T00:00:00.000Z'),
};

export const MOCK_REJECTED_OLDEST_SUBSCRIPTION = {
  ...MOCK_SUBSCRIPTION_ENTITY,
  id: 'dee2bbd5-369a-4d20-9444-65645b121864',
  status: SubscriptionStatus.REJECTED,
  createdAt: new Date('2025-01-01T00:00:00.000Z'),
  updatedAt: new Date('2025-01-01T00:00:00.000Z'),
};
