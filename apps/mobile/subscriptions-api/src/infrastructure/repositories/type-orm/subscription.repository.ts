import {
  ActionEntity,
  ActionStatus,
  ActionType,
} from '../../../domain/entities/action.entity';
import {
  BaseOrderEntity,
  OrderOrigin,
  SalesforceOrderEntity,
  SelfServiceOrderEntity,
} from '../../../domain/entities/order.entity';
import { BaseTypeOrmRepository } from '@experience/mobile/nest/typeorm-transactions';
import { ClsService } from 'nestjs-cls';
import {
  CouldNotDeleteEntityError,
  CouldNotFindEntityError,
  CouldNotPersistEntityError,
  FatalRepositoryError,
} from '@experience/mobile/clean-architecture';
import {
  ENTITY_MANAGER,
  Order,
  Subscription,
  SubscriptionPlan,
} from '@experience/mobile/driver-account/database';
import { EntityManager, JsonContains } from 'typeorm';
import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  PlanAllowancePeriod,
  PlanDetails,
  PlanEntity,
  PlanType,
  PodDrivePlanDetails,
  PodDriveRewardsPlanDetails,
} from '../../../domain/entities/plan.entity';
import {
  SubscriptionEntity,
  SubscriptionStatus,
  UnpersistedSubscriptionEntity,
} from '../../../domain/entities/subscription.entity';
import { SubscriptionRepositoryInterface } from '../../../domain/repositories/subscription.repository.interface';
import { assertEnumAssignable } from '../repository.utility';
import { undefined, z } from 'zod';

const PodDrivePlanDetailsSchema = z
  .object({
    productCode: z.string(),
    allowanceMiles: z.number(),
    allowancePeriod: z.nativeEnum(PlanAllowancePeriod),
    upfrontFeePounds: z.number(),
    monthlyFeePounds: z.number(),
    contractDurationMonths: z.number(),
    ratePencePerMile: z.number(),
    rateMilesPerKwh: z.number(),
  })
  .loose();

@Injectable()
export class TypeOrmSubscriptionRepository
  extends BaseTypeOrmRepository
  implements SubscriptionRepositoryInterface
{
  private readonly logger = new Logger(TypeOrmSubscriptionRepository.name);

  constructor(
    @Inject(ENTITY_MANAGER)
    entityManager: EntityManager,
    cls: ClsService
  ) {
    super(entityManager, cls);
  }

  async getByUserId<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(params: {
    userId: string;
  }): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails>[]> {
    try {
      const subscriptions = await this.manager.find(Subscription, {
        where: { userId: params.userId },
        relations: ['plans'],
      });

      return subscriptions.map((subscription) =>
        this.toDomainEntity(subscription)
      );
    } catch (error) {
      this.logger.error(
        {
          error: error instanceof Error ? error.message : error,
          stack: error instanceof Error ? error.stack : undefined,
          userId: params.userId,
        },
        'Failed to getByUserId for subscriptions in the DB'
      );

      throw new FatalRepositoryError(
        `Failed to getByUserId for subscriptions: ${
          error instanceof Error ? error.message : 'Unknown Error'
        }`
      );
    }
  }

  async getByPpid<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(params: {
    ppid: string;
  }): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails>[]> {
    try {
      const subscriptions = await this.manager.find(Subscription, {
        relations: ['actions', 'plans'],
        where: {
          actions: {
            type: ActionType.INSTALL_CHARGING_STATION_V1,
            status: ActionStatus.SUCCESS,
            data: JsonContains({
              ppid: params.ppid,
            }),
          },
        },
      });

      return subscriptions.map((subscription) =>
        this.toDomainEntity(subscription)
      );
    } catch (error) {
      this.logger.error(
        {
          error: error instanceof Error ? error.message : error,
          stack: error instanceof Error ? error.stack : undefined,
          userId: params.ppid,
        },
        'Failed to getByPpid for subscriptions in the DB'
      );

      throw new FatalRepositoryError(
        `Failed to getByPpid for subscriptions: ${
          error instanceof Error ? error.message : 'Unknown Error'
        }`
      );
    }
  }

  /**
   * @throws FatalRepositoryError,CouldNotPersistEntityError
   */
  async create(
    entity: UnpersistedSubscriptionEntity<BaseOrderEntity, PlanDetails>
  ): Promise<SubscriptionEntity<BaseOrderEntity, PlanDetails>> {
    try {
      // TODO: replace with transformer
      const { type: planType, ...planDetailsWithRenewal } = entity.plan.details;
      const { milesRenewalDate, ...planDetails } = planDetailsWithRenewal;

      const unpersistedSubscription = this.manager.create(Subscription, {
        userId: entity.userId,
        status: entity.status,
        order: entity.order,
        plans: [
          {
            type: planType,
            details: planDetails,
          },
        ],
      });

      const createdSubscription = await this.manager.save(
        Subscription,
        unpersistedSubscription,
        { reload: true }
      );

      return this.toDomainEntity(createdSubscription);
    } catch (error) {
      if (error instanceof FatalRepositoryError) {
        throw error;
      }

      this.logger.error(
        {
          error,
        },
        'Failed to create'
      );

      throw new CouldNotPersistEntityError(
        entity,
        'Failed to create due to ORM error'
      );
    }
  }

  /**
   * @throws FatalRepositoryError
   */
  async read(
    id: string
  ): Promise<SubscriptionEntity<BaseOrderEntity, PlanDetails> | null> {
    try {
      const maybeSubscription = await this.manager.findOne(Subscription, {
        where: { id },
        relations: ['plans'],
      });

      if (!maybeSubscription) {
        return null;
      }

      return this.toDomainEntity(maybeSubscription);
    } catch (error) {
      if (error instanceof FatalRepositoryError) {
        throw error;
      }

      this.logger.error(
        {
          error,
        },
        'Failed to read'
      );

      throw new FatalRepositoryError('Failed to read due to ORM error');
    }
  }

  /**
   * @throws CouldNotDeleteEntityError
   */
  async delete(id: string): Promise<void> {
    try {
      await this.manager.softDelete(Subscription, { id });
    } catch (error) {
      this.logger.error(
        {
          error,
        },
        'Failed to delete'
      );

      throw new CouldNotDeleteEntityError(
        { id },
        'Could not delete Subscription due to ORM error'
      );
    }
  }

  private constructRelevantPlanEntity(
    typeOrmPlan: SubscriptionPlan,
    subscriptionId: string
  ): PlanEntity<PodDrivePlanDetails | PodDriveRewardsPlanDetails> {
    this.validatePlanDetails(typeOrmPlan.details);

    switch (typeOrmPlan.type) {
      case PlanType.POD_DRIVE:
        return new PlanEntity({
          id: typeOrmPlan.id,
          subscriptionId,
          createdAt: typeOrmPlan.createdAt,
          updatedAt: typeOrmPlan.updatedAt,
          deletedAt: typeOrmPlan.deletedAt,
          details: {
            type: PlanType.POD_DRIVE,
            productCode: typeOrmPlan.details.productCode,
            allowanceMiles: typeOrmPlan.details.allowanceMiles,
            allowancePeriod: typeOrmPlan.details.allowancePeriod,
            upfrontFeePounds: typeOrmPlan.details.upfrontFeePounds,
            monthlyFeePounds: typeOrmPlan.details.monthlyFeePounds,
            contractDurationMonths: typeOrmPlan.details.contractDurationMonths,
            ratePencePerMile: typeOrmPlan.details.ratePencePerMile,
            rateMilesPerKwh: typeOrmPlan.details.rateMilesPerKwh,
            milesRenewalDate: null,
          },
        });
      case PlanType.POD_DRIVE_REWARDS:
        return new PlanEntity<PodDriveRewardsPlanDetails>({
          id: typeOrmPlan.id,
          subscriptionId,
          createdAt: typeOrmPlan.createdAt,
          updatedAt: typeOrmPlan.updatedAt,
          deletedAt: typeOrmPlan.deletedAt,
          details: {
            type: PlanType.POD_DRIVE_REWARDS,
            allowanceMiles: typeOrmPlan.details.allowanceMiles,
            allowancePeriod: typeOrmPlan.details.allowancePeriod,
            ratePencePerMile: typeOrmPlan.details.ratePencePerMile,
            rateMilesPerKwh: typeOrmPlan.details.rateMilesPerKwh,
            milesRenewalDate: null,
          },
        });
    }

    throw new FatalRepositoryError(
      'Invalid plan type. Unable to construct relevant plan entity.'
    );
  }

  private constructRelevantOrderEntity(
    typeOrmOrder: Order
  ): SalesforceOrderEntity | SelfServiceOrderEntity {
    switch (typeOrmOrder.origin) {
      case OrderOrigin.SALESFORCE:
      case OrderOrigin.SALESFORCE_TEST:
        return new SalesforceOrderEntity({
          id: typeOrmOrder.id,
          origin: typeOrmOrder.origin,
          orderedAt: typeOrmOrder.orderedAt,
          address: typeOrmOrder.address,
          email: typeOrmOrder.email,
          phoneNumber: typeOrmOrder.phoneNumber,
          firstName: typeOrmOrder.firstName,
          lastName: typeOrmOrder.lastName,
          mpan: typeOrmOrder.mpan,
          eCommerceId: typeOrmOrder.eCommerceId,
        });
      case OrderOrigin.POD_DRIVE_REWARDS_SELF_SERVE:
        return new SelfServiceOrderEntity({
          id: typeOrmOrder.id,
          origin: typeOrmOrder.origin,
          orderedAt: typeOrmOrder.orderedAt,
          ppid: typeOrmOrder.ppid,
        });
    }

    throw new FatalRepositoryError(
      'Invalid order origin. Unable to construct relevant order entity.'
    );
  }
  /**
   * @throws FatalRepositoryError if data fails checks
   */
  private toDomainEntity<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    typeOrmSubscription: Subscription
  ): SubscriptionEntity<TOrderEntity, TPlanDetails> {
    // TODO: We probably need to more clever here about identifying the relevant plan
    const [typeOrmPlan] = typeOrmSubscription.plans;

    assertEnumAssignable(typeOrmSubscription.status, SubscriptionStatus);
    assertEnumAssignable(typeOrmSubscription.order.origin, OrderOrigin);
    assertEnumAssignable(typeOrmPlan.type, PlanType);
    assertEnumAssignable(typeOrmSubscription.order.origin, OrderOrigin);

    const plan = this.constructRelevantPlanEntity(
      typeOrmPlan,
      typeOrmSubscription.id
    ) as PlanEntity<TPlanDetails>;

    const order = this.constructRelevantOrderEntity(
      typeOrmSubscription.order
    ) as unknown as TOrderEntity;

    return new SubscriptionEntity<TOrderEntity, TPlanDetails>({
      id: typeOrmSubscription.id,
      userId: typeOrmSubscription.userId,
      status: typeOrmSubscription.status,
      order,
      plan,
      activatedAt: typeOrmSubscription.activatedAt,
      createdAt: typeOrmSubscription.createdAt,
      updatedAt: typeOrmSubscription.updatedAt,
      deletedAt: typeOrmSubscription.deletedAt,
    });
  }

  private validatePlanDetails(
    maybeDetails: unknown
  ): asserts maybeDetails is z.infer<typeof PodDrivePlanDetailsSchema> {
    const result = PodDrivePlanDetailsSchema.safeParse(maybeDetails);

    if (!result.success) {
      throw new FatalRepositoryError('Plan details failed validation');
    }
  }

  async getSubscriptionByOrderId(
    orderId: string
  ): Promise<SubscriptionEntity<
    SalesforceOrderEntity,
    PodDrivePlanDetails
  > | null> {
    try {
      const entity = await this.manager.findOne(Subscription, {
        where: {
          order: JsonContains({
            id: orderId,
          }),
        },
        relations: ['plans', 'actions'],
      });

      if (!entity) {
        this.logger.warn(
          { orderId },
          'could not find subscription with matching orderId'
        );

        return null;
      }

      return this.toDomainEntity(entity);
    } catch (error) {
      this.logger.error(
        { error, orderId },
        'failed to get subscription by orderId'
      );

      throw new FatalRepositoryError();
    }
  }

  async getPendingSubscriptionByOrderId(
    orderId: string
  ): Promise<SubscriptionEntity<
    SalesforceOrderEntity,
    PodDrivePlanDetails
  > | null> {
    try {
      const entity = await this.manager.findOne(Subscription, {
        where: {
          order: JsonContains({
            id: orderId,
          }),
          status: ActionStatus.PENDING,
        },
        relations: ['plans', 'actions'],
      });

      if (!entity) {
        this.logger.warn(
          { orderId },
          'could not find pending subscription with matching orderId'
        );

        return null;
      }

      return this.toDomainEntity(entity);
    } catch (error) {
      this.logger.error(
        { error, orderId },
        'failed to get pending subscription by orderId'
      );

      throw new FatalRepositoryError();
    }
  }

  async activateSubscription<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    subscriptionId: string
  ): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails>> {
    try {
      this.logger.log({ subscriptionId }, 'activating subscription');

      const subscription = await this.manager.findOne(Subscription, {
        where: {
          id: subscriptionId,
        },
        relations: ['plans', 'actions'],
      });

      if (!subscription) {
        this.logger.error({ subscriptionId }, 'unable to find subscription');

        throw new FatalRepositoryError('unable to retrieve subscription');
      }

      subscription.status = SubscriptionStatus.ACTIVE;
      subscription.activatedAt = new Date(Date.now());

      const updated = await this.manager.save(Subscription, subscription);

      return this.toDomainEntity(updated);
    } catch (error) {
      this.logger.error(
        { subscriptionId, error },
        'unable to activate subscription'
      );

      throw new FatalRepositoryError(`unable to activate subscription`);
    }
  }

  async updateSubscriptionStatus<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    subscriptionId: string,
    status: SubscriptionStatus
  ): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails>> {
    try {
      this.logger.log(
        { subscriptionId, status },
        'updating status of subscription'
      );

      const subscription = await this.manager.findOne(Subscription, {
        where: {
          id: subscriptionId,
        },
        relations: ['plans', 'actions'],
      });

      if (!subscription) {
        this.logger.error(
          { subscriptionId, status },
          'unable to find subscription'
        );

        throw new FatalRepositoryError('unable to retrieve subscription');
      }

      subscription.status = status;

      const updated = await this.manager.save(Subscription, subscription);

      return this.toDomainEntity(updated);
    } catch (error) {
      this.logger.error(
        { subscriptionId, status, error },
        'unable to update subscription status'
      );

      throw new FatalRepositoryError('unable to update subscription status');
    }
  }

  async getByActionId<
    TOrderEntity extends BaseOrderEntity,
    TPlanDetails extends PlanDetails
  >(
    actionId: ActionEntity['id']
  ): Promise<SubscriptionEntity<TOrderEntity, TPlanDetails> | null> {
    try {
      const subscription = await this.manager.findOne(Subscription, {
        where: {
          actions: {
            id: actionId,
          },
        },
        relations: ['actions', 'plans'],
      });

      if (!subscription) {
        return null;
      }

      return this.toDomainEntity(subscription);
    } catch (error) {
      this.logger.error(
        {
          error: error instanceof Error ? error.message : error,
          stack: error instanceof Error ? error.stack : undefined,
          actionId,
        },
        'Failed to getByActionId for subscriptions in the DB'
      );

      throw new FatalRepositoryError(
        `Failed to getByActionId for subscriptions: ${
          error instanceof Error ? error.message : 'Unknown Error'
        }`
      );
    }
  }

  async cancelAndDeleteSubscription(
    subscriptionId: SubscriptionEntity<BaseOrderEntity, PlanDetails>['id']
  ) {
    try {
      await this.manager.transaction(async (manager) => {
        const subscription = await manager.findOne(Subscription, {
          where: { id: subscriptionId },
          relations: ['plans', 'actions'],
        });

        if (!subscription) {
          throw new CouldNotFindEntityError(
            {
              id: subscriptionId,
            },
            'could not find subscription by id'
          );
        }

        await manager.update(
          Subscription,
          {
            id: subscriptionId,
          },
          {
            status: SubscriptionStatus.CANCELLED,
          }
        );

        await manager.softRemove(subscription);
      });

      this.logger.log(
        { subscriptionId },
        'successfully cancelled and deleted subscription'
      );
    } catch (error) {
      if (error instanceof CouldNotFindEntityError) {
        throw error;
      }

      this.logger.error(
        {
          error: error instanceof Error ? error.message : error,
          stack: error instanceof Error ? error.stack : undefined,
          subscriptionId,
        },
        'Failed to cancel and delete for subscriptions'
      );

      throw new CouldNotDeleteEntityError(
        { id: subscriptionId },
        'Could not delete Subscription'
      );
    }
  }

  async cancelPendingSCRSelfServiceSubscriptions(ppid: string): Promise<void> {
    await this.manager.update(
      SubscriptionEntity,
      {
        status: SubscriptionStatus.PENDING,
        order: {
          ppid,
          origin: OrderOrigin.POD_DRIVE_REWARDS_SELF_SERVE,
        },
      },
      {
        status: SubscriptionStatus.CANCELLED,
      }
    );
  }
}
