import {
  <PERSON>Entity,
  ActionOwner,
  ActionStatus,
  ActionType,
  UnpersistedActionEntity,
} from '../../../domain/entities/action.entity';
import { ActionRepositoryInterface } from '../../../domain/repositories/action.repository.interface';
import { BaseTypeOrmRepository } from '@experience/mobile/nest/typeorm-transactions';
import { ClsService } from 'nestjs-cls';
import {
  CouldNotPersistEntityError,
  FatalRepositoryError,
} from '@experience/mobile/clean-architecture';
import {
  ENTITY_MANAGER,
  SubscriptionAction,
} from '@experience/mobile/driver-account/database';
import { EntityManager, JsonContains } from 'typeorm';
import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  assertEnumAssignable,
  getRecordArrayValue,
  getRecordNumberValue,
  getRecordStringValue,
} from '../repository.utility';

@Injectable()
export class TypeOrmActionRepository
  extends BaseTypeOrmRepository
  implements ActionRepositoryInterface
{
  private readonly logger = new Logger(TypeOrmActionRepository.name);

  constructor(
    @Inject(ENTITY_MANAGER)
    entityManager: EntityManager,
    cls: ClsService
  ) {
    super(entityManager, cls);
  }

  async findBySubscriptionId(id: string): Promise<ActionEntity[]> {
    try {
      const actions = await this.manager.find(SubscriptionAction, {
        where: {
          subscription: { id },
        },
        relations: ['subscription'],
      });

      return actions.map((action) => this.toDomainEntity(action));
    } catch (error) {
      if (error instanceof FatalRepositoryError) {
        throw error;
      }
      this.logger.error(
        {
          error,
        },
        'Failed to findBySubscriptionId'
      );

      throw new FatalRepositoryError(
        'Failed to findBySubscriptionId due to ORM error'
      );
    }
  }

  async findByApplicationId(
    type: ActionType,
    id: number
  ): Promise<ActionEntity[]> {
    try {
      const actions = await this.manager.find(SubscriptionAction, {
        where: {
          type,
          data: JsonContains({
            applicationId: id,
          }),
        },
        relations: ['subscription'],
      });

      return actions.map((action) => this.toDomainEntity(action));
    } catch (error) {
      if (error instanceof FatalRepositoryError) {
        throw error;
      }
      this.logger.error(
        {
          error,
        },
        'Failed to findByApplicationId'
      );

      throw new FatalRepositoryError(
        'Failed to findByApplicationId due to ORM error'
      );
    }
  }

  async create(protoEntity: UnpersistedActionEntity): Promise<ActionEntity> {
    this.logger.log({ protoEntity }, 'creating action');

    const { type, ...data } = protoEntity.data;

    try {
      const unsavedAction = this.manager.create(SubscriptionAction, {
        subscription: {
          id: protoEntity.subscriptionId,
        },
        data: {
          ...data,
        },
        type: protoEntity.data.type,
        owner: protoEntity.owner,
        status: protoEntity.status,
        dependsOn: protoEntity.dependsOn.map(({ id }) => id),
      });

      const savedAction = await this.manager.save(
        SubscriptionAction,
        unsavedAction
      );

      return this.toDomainEntity(savedAction);
    } catch (error) {
      if (error instanceof FatalRepositoryError) {
        throw error;
      }

      this.logger.error(
        {
          error,
        },
        'Failed to create'
      );

      throw new CouldNotPersistEntityError(
        protoEntity,
        'Failed to create due to ORM error'
      );
    }
  }

  async update(
    entity: ActionEntity,
    fields: Partial<ActionEntity>
  ): Promise<ActionEntity> {
    try {
      const { dependsOn, data, ...assignableFields } = fields;
      const { type: _, ...entityData } = entity.data;
      const { type: __, ...updateData } = fields.data || {};

      const entityUpdate: Partial<SubscriptionAction> = {
        ...assignableFields,
        ...(fields.data && { data: { ...entityData, ...updateData } }),
        ...(fields.dependsOn && {
          dependsOn: fields.dependsOn.map((dependsOn) => dependsOn.id),
        }),
      };

      await this.manager.update(SubscriptionAction, entity.id, entityUpdate);

      return new ActionEntity({ ...entity, ...fields });
    } catch (error) {
      this.logger.error({ entity, fields, error }, 'failed to update entity');

      throw new FatalRepositoryError(
        'Failed to update entity due to ORM error'
      );
    }
  }

  /**
   * @throws FatalRepositoryError
   */
  async read(id: ActionEntity['id']): Promise<ActionEntity | null> {
    try {
      const action = await this.manager.findOne(SubscriptionAction, {
        where: { id },
        relations: ['subscription'],
      });

      if (!action) {
        return null;
      }

      return this.toDomainEntity(action);
    } catch (error) {
      if (error instanceof FatalRepositoryError) {
        throw error;
      }

      this.logger.error(
        {
          error,
        },
        'Failed to read'
      );

      throw new FatalRepositoryError('Failed to read due to ORM error');
    }
  }

  private toDomainEntity(model: SubscriptionAction): ActionEntity {
    assertEnumAssignable(model.owner, ActionOwner);
    assertEnumAssignable(model.status, ActionStatus);
    assertEnumAssignable(model.type, ActionType);

    const baseEntity: Omit<ActionEntity, 'data'> = {
      id: model.id,
      subscriptionId: model.subscription.id,
      owner: model.owner,
      status: model.status,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      deletedAt: model.deletedAt,
      dependsOn: model.dependsOn.map((id) => ({ id })),
    };

    switch (model.type) {
      case ActionType.PAY_UPFRONT_FEE_V1:
        return new ActionEntity({
          ...baseEntity,
          data: {
            type: model.type,
          },
        });
      case ActionType.COMPLETE_HOME_SURVEY_V1:
        return new ActionEntity({
          ...baseEntity,
          data: {
            type: model.type,
            surveyUrl: getRecordStringValue(model.data, 'surveyUrl'),
          },
        });
      case ActionType.INSTALL_CHARGING_STATION_V1:
        return new ActionEntity({
          ...baseEntity,
          data: {
            type: model.type,
            ppid: getRecordStringValue(model.data, 'ppid', null),
          },
        });
      case ActionType.CHECK_AFFORDABILITY_V1:
        return new ActionEntity({
          ...baseEntity,
          data: {
            type: model.type,
            applicationId: getRecordNumberValue(
              model.data,
              'applicationId',
              null
            ),
            loanId: getRecordNumberValue(model.data, 'loanId', null),
          },
        });
      case ActionType.SIGN_DOCUMENTS_V1:
        return new ActionEntity({
          ...baseEntity,
          data: {
            type: model.type,
            documents: getRecordArrayValue(model.data, 'documents', []),
          },
        });
      case ActionType.SETUP_DIRECT_DEBIT_V1:
        return new ActionEntity({
          ...baseEntity,
          data: {
            type: model.type,
          },
        });
      default:
        throw new FatalRepositoryError('unsupported action type');
    }
  }
}
