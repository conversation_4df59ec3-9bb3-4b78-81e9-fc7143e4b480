import { Api<PERSON>roperty, getSchemaPath } from '@nestjs/swagger';
import {
  BaseOrderDTO,
  SalesforceOrderDTO,
  SelfServiceOrderDTO,
} from './order.dto';
import {
  BaseOrderEntity,
  OrderOrigin,
} from '../../../domain/entities/order.entity';
import {
  CreatePodDrivePlanDTO,
  CreatePodDriveRewardsPlanDTO,
} from './plan.dto';
import { CreatePodDrivePlanDTOToEntityTransformer } from '../transformers/create-pod-drive-plan-dto-to-entity.transformer';
import { CreatePodDriveRewardsPlanDTOToEntityTransformer } from '../transformers/create-pod-drive-rewards-plan-dto-to-entity.transformer';
import { CreateSubscriptionDTOToEntityTransformer } from '../transformers/create-subscription-dto-to-entity.transformer';
import {
  IsNotEmpty,
  IsObject,
  IsString,
  ValidateNested,
} from 'class-validator';
import { PlanDetails, PlanType } from '../../../domain/entities/plan.entity';
import { Type } from 'class-transformer';
import { UnpersistedSubscriptionEntity } from '../../../domain/entities/subscription.entity';

export class CreateSubscriptionDTO {
  @ApiProperty({
    name: 'userId',
    description: 'firebase user id',
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    name: 'plan',
    description:
      'The plan information for the given subscription - currently only PodDrive',
    oneOf: [
      { $ref: getSchemaPath(CreatePodDrivePlanDTO) },
      { $ref: getSchemaPath(CreatePodDriveRewardsPlanDTO) },
    ],
  })
  plan: CreatePodDrivePlanDTO | CreatePodDriveRewardsPlanDTO;

  @ApiProperty({
    name: 'order',
    description: 'order associated with the subscription',
    oneOf: [
      { $ref: getSchemaPath(SalesforceOrderDTO) },
      { $ref: getSchemaPath(SelfServiceOrderDTO) },
    ],
    required: true,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => BaseOrderDTO, {
    keepDiscriminatorProperty: true,
    discriminator: {
      property: 'origin',
      subTypes: [
        {
          name: OrderOrigin.SALESFORCE_TEST,
          value: SalesforceOrderDTO,
        },
        {
          name: OrderOrigin.SALESFORCE,
          value: SalesforceOrderDTO,
        },
        {
          name: OrderOrigin.POD_DRIVE_REWARDS_SELF_SERVE,
          value: SelfServiceOrderDTO,
        },
      ],
    },
  })
  order: SalesforceOrderDTO | SelfServiceOrderDTO;

  private getPlanTransformer() {
    switch (this.plan.type) {
      case PlanType.POD_DRIVE:
        return new CreatePodDrivePlanDTOToEntityTransformer();
      case PlanType.POD_DRIVE_REWARDS:
        return new CreatePodDriveRewardsPlanDTOToEntityTransformer();
    }
  }

  toEntity(): UnpersistedSubscriptionEntity<BaseOrderEntity, PlanDetails> {
    const transformer = new CreateSubscriptionDTOToEntityTransformer(
      this.getPlanTransformer()
    );

    return transformer.transform(this);
  }
}
