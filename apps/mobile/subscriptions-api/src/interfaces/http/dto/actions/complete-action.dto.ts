import { CheckAffordabilityActionDTO } from './check-affordability-action.dto';
import { HomeSurveyActionDTO } from './home-survey-action.dto';
import { InstallChargingStationActionDTO } from './install-charging-station-action.dto';
import { PayUpfrontFeeActionDTO } from './pay-upfront-fee-action.dto';
import { SetupDirectDebitActionDTO } from './setup-direct-debit-action.dto';
import { SignDocumentsActionDTO } from './sign-documents-action.dto';
import { SignRewardsTOSActionDTO } from './sign-rewards-tos-action.dto';

export type CompleteActionDTO =
  | PayUpfrontFeeActionDTO
  | SetupDirectDebitActionDTO
  | CheckAffordabilityActionDTO
  | InstallChargingStationActionDTO
  | HomeSurveyActionDTO
  | SignDocumentsActionDTO
  | SignRewardsTOSActionDTO;
