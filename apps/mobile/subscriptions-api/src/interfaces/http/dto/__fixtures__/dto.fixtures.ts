import {
  ActionOwner,
  ActionStatus,
  ActionType,
} from '../../../../domain/entities/action.entity';
import {
  AffordabilityCheck,
  EmploymentStatus,
  MaritalStatus,
  NumberOfDependants,
  ResidentalStatus,
  Title,
} from '../../../../domain/types/action.types';
import { CheckAffordabilityActionDTO } from '../actions/check-affordability-action.dto';
import {
  CircumstancesRequireSupport,
  UpdateCheckAffordabilityDataDTO,
  UpdateHomeSurveyActionDTO,
  UpdateLinkExistingChargerActionDTO,
  UpdateSignRewardsTOSActionDTO,
  UpdateSignRewardsTOSActionDataDTO,
} from '../update.action.dto';
import { CreateSubscriptionDTO } from '../create-subscription.dto';
import { HomeSurveyActionDTO } from '../actions/home-survey-action.dto';
import { InstallChargingStationActionDTO } from '../actions/install-charging-station-action.dto';
import { InstallCompletedEventDto } from '../install-completed-event.dto';
import {
  LinkExistingChargerActionDTO,
  LinkExistingChargerActionDataDTO,
} from '../actions/link-existing-charger-action.dto';
import {
  MOCK_APPLICATION_ID,
  MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA,
  MOCK_LINK_EXISTING_CHARGER_ACTION,
  MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY,
  MOCK_SETUP_DIRECT_DEBIT_ACTION,
  MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
  MOCK_SIGN_REWARDS_TOS_ACTION,
  MOCK_SUBSCRIPTION_ENTITY,
} from '../../../../domain/entities/__fixtures__/entity.fixtures';
import { OrderEventDTO } from '../order-event.dto';
import { OrderOrigin } from '../../../../domain/entities/order.entity';
import { PersistedSubscriptionDTO } from '../persisted-subscription.dto';
import {
  PlanAllowancePeriod,
  PlanType,
} from '../../../../domain/entities/plan.entity';
import { PodDrivePlanDTO } from '../plan.dto';
import { SalesforceOrderDTO } from '../order.dto';
import {
  SignDocumentsActionDTO,
  SignDocumentsDataDTO,
} from '../actions/sign-documents-action.dto';
import {
  SignRewardsTOSActionDTO,
  SignRewardsTOSActionDataDTO,
} from '../actions/sign-rewards-tos-action.dto';
import { SubscriptionStatus } from '../../../../domain/entities/subscription.entity';
import { SurveyCompletedEventDTO } from '../survey-completed-event.dto';

export const MOCK_USER_EMAIL = '<EMAIL>';
export const MOCK_USER_ID = 'c13f95c0-337d-4be7-a0d9-c300fd722a46';
export const MOCK_PPID = 'PSL-123456';
export const MOCK_SUBSCRIPTION_ID = '78a28650-01c3-4fe4-9111-0c48ad227ef6';
export const MOCK_ORDER_ID = 'd4ce5a51-a6e6-4d2f-8433-d335b555330f';
export const MOCK_SURVEY_ACTION_ID = '28d6027c-ba71-4fe2-bcee-77bb6c274f73';
export const MOCK_INSTALL_CHARGING_STATION_ID =
  '17e2ff20-881d-48a1-91c5-df2c6b2aadf7';
export const MOCK_POD_DRIVE_PLAN_ID = '82a26821-1d34-4af2-9aed-4f08ffe45af3';

export const MOCK_POD_DRIVE_PLAN_DTO: PodDrivePlanDTO = Object.assign(
  new PodDrivePlanDTO(),
  {
    id: MOCK_POD_DRIVE_PLAN_ID,
    productCode: 'pod_drive_product_01',
    allowanceMiles: 10_000,
    allowancePeriod: PlanAllowancePeriod.ANNUAL,
    monthlyFeePounds: 35,
    upfrontFeePounds: 99,
    contractDurationMonths: 18,
    ratePencePerMile: 2.28,
    rateMilesPerKwh: 3.5,
    type: PlanType.POD_DRIVE,
  }
);

export const MOCK_ORDER_DTO: SalesforceOrderDTO = {
  address: {
    line1: "222 Gray's Inn Road",
    line2: null,
    line3: null,
    postcode: 'WC1X 8HB',
  },
  email: '<EMAIL>',
  firstName: 'Mobile',
  lastName: 'Tester',
  mpan: 'S 01 801 101 22 6130 5588 165',
  eCommerceId: 'dr3l2e8dhu::2415',
  phoneNumber: '02072474114',
  id: MOCK_ORDER_ID,
  orderedAt: new Date('2025-03-05T16:40:00.000Z'),
  origin: OrderOrigin.SALESFORCE,
};

export const MOCK_CREATE_SUBSCRIPTION_DTO: CreateSubscriptionDTO =
  Object.assign(new CreateSubscriptionDTO(), {
    userId: MOCK_USER_ID,
    plan: MOCK_POD_DRIVE_PLAN_DTO,
    order: MOCK_ORDER_DTO,
  });

export const MOCK_ORDER_EVENT_DTO: OrderEventDTO = Object.assign(
  new OrderEventDTO(),
  {
    email: MOCK_USER_EMAIL,
    plan: MOCK_POD_DRIVE_PLAN_DTO,
    order: MOCK_ORDER_DTO,
  }
);

export const MOCK_SURVEY_ACTION_DTO = Object.assign(new HomeSurveyActionDTO(), {
  id: MOCK_SURVEY_ACTION_ID,
  subscriptionId: MOCK_SUBSCRIPTION_ID,
  type: ActionType.COMPLETE_HOME_SURVEY_V1,
  owner: ActionOwner.USER,
  status: ActionStatus.PENDING,
  data: {
    surveyUrl: 'http://www.example.com',
  },
  dependsOn: [MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY.id],
});

export const MOCK_INSTALL_CHARGING_STATION_ACTION_DTO = Object.assign(
  new InstallChargingStationActionDTO(),
  {
    id: MOCK_INSTALL_CHARGING_STATION_ID,
    subscriptionId: MOCK_SUBSCRIPTION_ID,
    type: ActionType.INSTALL_CHARGING_STATION_V1,
    owner: ActionOwner.SYSTEM,
    status: ActionStatus.PENDING,
    dependsOn: [MOCK_SIGN_DOCUMENTS_ACTION_ENTITY.id],
    data: {
      ppid: null,
    },
  }
);

export const MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_DTO = Object.assign(
  new CheckAffordabilityActionDTO(),
  {
    id: MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA.id,
    subscriptionId: MOCK_SUBSCRIPTION_ID,
    type: ActionType.CHECK_AFFORDABILITY_V1,
    owner: ActionOwner.SYSTEM,
    status: ActionStatus.PENDING,
    dependsOn: [MOCK_SURVEY_ACTION_DTO.id],
    data: {
      applicationId: MOCK_APPLICATION_ID,
      loanId: null,
    },
  }
);

export const MOCK_PERSISTED_SUBSCRIPTION_DTO: PersistedSubscriptionDTO = {
  createdAt: new Date('2025-03-05T16:40:00.000Z'),
  updatedAt: new Date('2025-03-05T16:40:00.000Z'),
  deletedAt: null,
  activatedAt: null,
  id: MOCK_SUBSCRIPTION_ID,
  plan: MOCK_POD_DRIVE_PLAN_DTO,
  status: SubscriptionStatus.PENDING,
  userId: MOCK_USER_ID,
  order: MOCK_ORDER_DTO,
  actions: [MOCK_SURVEY_ACTION_DTO, MOCK_INSTALL_CHARGING_STATION_ACTION_DTO],
};

export const MOCK_INSTALLATION_COMPLETED_DTO: InstallCompletedEventDto = {
  eventId: '',
  metadata: {},
  publishedAt: '',
  routingKey: '',
  type: '',
  data: {
    chargingStation: {
      id: 'PSL-123456',
      sku: '',
    },
    order: {
      reference: MOCK_ORDER_DTO.id,
      type: '',
    },
    address: {
      id: '',
      line1: '222 Grays Inn Road',
      city: 'London',
      country: 'England',
      postcode: 'WC1X 8HB',
    },
    appointment: {
      reference: '',
    },
    installation: {
      completedAt: '',
      firmwareVersion: '',
      id: '',
    },
    installer: {
      authId: '',
    },
    location: {
      id: '',
      latitude: '',
      longitude: '',
    },
    mpan: '',
    useCase: '',
  },
};

export const MOCK_SURVEY_COMPLETED_DTO: SurveyCompletedEventDTO = {
  orderId: MOCK_ORDER_DTO.id,
};

export const MOCK_UPDATE_AFFORDABILITY_CHECK_ACTION_DTO = Object.assign(
  new CheckAffordabilityActionDTO(),
  {
    type: ActionType.CHECK_AFFORDABILITY_V1,
    data: Object.assign<UpdateCheckAffordabilityDataDTO, AffordabilityCheck>(
      new UpdateCheckAffordabilityDataDTO(),
      {
        title: Title.MR,
        firstName: 'john',
        lastName: 'smith',
        dateOfBirth: new Date(Date.now()),
        email: '<EMAIL>',
        phoneNumber: '+447234567891',
        employmentStatus: EmploymentStatus.FULL_TIME,
        maritalStatus: MaritalStatus.SINGLE,
        monthlyCreditPayments: 250,
        monthlyHouseholdExpenses: 250,
        monthlyHousePayments: 500,
        monthlyTakeHomePay: 1500,
        monthlyTravelAndLivingExpenses: 100,
        dependencies: NumberOfDependants.THREE_PLUS,
        residentialStatus: ResidentalStatus.LIVING_WITH_PARENTS,
        circumstancesRequireSupport:
          CircumstancesRequireSupport.SUPPORT_REQUESTED,
        billingAddress: {
          number: '222',
          street: "Gray's Inn Road",
          town: 'London',
          postcode: 'WC1X 8HB',
        },
      }
    ),
  }
);

export const MOCK_UPDATE_HOME_SURVEY_ACTION_DTO = Object.assign(
  new UpdateHomeSurveyActionDTO(),
  {
    type: ActionType.COMPLETE_HOME_SURVEY_V1,
    data: {},
  }
);

export const MOCK_SIGN_DOCUMENT_ACTION_DTO = Object.assign(
  new SignDocumentsActionDTO(),
  {
    id: MOCK_SIGN_DOCUMENTS_ACTION_ENTITY.id,
    subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
    type: ActionType.SIGN_DOCUMENTS_V1,
    owner: ActionOwner.SYSTEM,
    status: ActionStatus.SUCCESS,
    dependsOn: [MOCK_SETUP_DIRECT_DEBIT_ACTION.id],
    data: Object.assign(new SignDocumentsDataDTO(), {
      documents: [],
    }),
  }
);

export const MOCK_SIGN_REWARDS_TOS_ACTION_DTO = Object.assign(
  new SignRewardsTOSActionDTO(),
  {
    id: MOCK_SIGN_REWARDS_TOS_ACTION.id,
    subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
    type: ActionType.SIGN_REWARDS_TOS_V1,
    owner: ActionOwner.USER,
    status: ActionStatus.SUCCESS,
    dependsOn: [],
    data: Object.assign(new SignRewardsTOSActionDataDTO(), {
      revision: 'test',
    } satisfies SignRewardsTOSActionDataDTO),
  } satisfies SignRewardsTOSActionDTO
);

export const MOCK_UPDATE_SIGN_REWARDS_TOS_ACTION_DTO = Object.assign(
  new UpdateSignRewardsTOSActionDTO(),
  {
    type: ActionType.SIGN_REWARDS_TOS_V1,
    data: Object.assign(new UpdateSignRewardsTOSActionDataDTO(), {
      revision: 'test',
    } satisfies UpdateSignRewardsTOSActionDataDTO),
  } satisfies UpdateSignRewardsTOSActionDTO
);

export const MOCK_LINK_EXISTING_CHARGER_ACTION_DTO = Object.assign(
  new LinkExistingChargerActionDTO(),
  {
    id: MOCK_LINK_EXISTING_CHARGER_ACTION.id,
    subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
    type: ActionType.LINK_EXISTING_CHARGER_V1,
    owner: ActionOwner.USER,
    status: ActionStatus.SUCCESS,
    dependsOn: [],
    data: Object.assign(new LinkExistingChargerActionDataDTO(), {
      ppid: 'PSL-123456',
    } satisfies LinkExistingChargerActionDataDTO),
  } satisfies LinkExistingChargerActionDTO
);

export const MOCK_UPDATE_LINK_EXISTING_CHARGER_ACTION_DTO = Object.assign(
  new UpdateLinkExistingChargerActionDTO(),
  {
    type: ActionType.LINK_EXISTING_CHARGER_V1,
    data: {},
  } satisfies UpdateLinkExistingChargerActionDTO
);
