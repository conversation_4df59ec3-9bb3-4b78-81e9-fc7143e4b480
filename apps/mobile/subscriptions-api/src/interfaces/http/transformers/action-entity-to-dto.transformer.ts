import {
  ActionEntity,
  ActionType,
} from '../../../domain/entities/action.entity';
import { CheckAffordabilityActionDTO } from '../dto/actions/check-affordability-action.dto';
import { CompleteActionDTO } from '../dto/actions/complete-action.dto';
import { HomeSurveyActionDTO } from '../dto/actions/home-survey-action.dto';
import { InstallChargingStationActionDTO } from '../dto/actions/install-charging-station-action.dto';
import { PayUpfrontFeeActionDTO } from '../dto/actions/pay-upfront-fee-action.dto';
import { SetupDirectDebitActionDTO } from '../dto/actions/setup-direct-debit-action.dto';
import { SignDocumentsActionDTO } from '../dto/actions/sign-documents-action.dto';
import { Transformer } from '@experience/mobile/clean-architecture';

export class ActionEntityToDTOTransformer extends Transformer<
  ActionEntity,
  CompleteActionDTO
> {
  transform(input: ActionEntity): CompleteActionDTO {
    const { type } = input.data;

    switch (type) {
      case ActionType.PAY_UPFRONT_FEE_V1:
        return PayUpfrontFeeActionDTO.from(input);
      case ActionType.SETUP_DIRECT_DEBIT_V1:
        return SetupDirectDebitActionDTO.from(input);
      case ActionType.CHECK_AFFORDABILITY_V1:
        return CheckAffordabilityActionDTO.from(input);
      case ActionType.INSTALL_CHARGING_STATION_V1:
        return InstallChargingStationActionDTO.from(input);
      case ActionType.COMPLETE_HOME_SURVEY_V1:
        return HomeSurveyActionDTO.from(input);
      case ActionType.SIGN_DOCUMENTS_V1:
        return SignDocumentsActionDTO.from(input);
      default:
        throw new Error(`Unhandled action type: ${type}`);
    }
  }
}
