import { BaseOrderEntity } from '../../../domain/entities/order.entity';
import {
  CreatePodDrivePlanDTO,
  CreatePodDriveRewardsPlanDTO,
} from '../dto/plan.dto';
import { CreateSubscriptionDTO } from '../dto/create-subscription.dto';
import {
  PlanDetails,
  UnpersistedPlanEntity,
} from '../../../domain/entities/plan.entity';
import {
  SubscriptionStatus,
  UnpersistedSubscriptionEntity,
} from '../../../domain/entities/subscription.entity';
import { Transformer } from '@experience/mobile/clean-architecture';

export class CreateSubscriptionDTOToEntityTransformer extends Transformer<
  CreateSubscriptionDTO,
  UnpersistedSubscriptionEntity<BaseOrderEntity, PlanDetails>
> {
  constructor(
    // TODO: make this generic to other plans
    private readonly planTransformer: Transformer<
      CreatePodDrivePlanDTO | CreatePodDriveRewardsPlanDTO,
      UnpersistedPlanEntity<PlanDetails>
    >
  ) {
    super();
  }
  transform<TOrder extends BaseOrderEntity, TPlanDetails extends PlanDetails>(
    input: CreateSubscriptionDTO
  ): UnpersistedSubscriptionEntity<TOrder, TPlanDetails> {
    return new UnpersistedSubscriptionEntity<TOrder, TPlanDetails>({
      userId: input.userId,
      status: SubscriptionStatus.PENDING,
      plan: this.planTransformer.transform(input.plan),
      order: input.order,
      activatedAt: null,
    });
  }
}
