import {
  PlanDetails,
  PlanEntity,
  PlanType,
} from '../../../domain/entities/plan.entity';
import { PodDrivePlanDTO, PodDriveRewardsPlanDTO } from '../dto/plan.dto';
import { Transformer } from '@experience/mobile/clean-architecture';

export class PlanEntityToDTOTransformer extends Transformer<
  PlanEntity<PlanDetails>,
  PodDrivePlanDTO | PodDriveRewardsPlanDTO
> {
  transform(
    input: PlanEntity<PlanDetails>
  ): PodDrivePlanDTO | PodDriveRewardsPlanDTO {
    switch (input.details.type) {
      case PlanType.POD_DRIVE:
        return Object.assign(new PodDrivePlanDTO(), {
          id: input.id,
          type: input.details.type,
          allowanceMiles: input.details.allowanceMiles,
          productCode: input.details.productCode,
          allowancePeriod: input.details.allowancePeriod,
          upfrontFeePounds: input.details.upfrontFeePounds,
          monthlyFeePounds: input.details.monthlyFeePounds,
          contractDurationMonths: input.details.contractDurationMonths,
          ratePencePerMile: input.details.ratePencePerMile,
          rateMilesPerKwh: input.details.rateMilesPerKwh,
          milesRenewalDate:
            input.details.milesRenewalDate?.toISOString() ?? null,
        } satisfies PodDrivePlanDTO);
      case PlanType.POD_DRIVE_REWARDS:
        return Object.assign(new PodDriveRewardsPlanDTO(), {
          id: input.id,
          type: input.details.type,
          allowanceMiles: input.details.allowanceMiles,
          allowancePeriod: input.details.allowancePeriod,
          rateMilesPerKwh: input.details.rateMilesPerKwh,
          ratePencePerMile: input.details.ratePencePerMile,
          milesRenewalDate:
            input.details.milesRenewalDate?.toISOString() ?? null,
        } satisfies PodDriveRewardsPlanDTO);

      default:
        throw new Error('Unknown plan type. Unable to map entity to DTO.');
    }
  }
}
