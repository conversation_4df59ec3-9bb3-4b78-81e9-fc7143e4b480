import {
  ActionDocumentCodeType,
  ActionType,
} from '../../domain/entities/action.entity';
import { ActionService } from '../../application/services/action.service';
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  InternalServerErrorException,
  NotFoundException,
  StreamableFile,
} from '@nestjs/common';
import {
  CouldNotFindEntityError,
  CouldNotPersistEntityError,
  InvalidEntityError,
} from '@experience/mobile/clean-architecture';
import { IncompleteDependencyError } from '../../domain/errors/incomplete-dependency.error';
import { LoanManagementSystemError } from '../../domain/errors/loan-management-system.error';
import {
  MOCK_CHECK_AFFORDABILITY_ACTION,
  MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA,
  MOCK_LINK_EXISTING_CHARGER_ACTION_SUCCESS,
  MOCK_SCR_SUBSCRIPTION_ENTITY,
  MOCK_SIGN_DOCUMENTS_ACTION_ENTITY_SUCCESS,
  MOCK_SIGN_REWARDS_TOS_ACTION_SUCCESS,
  MOCK_SUBSCRIPTION_ENTITY,
  MOCK_SURVEY_ACTION_ENTITY,
} from '../../domain/entities/__fixtures__/entity.fixtures';
import {
  MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_DTO,
  MOCK_CREATE_POD_DRIVE_REWARDS_SUBSCRIPTION_DTO,
  MOCK_LINK_EXISTING_CHARGER_ACTION_DTO,
  MOCK_PPID,
  MOCK_SIGN_DOCUMENT_ACTION_DTO,
  MOCK_SIGN_REWARDS_TOS_ACTION_DTO,
  MOCK_SUBSCRIPTION_ID,
  MOCK_SURVEY_ACTION_DTO,
  MOCK_SURVEY_ACTION_ID,
  MOCK_UPDATE_AFFORDABILITY_CHECK_ACTION_DTO,
  MOCK_UPDATE_HOME_SURVEY_ACTION_DTO,
  MOCK_UPDATE_LINK_EXISTING_CHARGER_ACTION_DTO,
  MOCK_UPDATE_SIGN_REWARDS_TOS_ACTION_DTO,
  MOCK_USER_ID,
} from './dto/__fixtures__/dto.fixtures';
import { NoApplicationIdError } from '../../application/errors/no-application-id.error';
import { OrderOrigin } from '../../domain/entities/order.entity';
import { PPIDAlreadyOnSubscriptionError } from '../../application/errors/ppid-already-on-subscription.error';
import { PersistedSubscriptionWithActionsDTO } from '../../application/dto/persisted-subscription-with-actions.dto';
import { PlanAllowancePeriod } from '../../domain/entities/plan.entity';
import { Readable } from 'stream';
import { SubscriptionDirectDebit } from '../../domain/types/subscription.types';
import { SubscriptionNotActiveError } from '../../application/errors/subscription-not-active.error';
import { SubscriptionsController } from './subscriptions.controller';
import { SubscriptionsService } from '../../application/services/subscriptions.service';
import { UnknownDocumentError } from '../../application/errors/unknown-document.error';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { createMock } from '@golevelup/ts-jest';
import { v4 } from 'uuid';

describe(SubscriptionsController.name, () => {
  let mockedSubscriptionsService: jest.Mocked<SubscriptionsService>;
  let mockedActionService: jest.Mocked<ActionService>;
  let controller: SubscriptionsController;

  beforeEach(() => {
    mockedSubscriptionsService = createMock();
    mockedActionService = createMock();

    controller = new SubscriptionsController(
      mockedSubscriptionsService,
      mockedActionService
    );
  });

  describe(SubscriptionsController.prototype.search, () => {
    it('calls getByPpidAndOrUserId with the userId', async () => {
      mockedSubscriptionsService.getByPpidAndOrUserId.mockResolvedValueOnce([
        new PersistedSubscriptionWithActionsDTO({
          subscription: MOCK_SUBSCRIPTION_ENTITY,
          actions: [],
        }),
      ]);

      await controller.search({
        userId: MOCK_USER_ID,
      });

      expect(
        mockedSubscriptionsService.getByPpidAndOrUserId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionsService.getByPpidAndOrUserId
      ).toHaveBeenCalledWith({
        userId: MOCK_USER_ID,
      });
    });

    it('calls getByPpidAndOrUserId with the ppid', async () => {
      mockedSubscriptionsService.getByPpidAndOrUserId.mockResolvedValueOnce([
        new PersistedSubscriptionWithActionsDTO({
          subscription: MOCK_SUBSCRIPTION_ENTITY,
          actions: [],
        }),
      ]);

      await controller.search({
        ppid: MOCK_PPID,
      });

      expect(
        mockedSubscriptionsService.getByPpidAndOrUserId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionsService.getByPpidAndOrUserId
      ).toHaveBeenCalledWith({
        ppid: MOCK_PPID,
      });
    });
  });

  describe(SubscriptionsController.prototype.getBySubscriptionId, () => {
    it('calls the SubscriptionsService.readById with the subscriptionId', async () => {
      mockedSubscriptionsService.readById.mockResolvedValueOnce(
        new PersistedSubscriptionWithActionsDTO({
          subscription: MOCK_SUBSCRIPTION_ENTITY,
          actions: [],
        })
      );

      await controller.getBySubscriptionId({
        subscriptionId: MOCK_SUBSCRIPTION_ID,
      });

      expect(mockedSubscriptionsService.readById).toHaveBeenCalledTimes(1);
      expect(mockedSubscriptionsService.readById).toHaveBeenCalledWith(
        MOCK_SUBSCRIPTION_ID
      );
    });

    it('throws 404 if the SubscriptionsService.readById returns null', async () => {
      mockedSubscriptionsService.readById.mockResolvedValueOnce(null);

      await expect(
        controller.getBySubscriptionId({ subscriptionId: v4() })
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe(SubscriptionsController.prototype.getByActionId, () => {
    it('calls the SubscriptionsService.readById with the actionId', async () => {
      mockedActionService.readById.mockResolvedValueOnce(
        MOCK_SURVEY_ACTION_ENTITY
      );

      await controller.getByActionId({
        subscriptionId: MOCK_SUBSCRIPTION_ID,
        actionId: MOCK_SURVEY_ACTION_ID,
      });

      expect(mockedActionService.readById).toHaveBeenCalledTimes(1);
      expect(mockedActionService.readById).toHaveBeenCalledWith(
        MOCK_SURVEY_ACTION_ID
      );
    });

    it('throws a NotFoundException if the action is for a different subscription', async () => {
      mockedActionService.readById.mockResolvedValueOnce(
        MOCK_SURVEY_ACTION_ENTITY
      );

      await expect(
        controller.getByActionId({
          subscriptionId: 'df4a4a0c-8d59-4579-b5c2-1591a8aa1afb',
          actionId: MOCK_SURVEY_ACTION_ID,
        })
      ).rejects.toThrow(NotFoundException);
    });

    it('throws 404 if the SubscriptionsService.readById returns null', async () => {
      mockedActionService.readById.mockResolvedValueOnce(null);

      await expect(
        controller.getByActionId({ subscriptionId: v4(), actionId: v4() })
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe(SubscriptionsController.prototype.getSubscriptionDirectDebit, () => {
    it('retrieves direct debit details from the subscription service', async () => {
      const subscriptionDirectDirect: SubscriptionDirectDebit = {
        accountNumberLastDigits: '4567',
        sortCodeLastDigits: '80',
        nameOnAccount: 'Mr John Smith',
        monthlyPaymentDay: 28,
      };

      const id = v4();

      mockedSubscriptionsService.getSubscriptionDirectDebit.mockResolvedValueOnce(
        subscriptionDirectDirect
      );

      expect(await controller.getSubscriptionDirectDebit(id)).toEqual(
        subscriptionDirectDirect
      );
      expect(
        mockedSubscriptionsService.getSubscriptionDirectDebit
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionsService.getSubscriptionDirectDebit
      ).toHaveBeenCalledWith(id);
    });

    it('throws 404 if the subscription service throws a could not find entity exception', async () => {
      mockedSubscriptionsService.getSubscriptionDirectDebit.mockRejectedValue(
        new CouldNotFindEntityError({ id: 'test' }, 'oh oh')
      );

      await expect(controller.getSubscriptionDirectDebit(v4())).rejects.toThrow(
        NotFoundException
      );
    });

    it('throws 404 if the subscription service throws a no application id exception', async () => {
      mockedSubscriptionsService.getSubscriptionDirectDebit.mockRejectedValue(
        new NoApplicationIdError('no application id')
      );

      await expect(controller.getSubscriptionDirectDebit(v4())).rejects.toThrow(
        NotFoundException
      );
    });

    it('throws 404 if the subscription service throws a subscription not active exception', async () => {
      mockedSubscriptionsService.getSubscriptionDirectDebit.mockRejectedValue(
        new SubscriptionNotActiveError('subscription not active')
      );

      await expect(controller.getSubscriptionDirectDebit(v4())).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe(SubscriptionsController.prototype.getSubscriptionDocuments, () => {
    it.each([
      new SubscriptionNotActiveError(''),
      new CouldNotFindEntityError({}, ''),
      new NoApplicationIdError(''),
    ])('throws a NotFoundException if a %s is thrown', async (error) => {
      mockedSubscriptionsService.getSubscriptionDocuments.mockRejectedValue(
        error
      );

      await expect(() =>
        controller.getSubscriptionDocuments(MOCK_SUBSCRIPTION_ID)
      ).rejects.toThrow(NotFoundException);
    });

    it('throws an InternalServerErrorException if something unknown goes wrong', async () => {
      mockedSubscriptionsService.getSubscriptionDocuments.mockRejectedValue(
        new Error()
      );

      await expect(() =>
        controller.getSubscriptionDocuments(MOCK_SUBSCRIPTION_ID)
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('returns the documents related to the subscription', async () => {
      mockedSubscriptionsService.getSubscriptionDocuments.mockResolvedValue([
        {
          issued: '2025-05-06T11:34:00.000Z',
          link: `/subscriptions/${MOCK_SUBSCRIPTION_ID}/document/A0838D19-ED4H-1HD0-6F21-976J0ABCC627`,
          format: 'PDF',
          active: true,
          type: ActionDocumentCodeType.HA,
        },
        {
          issued: '2025-05-06T11:34:00.000Z',
          link: `/subscriptions/${MOCK_SUBSCRIPTION_ID}/document/A0838D19-ED4H-1HD0-6F21-976J0ABCC627`,
          format: 'PDF',
          active: true,
          type: ActionDocumentCodeType.RCA,
        },
      ]);

      const res = await controller.getSubscriptionDocuments(v4());

      expect(res).toStrictEqual({
        documents: [
          {
            issued: '2025-05-06T11:34:00.000Z',
            link: `/subscriptions/${MOCK_SUBSCRIPTION_ID}/document/A0838D19-ED4H-1HD0-6F21-976J0ABCC627`,
            format: 'PDF',
            active: true,
            type: ActionDocumentCodeType.HA,
          },
          {
            issued: '2025-05-06T11:34:00.000Z',
            link: `/subscriptions/${MOCK_SUBSCRIPTION_ID}/document/A0838D19-ED4H-1HD0-6F21-976J0ABCC627`,
            format: 'PDF',
            active: true,
            type: ActionDocumentCodeType.RCA,
          },
        ],
      });
    });
  });

  describe(SubscriptionsController.prototype.getSubscriptionDocument, () => {
    it('throws a NotFoundException if not a valid document ID format', async () => {
      await expect(() =>
        controller.getSubscriptionDocument(MOCK_SUBSCRIPTION_ID, 'awesdfghj')
      ).rejects.toThrow(NotFoundException);
    });

    it('throws a NotFoundException if service throws a UnknownDocumentError', async () => {
      mockedSubscriptionsService.getSubscriptionDocument.mockRejectedValue(
        new UnknownDocumentError()
      );

      await expect(() =>
        controller.getSubscriptionDocument(MOCK_SUBSCRIPTION_ID, 'LMS-1')
      ).rejects.toThrow(NotFoundException);
    });

    it('throws a InternalServerErrorException if something goes wrong', async () => {
      mockedSubscriptionsService.getSubscriptionDocument.mockRejectedValue(
        new Error()
      );

      await expect(() =>
        controller.getSubscriptionDocument(MOCK_SUBSCRIPTION_ID, 'LMS-1')
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('returns a StreamableFile for the subscription document', async () => {
      const MOCK_READABLE = new Readable();

      mockedSubscriptionsService.getSubscriptionDocument.mockResolvedValue(
        MOCK_READABLE
      );

      const res = await controller.getSubscriptionDocument(
        MOCK_SUBSCRIPTION_ID,
        'LMS-1'
      );

      expect(res).toBeInstanceOf(StreamableFile);
      expect(res.getStream()).toEqual(MOCK_READABLE);
    });
  });

  describe(SubscriptionsController.prototype.updateActionById, () => {
    describe('Affordability Check', () => {
      it('completes the affordability check', async () => {
        mockedActionService.completeAffordabilityCheckAction.mockResolvedValueOnce(
          MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA
        );

        const result = await controller.updateActionById(
          {
            subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
            actionId: MOCK_CHECK_AFFORDABILITY_ACTION.id,
          },
          MOCK_UPDATE_AFFORDABILITY_CHECK_ACTION_DTO
        );

        expect(result).toEqual(MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA_DTO);
      });

      it('completes the home survey update', async () => {
        mockedActionService.completeHomeSurveyAction.mockResolvedValueOnce(
          MOCK_SURVEY_ACTION_ENTITY
        );

        const result = await controller.updateActionById(
          {
            subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
            actionId: MOCK_SURVEY_ACTION_ENTITY.id,
          },
          MOCK_UPDATE_HOME_SURVEY_ACTION_DTO
        );

        expect(result).toEqual(MOCK_SURVEY_ACTION_DTO);
      });

      it('throws a NotFoundException when the action can not be found', async () => {
        mockedActionService.completeAffordabilityCheckAction.mockRejectedValueOnce(
          new CouldNotFindEntityError(MOCK_CHECK_AFFORDABILITY_ACTION, 'oops')
        );

        await expect(
          controller.updateActionById(
            {
              subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
              actionId: MOCK_CHECK_AFFORDABILITY_ACTION.id,
            },
            MOCK_UPDATE_AFFORDABILITY_CHECK_ACTION_DTO
          )
        ).rejects.toThrow(NotFoundException);
      });

      it('throws a BadRequestException when the provided action is not applicable', async () => {
        mockedActionService.completeAffordabilityCheckAction.mockRejectedValueOnce(
          new InvalidEntityError(MOCK_CHECK_AFFORDABILITY_ACTION, 'oops')
        );

        await expect(
          controller.updateActionById(
            {
              subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
              actionId: MOCK_CHECK_AFFORDABILITY_ACTION.id,
            },
            MOCK_UPDATE_AFFORDABILITY_CHECK_ACTION_DTO
          )
        ).rejects.toThrow(BadRequestException);
      });

      it('throws a BadRequestException when the provided action has incomplete dependants', async () => {
        mockedActionService.completeAffordabilityCheckAction.mockRejectedValueOnce(
          new IncompleteDependencyError(MOCK_CHECK_AFFORDABILITY_ACTION, 'oops')
        );

        await expect(
          controller.updateActionById(
            {
              subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
              actionId: MOCK_CHECK_AFFORDABILITY_ACTION.id,
            },
            MOCK_UPDATE_AFFORDABILITY_CHECK_ACTION_DTO
          )
        ).rejects.toThrow(BadRequestException);
      });

      it('throws an InternalServerException when the action can not be persisted', async () => {
        mockedActionService.completeAffordabilityCheckAction.mockRejectedValueOnce(
          new CouldNotPersistEntityError(
            MOCK_CHECK_AFFORDABILITY_ACTION,
            'oops'
          )
        );

        await expect(
          controller.updateActionById(
            {
              subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
              actionId: MOCK_CHECK_AFFORDABILITY_ACTION.id,
            },
            MOCK_UPDATE_AFFORDABILITY_CHECK_ACTION_DTO
          )
        ).rejects.toThrow(InternalServerErrorException);
      });

      it('throws an InternalServerException when slick call fails', async () => {
        mockedActionService.completeAffordabilityCheckAction.mockRejectedValueOnce(
          new LoanManagementSystemError('oops')
        );

        await expect(
          controller.updateActionById(
            {
              subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
              actionId: MOCK_CHECK_AFFORDABILITY_ACTION.id,
            },
            MOCK_UPDATE_AFFORDABILITY_CHECK_ACTION_DTO
          )
        ).rejects.toThrow(InternalServerErrorException);
      });

      it('throws an InternalServerException if something else goes wrong', async () => {
        mockedActionService.completeAffordabilityCheckAction.mockRejectedValueOnce(
          new Error('universe has reached maximum entropy, please reboot')
        );

        await expect(
          controller.updateActionById(
            {
              subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id,
              actionId: MOCK_CHECK_AFFORDABILITY_ACTION.id,
            },
            MOCK_UPDATE_AFFORDABILITY_CHECK_ACTION_DTO
          )
        ).rejects.toThrow(InternalServerErrorException);
      });
    });

    describe('Sync Sign Documents', () => {
      it('syncs the documents', async () => {
        mockedActionService.syncSignDocumentsAction.mockResolvedValueOnce(
          MOCK_SIGN_DOCUMENTS_ACTION_ENTITY_SUCCESS
        );

        const result = await controller.updateActionById(
          { subscriptionId: v4(), actionId: v4() },
          { type: ActionType.SIGN_DOCUMENTS_V1, data: {} }
        );

        expect(result).toEqual(MOCK_SIGN_DOCUMENT_ACTION_DTO);
      });
    });

    describe('Sign Rewards T&C', () => {
      it('completes the action', async () => {
        mockedActionService.completeSignRewardsTOSAction.mockResolvedValue(
          MOCK_SIGN_REWARDS_TOS_ACTION_SUCCESS
        );

        const result = await controller.updateActionById(
          { subscriptionId: v4(), actionId: v4() },
          MOCK_UPDATE_SIGN_REWARDS_TOS_ACTION_DTO
        );

        expect(result).toEqual(MOCK_SIGN_REWARDS_TOS_ACTION_DTO);
      });
    });

    describe('Link Existing Charger', () => {
      it('completes the action', async () => {
        mockedActionService.completeLinkExistingChargerAction.mockResolvedValue(
          MOCK_LINK_EXISTING_CHARGER_ACTION_SUCCESS
        );

        const result = await controller.updateActionById(
          { subscriptionId: v4(), actionId: v4() },
          MOCK_UPDATE_LINK_EXISTING_CHARGER_ACTION_DTO
        );

        expect(result).toEqual(MOCK_LINK_EXISTING_CHARGER_ACTION_DTO);
      });
    });
  });

  describe(SubscriptionsController.prototype.delete, () => {
    it('deletes the subscription successfully', async () => {
      mockedSubscriptionsService.deleteSubscription.mockResolvedValue();

      await controller.delete(
        { subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id },
        'debug'
      );

      expect(
        mockedSubscriptionsService.deleteSubscription
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionsService.deleteSubscription
      ).toHaveBeenCalledWith(MOCK_SUBSCRIPTION_ENTITY.id);
    });

    it('throws a NotFoundException if service throws a CouldNotFindEntityError', async () => {
      const randomId = v4();
      mockedSubscriptionsService.deleteSubscription.mockRejectedValue(
        new CouldNotFindEntityError(
          {
            id: randomId,
          },
          'could not find'
        )
      );

      await expect(() =>
        controller.delete({ subscriptionId: randomId }, 'debug')
      ).rejects.toThrow(NotFoundException);

      expect(
        mockedSubscriptionsService.deleteSubscription
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionsService.deleteSubscription
      ).toHaveBeenCalledWith(randomId);
    });

    it('throws a InternalServerErrorException if something goes wrong', async () => {
      mockedSubscriptionsService.deleteSubscription.mockRejectedValue(
        new Error()
      );

      await expect(() =>
        controller.delete(
          { subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id },
          'debug'
        )
      ).rejects.toThrow(InternalServerErrorException);

      expect(
        mockedSubscriptionsService.deleteSubscription
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionsService.deleteSubscription
      ).toHaveBeenCalledWith(MOCK_SUBSCRIPTION_ENTITY.id);
    });

    it('throws a ForbiddenException if mode not debug', async () => {
      mockedSubscriptionsService.deleteSubscription.mockRejectedValue(
        new Error()
      );

      await expect(() =>
        controller.delete(
          { subscriptionId: MOCK_SUBSCRIPTION_ENTITY.id },
          'precious'
        )
      ).rejects.toThrow(ForbiddenException);

      expect(
        mockedSubscriptionsService.deleteSubscription
      ).toHaveBeenCalledTimes(0);
    });
  });

  describe(SubscriptionsController.prototype.create, () => {
    it('creates a new Pod Drive Rewards subscription', async () => {
      mockedSubscriptionsService.createNew.mockResolvedValue({
        subscription: MOCK_SCR_SUBSCRIPTION_ENTITY,
        actions: [],
      });

      await controller.create(MOCK_CREATE_POD_DRIVE_REWARDS_SUBSCRIPTION_DTO);

      expect(mockedSubscriptionsService.createNew).toHaveBeenCalledTimes(1);
      expect(mockedSubscriptionsService.createNew).toHaveBeenCalledWith({
        protoSubscription: {
          userId: MOCK_CREATE_POD_DRIVE_REWARDS_SUBSCRIPTION_DTO.userId,
          activatedAt: null,
          plan: {
            details: {
              type: MOCK_CREATE_POD_DRIVE_REWARDS_SUBSCRIPTION_DTO.planType,
              allowanceMiles: 3000,
              allowancePeriod: PlanAllowancePeriod.ANNUAL,
              ratePencePerMile: 2.3,
              rateMilesPerKwh: 3.5,
              milesRenewalDate: null,
            },
          },
          order: expect.objectContaining({
            origin: OrderOrigin.POD_DRIVE_REWARDS_SELF_SERVE,
            ppid: 'PSL-123456',
          }),
        },
      });
    });

    it('throws a ConflictException if a PPIDAlreadyOnSubscriptionError is thrown', async () => {
      mockedSubscriptionsService.createNew.mockRejectedValue(
        new PPIDAlreadyOnSubscriptionError()
      );

      await expect(
        controller.create(MOCK_CREATE_POD_DRIVE_REWARDS_SUBSCRIPTION_DTO)
      ).rejects.toThrow(ConflictException);
    });
  });
});
