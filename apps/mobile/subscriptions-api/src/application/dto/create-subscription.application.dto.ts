import { BaseOrderEntity } from '../../domain/entities/order.entity';
import { PlanDetails } from '../../domain/entities/plan.entity';
import { StripFunctions } from '../../utility/types/helper.types';
import { UnpersistedSubscriptionEntity } from '../../domain/entities/subscription.entity';

export class CreateSubscriptionApplicationDTO {
  constructor(params: StripFunctions<CreateSubscriptionApplicationDTO>) {
    Object.assign(this, params);
  }
  protoSubscription: Omit<
    UnpersistedSubscriptionEntity<BaseOrderEntity, PlanDetails>,
    'status'
  >;
  homeSurveyUrl: string;
}
