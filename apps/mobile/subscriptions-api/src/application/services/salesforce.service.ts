import {
  BaseOrderEntity,
  OrderOrigin,
  SalesforceOrderEntity,
} from '../../domain/entities/order.entity';
import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import { SalesforceClient } from '@experience/shared/salesforce/client';

@Injectable()
export class SalesforceService {
  private readonly logger = new Logger(SalesforceService.name);

  private readonly alpBaseUrl: string;

  constructor(
    private readonly salesforceClient: SalesforceClient,
    private readonly configService: ConfigService
  ) {
    this.alpBaseUrl = this.configService.getOrThrow('ALP_BASE_URL');
  }

  isSalesforceOrder(order: BaseOrderEntity): order is SalesforceOrderEntity {
    return [OrderOrigin.SALESFORCE].includes(order.origin);
  }

  async setSubscriptionId(
    order: SalesforceOrderEntity,
    subscriptionId: string
  ) {
    try {
      this.logger.log({ order, subscriptionId }, 'setting subscription id');

      if (order.origin === 'SALESFORCE_TEST') {
        return;
      }

      await this.salesforceClient.setSubscriptionId(order.id, subscriptionId);
    } catch (error) {
      this.logger.error(
        { order, subscriptionId, error },
        'failed to set subscription id'
      );

      throw error;
    }
  }

  async setApplicationUrl(order: SalesforceOrderEntity, applicationId: number) {
    try {
      this.logger.log({ order, applicationId }, 'setting application url');

      // TODO: Temporary support to skip actual Salesforce call
      if (order.origin === 'SALESFORCE_TEST') {
        return;
      }

      await this.salesforceClient.setSlickApplicationUrl(
        order.id,
        `https://${this.alpBaseUrl}/core/application/view?id=${applicationId}`
      );
    } catch (error) {
      this.logger.error(
        { order, applicationId, error },
        'failed to set application url'
      );

      throw error;
    }
  }

  async setInterventionReason(
    order: SalesforceOrderEntity,
    interventionReason:
      | 'affordability'
      | 'direct_debit'
      | 'direct_debit_recovered'
      | 'affordability_recovered'
  ) {
    try {
      this.logger.log(
        { order, interventionReason },
        'setting intervention reason'
      );

      // TODO: Temporary support to skip actual Salesforce call
      if (order.origin === 'SALESFORCE_TEST') {
        return;
      }

      await this.salesforceClient.setSubscriptionInterventionReason(
        order.id,
        interventionReason
      );
    } catch (error) {
      this.logger.error(
        { order, interventionReason, error },
        'failed to set intervention reason'
      );

      throw error;
    }
  }

  async setApplicationComplete(order: SalesforceOrderEntity) {
    try {
      this.logger.log({ order }, 'setting application to complete');

      // TODO: Temporary support to skip actual Salesforce call
      if (order.origin === 'SALESFORCE_TEST') {
        return;
      }

      await this.salesforceClient.setSubscriptionSetupComplete(order.id, true);
    } catch (error) {
      this.logger.error(
        { order, error },
        'failed to set application to complete'
      );

      throw error;
    }
  }
}
