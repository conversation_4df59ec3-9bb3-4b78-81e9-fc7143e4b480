import {
  ActionDocumentCodeType,
  ActionEntity,
  UnpersistedActionEntity,
} from '../../domain/entities/action.entity';
import { ActionService } from './action.service';
import {
  CouldNotFindEntityError,
  CouldNotPersistEntityError,
} from '@experience/mobile/clean-architecture';
import { LoanManagementSystemService } from './loan-management-system.service';
import {
  MOCK_ACTIVE_NEWEST_SUBSCRIPTION,
  MOCK_ACTIVE_OLDEST_SUBSCRIPTION,
  MOCK_APPLICATION_ID,
  MOCK_CANCELLED_NEWEST_SUBSCRIPTION,
  MOCK_CANCELLED_OLDEST_SUBSCRIPTION,
  MOCK_CHECK_AFFORDABILITY_ACTION,
  MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA,
  MOCK_CREATE_SUBSCRIPTION_ENTITY,
  MOCK_ENDED_NEWEST_SUBSCRIPTION,
  MOCK_ENDED_OLDEST_SUBSCRIPTION,
  MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
  MOCK_ORDER_ENTITY,
  MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY,
  MOCK_PENDING_NEWEST_SUBSCRIPTION,
  MOCK_PENDING_OLDEST_SUBSCRIPTION,
  MOCK_REJECTED_NEWEST_SUBSCRIPTION,
  MOCK_REJECTED_OLDEST_SUBSCRIPTION,
  MOCK_SETUP_DIRECT_DEBIT_ACTION,
  MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
  MOCK_SUBSCRIPTION_ENTITY,
  MOCK_SURVEY_ACTION_ENTITY,
  MOCK_SUSPENDED_NEWEST_SUBSCRIPTION,
  MOCK_SUSPENDED_OLDEST_SUBSCRIPTION,
} from '../../domain/entities/__fixtures__/entity.fixtures';
import { MOCK_CREATE_SUBSCRIPTION_APPLICATION_DTO } from '../__fixtures__/subscription';
import { MOCK_SUBSCRIPTION_ID } from '../../interfaces/http/dto/__fixtures__/dto.fixtures';
import { MissingDataError } from '../errors/missing-data.error';
import { NoApplicationIdError } from '../errors/no-application-id.error';
import { PersistedSubscriptionWithActionsDTO } from '../dto/persisted-subscription-with-actions.dto';
import { PlanType } from '../../domain/entities/plan.entity';
import { Readable } from 'stream';
import { SubscriptionDirectDebit } from '../../domain/types/subscription.types';
import { SubscriptionDocumentsDTO } from '../dto/subscription-document.dto';
import { SubscriptionNotActiveError } from '../errors/subscription-not-active.error';
import { SubscriptionRepositoryInterface } from '../../domain/repositories/subscription.repository.interface';
import { SubscriptionStatus } from '../../domain/entities/subscription.entity';
import { SubscriptionsService } from './subscriptions.service';
import { UnknownDocumentError } from '../errors/unknown-document.error';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { createMock } from '@golevelup/ts-jest';
import { v4 } from 'uuid';

const mockActionCreateImplementation = async (
  proto: UnpersistedActionEntity
): Promise<ActionEntity> =>
  [
    MOCK_CHECK_AFFORDABILITY_ACTION,
    MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
    MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY,
    MOCK_SETUP_DIRECT_DEBIT_ACTION,
    MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
    MOCK_SURVEY_ACTION_ENTITY,
  ].find((action) => action.data.type === proto.data.type) as ActionEntity;

describe(SubscriptionsService, () => {
  let mockedSubscriptionRepository: jest.Mocked<SubscriptionRepositoryInterface>;
  let mockedActionService: jest.Mocked<ActionService>;
  let mockedLoanManagementSystemService: jest.Mocked<LoanManagementSystemService>;
  let service: SubscriptionsService;

  beforeEach(() => {
    mockedSubscriptionRepository = createMock();
    mockedActionService = createMock();
    mockedLoanManagementSystemService = createMock();

    service = new SubscriptionsService(
      mockedSubscriptionRepository,
      mockedActionService,
      mockedLoanManagementSystemService
    );
  });

  describe(SubscriptionsService.prototype.getByPpidAndOrUserId, () => {
    it('calls the getByUserId method in the repository with the user id for preference', async () => {
      const userId = v4();
      const ppid = 'PSL-123456';

      await service.getByPpidAndOrUserId({ userId, ppid });

      expect(mockedSubscriptionRepository.getByUserId).toHaveBeenCalledTimes(1);
      expect(mockedSubscriptionRepository.getByUserId).toHaveBeenCalledWith({
        userId,
      });
    });

    it('calls the getByUserId method in the repository with the ppid if supplied', async () => {
      const ppid = 'PSL-123456';

      await service.getByPpidAndOrUserId({ ppid });

      expect(mockedSubscriptionRepository.getByPpid).toHaveBeenCalledTimes(1);
      expect(mockedSubscriptionRepository.getByPpid).toHaveBeenCalledWith({
        ppid,
      });
    });

    it('returns the found subscription from the repository', async () => {
      mockedSubscriptionRepository.getByUserId.mockResolvedValueOnce([
        MOCK_SUBSCRIPTION_ENTITY,
      ]);

      mockedActionService.findBySubscription.mockResolvedValueOnce([
        MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
      ]);

      const actual = await service.getByPpidAndOrUserId({
        userId: MOCK_SUBSCRIPTION_ENTITY.userId,
      });

      expect(actual).toEqual([
        {
          subscription: MOCK_SUBSCRIPTION_ENTITY,
          actions: [MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY],
        },
      ]);
    });

    it('returns empty array from the repository when none found', async () => {
      const userId = v4();

      mockedSubscriptionRepository.getByUserId.mockResolvedValueOnce([]);

      const actual = await service.getByPpidAndOrUserId({ userId });

      expect(actual).toEqual([]);
    });

    it('correctly sorts list of subscriptions', async () => {
      mockedSubscriptionRepository.getByUserId.mockResolvedValue([
        MOCK_CANCELLED_OLDEST_SUBSCRIPTION,
        MOCK_ACTIVE_NEWEST_SUBSCRIPTION,
        MOCK_CANCELLED_NEWEST_SUBSCRIPTION,
        MOCK_PENDING_NEWEST_SUBSCRIPTION,
        MOCK_ACTIVE_OLDEST_SUBSCRIPTION,
        MOCK_PENDING_OLDEST_SUBSCRIPTION,
        MOCK_SUSPENDED_NEWEST_SUBSCRIPTION,
        MOCK_ENDED_OLDEST_SUBSCRIPTION,
        MOCK_ENDED_NEWEST_SUBSCRIPTION,
        MOCK_SUSPENDED_OLDEST_SUBSCRIPTION,
        MOCK_REJECTED_NEWEST_SUBSCRIPTION,
        MOCK_REJECTED_OLDEST_SUBSCRIPTION,
      ]);

      mockedActionService.findBySubscription.mockResolvedValueOnce([
        MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
      ]);

      const res = await service.getByPpidAndOrUserId({
        userId: MOCK_SUBSCRIPTION_ENTITY.userId,
      });

      const subscriptions = res.map((x) => x.subscription);

      expect(subscriptions).toStrictEqual([
        MOCK_ACTIVE_NEWEST_SUBSCRIPTION,
        MOCK_ACTIVE_OLDEST_SUBSCRIPTION,
        MOCK_PENDING_NEWEST_SUBSCRIPTION,
        MOCK_PENDING_OLDEST_SUBSCRIPTION,
        MOCK_REJECTED_NEWEST_SUBSCRIPTION,
        MOCK_REJECTED_OLDEST_SUBSCRIPTION,
        MOCK_SUSPENDED_NEWEST_SUBSCRIPTION,
        MOCK_SUSPENDED_OLDEST_SUBSCRIPTION,
        MOCK_ENDED_NEWEST_SUBSCRIPTION,
        MOCK_ENDED_OLDEST_SUBSCRIPTION,
        MOCK_CANCELLED_NEWEST_SUBSCRIPTION,
        MOCK_CANCELLED_OLDEST_SUBSCRIPTION,
      ]);
    });
  });

  describe(SubscriptionsService.prototype.readById, () => {
    it('calls the readById method in the repository with the correct params', async () => {
      mockedSubscriptionRepository.read.mockResolvedValue(
        MOCK_SUBSCRIPTION_ENTITY
      );

      await service.readById(MOCK_SUBSCRIPTION_ENTITY.id);

      expect(mockedSubscriptionRepository.read).toHaveBeenCalledTimes(1);
      expect(mockedSubscriptionRepository.read).toHaveBeenCalledWith(
        MOCK_SUBSCRIPTION_ENTITY.id
      );
    });

    it('returns the found subscription from the repository', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce(
        MOCK_SUBSCRIPTION_ENTITY
      );

      mockedActionService.findBySubscription.mockResolvedValueOnce([
        MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
      ]);

      const actual = await service.readById(MOCK_SUBSCRIPTION_ID);

      expect(actual).toEqual({
        subscription: MOCK_SUBSCRIPTION_ENTITY,
        actions: [MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY],
      });
    });

    it('includes the milesRenewalsDate if active subscription', async () => {
      const dateNowSpy = jest
        .spyOn(Date, 'now')
        .mockReturnValue(new Date('2026-10-01T00:00:00.000Z').getTime());

      mockedSubscriptionRepository.read.mockResolvedValue({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
        activatedAt: new Date('2025-06-05T10:47:00.000Z'),
      });

      const res = await service.readById(MOCK_SUBSCRIPTION_ENTITY.id);

      expect(res?.subscription.plan.details.milesRenewalDate).toEqual(
        new Date('2027-06-05T10:47:00.000Z')
      );

      dateNowSpy.mockRestore();
    });

    it('returns null from the repository when not found', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce(null);

      const actual = await service.readById(v4());

      expect(actual).toBeNull();
    });
  });

  describe(SubscriptionsService.prototype.createNew, () => {
    it('calls the create method in the repository with the correct params', async () => {
      mockedSubscriptionRepository.create.mockResolvedValueOnce(
        MOCK_SUBSCRIPTION_ENTITY
      );
      mockedActionService.create.mockResolvedValue(
        MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY
      );

      await service.createNew(MOCK_CREATE_SUBSCRIPTION_APPLICATION_DTO);

      expect(mockedSubscriptionRepository.create).toHaveBeenCalledTimes(1);
      expect(mockedSubscriptionRepository.create).toHaveBeenCalledWith({
        userId: MOCK_CREATE_SUBSCRIPTION_ENTITY.userId,
        status: SubscriptionStatus.PENDING,
        plan: MOCK_CREATE_SUBSCRIPTION_ENTITY.plan,
        order: MOCK_CREATE_SUBSCRIPTION_ENTITY.order,
        activatedAt: null,
      });
    });

    it('returns the new subscription from the repository', async () => {
      mockedSubscriptionRepository.create.mockResolvedValueOnce(
        MOCK_SUBSCRIPTION_ENTITY
      );
      mockedActionService.create.mockImplementation(
        mockActionCreateImplementation
      );

      const actual = await service.createNew(
        MOCK_CREATE_SUBSCRIPTION_APPLICATION_DTO
      );

      expect(actual).toEqual(
        new PersistedSubscriptionWithActionsDTO({
          subscription: MOCK_SUBSCRIPTION_ENTITY,
          actions: [
            MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY,
            MOCK_SURVEY_ACTION_ENTITY,
            MOCK_CHECK_AFFORDABILITY_ACTION,
            MOCK_SETUP_DIRECT_DEBIT_ACTION,
            MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
            MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
          ],
        })
      );
    });

    it('returns the new actions from the action service', async () => {
      mockedSubscriptionRepository.create.mockResolvedValueOnce(
        MOCK_SUBSCRIPTION_ENTITY
      );

      mockedActionService.create.mockImplementation(
        mockActionCreateImplementation
      );

      const actual = await service.createNew(
        MOCK_CREATE_SUBSCRIPTION_APPLICATION_DTO
      );

      expect(actual).toEqual(
        new PersistedSubscriptionWithActionsDTO({
          subscription: MOCK_SUBSCRIPTION_ENTITY,
          actions: [
            MOCK_PAY_UPFRONT_FEE_ACTION_ENTITY,
            MOCK_SURVEY_ACTION_ENTITY,
            MOCK_CHECK_AFFORDABILITY_ACTION,
            MOCK_SETUP_DIRECT_DEBIT_ACTION,
            MOCK_SIGN_DOCUMENTS_ACTION_ENTITY,
            MOCK_INSTALL_CHARGING_STATION_ACTION_ENTITY,
          ],
        })
      );
    });

    it('throws CouldNotPersistEntityError from repository', async () => {
      const expectedError = new CouldNotPersistEntityError(
        MOCK_CREATE_SUBSCRIPTION_ENTITY,
        'this is a test'
      );

      mockedSubscriptionRepository.create.mockRejectedValueOnce(expectedError);

      await expect(
        service.createNew(MOCK_CREATE_SUBSCRIPTION_APPLICATION_DTO)
      ).rejects.toThrow(expectedError);
    });
  });

  describe(SubscriptionsService.prototype.getSubscriptionByOrderId, () => {
    it('calls the repository', async () => {
      mockedSubscriptionRepository.getSubscriptionByOrderId.mockResolvedValue(
        MOCK_SUBSCRIPTION_ENTITY
      );

      const res = await service.getSubscriptionByOrderId(MOCK_ORDER_ENTITY.id);

      expect(res).toStrictEqual(MOCK_SUBSCRIPTION_ENTITY);

      expect(
        mockedSubscriptionRepository.getSubscriptionByOrderId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionRepository.getSubscriptionByOrderId
      ).toHaveBeenCalledWith(MOCK_ORDER_ENTITY.id);
    });
  });

  describe(SubscriptionsService.prototype.doesMatchingSubscriptionExist, () => {
    it('returns false if subscription does not exist', async () => {
      mockedSubscriptionRepository.getSubscriptionByOrderId.mockResolvedValue(
        null
      );

      const res = await service.doesMatchingSubscriptionExist(
        '88b8f8f5-6764-4017-b3f3-c9ab60146947',
        PlanType.POD_DRIVE
      );

      expect(res).toBeFalsy();

      expect(
        mockedSubscriptionRepository.getSubscriptionByOrderId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionRepository.getSubscriptionByOrderId
      ).toHaveBeenCalledWith('88b8f8f5-6764-4017-b3f3-c9ab60146947');
    });

    it('returns false if subscription exist, with a different type', async () => {
      mockedSubscriptionRepository.getSubscriptionByOrderId.mockResolvedValue(
        MOCK_SUBSCRIPTION_ENTITY
      );

      const res = await service.doesMatchingSubscriptionExist(
        MOCK_ORDER_ENTITY.id,
        PlanType.POD_DRIVE_REWARDS
      );

      expect(res).toBeFalsy();

      expect(
        mockedSubscriptionRepository.getSubscriptionByOrderId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionRepository.getSubscriptionByOrderId
      ).toHaveBeenCalledWith(MOCK_ORDER_ENTITY.id);
    });

    it('returns true if subscription exists with matching type', async () => {
      mockedSubscriptionRepository.getSubscriptionByOrderId.mockResolvedValue(
        MOCK_SUBSCRIPTION_ENTITY
      );

      const res = await service.doesMatchingSubscriptionExist(
        MOCK_ORDER_ENTITY.id,
        PlanType.POD_DRIVE
      );

      expect(res).toBeTruthy();

      expect(
        mockedSubscriptionRepository.getSubscriptionByOrderId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionRepository.getSubscriptionByOrderId
      ).toHaveBeenCalledWith(MOCK_ORDER_ENTITY.id);
    });
  });

  describe(
    SubscriptionsService.prototype.getPendingSubscriptionByOrderId,
    () => {
      it('calls the repository', async () => {
        mockedSubscriptionRepository.getPendingSubscriptionByOrderId.mockResolvedValue(
          MOCK_SUBSCRIPTION_ENTITY
        );

        const res = await service.getPendingSubscriptionByOrderId(
          MOCK_ORDER_ENTITY.id
        );

        expect(res).toStrictEqual(MOCK_SUBSCRIPTION_ENTITY);

        expect(
          mockedSubscriptionRepository.getPendingSubscriptionByOrderId
        ).toHaveBeenCalledTimes(1);
        expect(
          mockedSubscriptionRepository.getPendingSubscriptionByOrderId
        ).toHaveBeenCalledWith(MOCK_ORDER_ENTITY.id);
      });
    }
  );

  describe(SubscriptionsService.prototype.activateSubscription, () => {
    it('activates the subscription', async () => {
      await service.activateSubscription(MOCK_SUBSCRIPTION_ENTITY.id);

      expect(
        mockedSubscriptionRepository.activateSubscription
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionRepository.activateSubscription
      ).toHaveBeenCalledWith(MOCK_SUBSCRIPTION_ENTITY.id);
    });
  });

  describe(SubscriptionsService.prototype.updateSubscriptionStatus, () => {
    it('calls the repository', async () => {
      mockedSubscriptionRepository.updateSubscriptionStatus.mockResolvedValue({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.SUSPENDED,
      });

      const res = await service.updateSubscriptionStatus(
        MOCK_SUBSCRIPTION_ID,
        SubscriptionStatus.SUSPENDED
      );

      expect(res).toStrictEqual({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.SUSPENDED,
      });

      expect(
        mockedSubscriptionRepository.updateSubscriptionStatus
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionRepository.updateSubscriptionStatus
      ).toHaveBeenCalledWith(
        MOCK_SUBSCRIPTION_ID,
        SubscriptionStatus.SUSPENDED
      );
    });
  });

  describe(
    SubscriptionsService.prototype.cancelSubscriptionByApplicationId,
    () => {
      it('gets the subscription by application ID and cancels it', async () => {
        mockedActionService.getAffordabilityActionByApplicationId.mockResolvedValue(
          MOCK_CHECK_AFFORDABILITY_ACTION_WITH_DATA
        );
        mockedSubscriptionRepository.updateSubscriptionStatus.mockResolvedValue(
          {
            ...MOCK_SUBSCRIPTION_ENTITY,
            status: SubscriptionStatus.CANCELLED,
          }
        );

        const res = await service.cancelSubscriptionByApplicationId(
          MOCK_APPLICATION_ID
        );

        expect(res).toStrictEqual({
          ...MOCK_SUBSCRIPTION_ENTITY,
          status: SubscriptionStatus.CANCELLED,
        });

        expect(
          mockedActionService.getAffordabilityActionByApplicationId
        ).toHaveBeenCalledTimes(1);
        expect(
          mockedActionService.getAffordabilityActionByApplicationId
        ).toHaveBeenCalledWith(MOCK_APPLICATION_ID);

        expect(
          mockedSubscriptionRepository.updateSubscriptionStatus
        ).toHaveBeenCalledTimes(1);
        expect(
          mockedSubscriptionRepository.updateSubscriptionStatus
        ).toHaveBeenCalledWith(
          MOCK_SUBSCRIPTION_ID,
          SubscriptionStatus.CANCELLED
        );
      });
    }
  );

  describe(SubscriptionsService.prototype.getByActionId, () => {
    it('calls the repository', async () => {
      mockedSubscriptionRepository.getByActionId.mockResolvedValue(
        MOCK_SUBSCRIPTION_ENTITY
      );

      const res = await service.getByActionId(MOCK_SURVEY_ACTION_ENTITY.id);

      expect(res).toStrictEqual(MOCK_SUBSCRIPTION_ENTITY);

      expect(mockedSubscriptionRepository.getByActionId).toHaveBeenCalledTimes(
        1
      );
      expect(mockedSubscriptionRepository.getByActionId).toHaveBeenCalledWith(
        MOCK_SURVEY_ACTION_ENTITY.id
      );
    });
  });

  describe(SubscriptionsService.prototype.deleteSubscription, () => {
    it('calls the subscription repository to cancel and delete', async () => {
      mockedSubscriptionRepository.cancelAndDeleteSubscription.mockResolvedValue();

      await service.deleteSubscription(MOCK_SUBSCRIPTION_ENTITY.id);

      expect(
        mockedSubscriptionRepository.cancelAndDeleteSubscription
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedSubscriptionRepository.cancelAndDeleteSubscription
      ).toHaveBeenCalledWith(MOCK_SUBSCRIPTION_ENTITY.id);
    });

    it('bubbles up repository thrown errors', async () => {
      mockedSubscriptionRepository.cancelAndDeleteSubscription.mockRejectedValue(
        new CouldNotFindEntityError(
          {
            id: 'random-id',
          },
          'example'
        )
      );

      await expect(() =>
        service.deleteSubscription('random-id')
      ).rejects.toThrow(CouldNotFindEntityError);
    });
  });

  describe(SubscriptionsService.prototype.getSubscriptionDirectDebit, () => {
    it('throws an exception if subscription could not be retrieved', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce(null);

      await expect(service.getSubscriptionDirectDebit(v4())).rejects.toThrow(
        CouldNotFindEntityError
      );
    });

    it('throws an exception if subscription is not active', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce(
        MOCK_SUBSCRIPTION_ENTITY
      );

      await expect(service.getSubscriptionDirectDebit(v4())).rejects.toThrow(
        SubscriptionNotActiveError
      );
    });

    it('throws an exception if check affordability action could not be retrieved', async () => {
      const exception = new CouldNotFindEntityError({ id: 'test' }, 'oops');
      const subscriptionId = v4();

      mockedSubscriptionRepository.read.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
      });

      mockedActionService.getAffordabilityApplicationId.mockRejectedValueOnce(
        exception
      );

      await expect(
        service.getSubscriptionDirectDebit(subscriptionId)
      ).rejects.toThrow(exception);

      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledWith(subscriptionId);
    });

    it('throws an exception if check affordability is null', async () => {
      const subscriptionId = v4();

      mockedSubscriptionRepository.read.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
      });

      mockedActionService.getAffordabilityApplicationId.mockResolvedValueOnce(
        null
      );

      await expect(
        service.getSubscriptionDirectDebit(subscriptionId)
      ).rejects.toThrow(NoApplicationIdError);

      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledWith(subscriptionId);
    });

    it('retrieves direct debit details', async () => {
      const directDebitDetails: SubscriptionDirectDebit = {
        accountNumberLastDigits: '1234',
        sortCodeLastDigits: '12',
        nameOnAccount: 'Mr Tom Wallace',
        monthlyPaymentDay: 13,
      };

      const subscriptionId = v4();
      const applicationId = 42;

      mockedSubscriptionRepository.read.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
      });

      mockedActionService.getAffordabilityApplicationId.mockResolvedValueOnce(
        applicationId
      );

      mockedLoanManagementSystemService.getApplicationDirectDebitDetails.mockResolvedValueOnce(
        directDebitDetails
      );

      expect(await service.getSubscriptionDirectDebit(subscriptionId)).toEqual(
        directDebitDetails
      );

      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledWith(subscriptionId);

      expect(
        mockedLoanManagementSystemService.getApplicationDirectDebitDetails
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.getApplicationDirectDebitDetails
      ).toHaveBeenCalledWith(applicationId);
    });
  });

  describe(SubscriptionsService.prototype.getSubscriptionDocuments, () => {
    const MOCK_APPLICATION_ID = 1;

    it('throws an exception if subscription could not be retrieved', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce(null);

      await expect(service.getSubscriptionDocuments(v4())).rejects.toThrow(
        CouldNotFindEntityError
      );
    });

    it('throws an exception if subscription is not active', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce(
        MOCK_SUBSCRIPTION_ENTITY
      );

      await expect(
        service.getSubscriptionDocuments(MOCK_SUBSCRIPTION_ID)
      ).rejects.toThrow(SubscriptionNotActiveError);
    });

    it('throws an exception if check affordability action could not be retrieved', async () => {
      const exception = new CouldNotFindEntityError({ id: 'test' }, 'oops');

      mockedSubscriptionRepository.read.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
      });

      mockedActionService.getAffordabilityApplicationId.mockRejectedValueOnce(
        exception
      );

      await expect(
        service.getSubscriptionDocuments(MOCK_SUBSCRIPTION_ID)
      ).rejects.toThrow(exception);

      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledWith(MOCK_SUBSCRIPTION_ID);
    });

    it('throws an exception if check affordability is null', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
      });

      mockedActionService.getAffordabilityApplicationId.mockResolvedValueOnce(
        null
      );

      await expect(
        service.getSubscriptionDocuments(MOCK_SUBSCRIPTION_ID)
      ).rejects.toThrow(NoApplicationIdError);

      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledWith(MOCK_SUBSCRIPTION_ID);
    });

    it('throws a MissingDataError if a document does not have a sign_by_link or tag', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
      });

      mockedActionService.getAffordabilityApplicationId.mockResolvedValueOnce(
        MOCK_APPLICATION_ID
      );

      mockedLoanManagementSystemService.getApplicationDocuments.mockResolvedValue(
        [
          {
            id: 1,
            createdAt: 123,
            type: 'sign_by_link',
          } as unknown as SubscriptionDocumentsDTO,
          {
            id: 2,
            createdAt: 123,
            type: 'sign_by_link',
            signByLink: {
              signedAt: 123,
              tag: null,
              status: 'signed',
            },
          },
        ]
      );

      mockedLoanManagementSystemService.getDocument.mockResolvedValue({
        code: 200,
        message: 'Success',
        response: {
          token: 'A0838D19-ED4H-1HD0-6F21-976J0ABCC627',
          expiry_time: 1749206780,
        },
      });

      await expect(
        service.getSubscriptionDocuments(MOCK_SUBSCRIPTION_ID)
      ).rejects.toThrow(MissingDataError);

      expect(
        mockedLoanManagementSystemService.getApplicationDocuments
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.getApplicationDocuments
      ).toHaveBeenCalledWith(MOCK_APPLICATION_ID);
    });

    it('returns the subscription documents with their links', async () => {
      const MOCK_SIGNED_AT = new Date();

      mockedSubscriptionRepository.read.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
      });

      mockedActionService.getAffordabilityApplicationId.mockResolvedValueOnce(
        MOCK_APPLICATION_ID
      );

      mockedLoanManagementSystemService.getApplicationDocuments.mockResolvedValue(
        [
          {
            id: 1,
            createdAt: 123,
            type: 'sign_by_link',
            signByLink: {
              signedAt: MOCK_SIGNED_AT.getTime(),
              status: 'signed',
              tag: 'ha',
            },
          },
          {
            id: 2,
            createdAt: 123,
            type: 'sign_by_link',
            signByLink: {
              signedAt: MOCK_SIGNED_AT.getTime(),
              status: 'signed',
              tag: 'rca',
            },
          },
        ]
      );

      const res = await service.getSubscriptionDocuments(MOCK_SUBSCRIPTION_ID);

      expect(res).toStrictEqual([
        {
          issued: MOCK_SIGNED_AT.toISOString(),
          link: `/subscriptions/${MOCK_SUBSCRIPTION_ID}/documents/LMS-1`,
          format: 'PDF',
          active: true,
          type: ActionDocumentCodeType.HA,
        },
        {
          issued: MOCK_SIGNED_AT.toISOString(),
          link: `/subscriptions/${MOCK_SUBSCRIPTION_ID}/documents/LMS-2`,
          format: 'PDF',
          active: true,
          type: ActionDocumentCodeType.RCA,
        },
      ]);

      expect(
        mockedLoanManagementSystemService.getApplicationDocuments
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.getApplicationDocuments
      ).toHaveBeenCalledWith(MOCK_APPLICATION_ID);
    });
  });

  describe(SubscriptionsService.prototype.getSubscriptionDocument, () => {
    const MOCK_APPLICATION_ID = 1;
    const MOCK_DOCUMENT_ID = 2;

    it('throws an exception if subscription could not be retrieved', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce(null);

      await expect(
        service.getSubscriptionDocument(v4(), MOCK_DOCUMENT_ID)
      ).rejects.toThrow(CouldNotFindEntityError);
    });

    it('throws an exception if subscription is not active', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce(
        MOCK_SUBSCRIPTION_ENTITY
      );

      await expect(
        service.getSubscriptionDocument(MOCK_SUBSCRIPTION_ID, MOCK_DOCUMENT_ID)
      ).rejects.toThrow(SubscriptionNotActiveError);
    });

    it('throws an exception if check affordability action could not be retrieved', async () => {
      const exception = new CouldNotFindEntityError({ id: 'test' }, 'oops');

      mockedSubscriptionRepository.read.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
      });

      mockedActionService.getAffordabilityApplicationId.mockRejectedValueOnce(
        exception
      );

      await expect(
        service.getSubscriptionDocument(MOCK_SUBSCRIPTION_ID, MOCK_DOCUMENT_ID)
      ).rejects.toThrow(exception);

      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledWith(MOCK_SUBSCRIPTION_ID);
    });

    it('throws an exception if check affordability is null', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
      });

      mockedActionService.getAffordabilityApplicationId.mockResolvedValueOnce(
        null
      );

      await expect(
        service.getSubscriptionDocument(MOCK_SUBSCRIPTION_ID, MOCK_DOCUMENT_ID)
      ).rejects.toThrow(NoApplicationIdError);

      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedActionService.getAffordabilityApplicationId
      ).toHaveBeenCalledWith(MOCK_SUBSCRIPTION_ID);
    });

    it('throws a UnknownDocumentError if given document id does not belong to given subscription', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
      });

      mockedActionService.getAffordabilityApplicationId.mockResolvedValueOnce(
        MOCK_APPLICATION_ID
      );

      mockedLoanManagementSystemService.getApplicationDocuments.mockResolvedValue(
        []
      );

      await expect(() =>
        service.getSubscriptionDocument(MOCK_SUBSCRIPTION_ID, MOCK_DOCUMENT_ID)
      ).rejects.toThrow(UnknownDocumentError);

      expect(
        mockedLoanManagementSystemService.getApplicationDocuments
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.getApplicationDocuments
      ).toHaveBeenCalledWith(MOCK_APPLICATION_ID);

      expect(
        mockedLoanManagementSystemService.getDocument
      ).not.toHaveBeenCalled();
      expect(
        mockedLoanManagementSystemService.downloadDocumentByToken
      ).not.toHaveBeenCalled();
    });

    it('returns a readable stream of the document', async () => {
      mockedSubscriptionRepository.read.mockResolvedValueOnce({
        ...MOCK_SUBSCRIPTION_ENTITY,
        status: SubscriptionStatus.ACTIVE,
      });

      mockedActionService.getAffordabilityApplicationId.mockResolvedValueOnce(
        MOCK_APPLICATION_ID
      );

      mockedLoanManagementSystemService.getApplicationDocuments.mockResolvedValue(
        [
          {
            id: MOCK_DOCUMENT_ID,
            type: 'ha',
            createdAt: 123,
          } as unknown as SubscriptionDocumentsDTO,
        ]
      );

      const MOCK_TOKEN = 'A0838D19-ED4H-1HD0-6F21-976J0ABCC627';

      mockedLoanManagementSystemService.getDocument.mockResolvedValue({
        code: 200,
        message: 'OK',
        response: {
          token: MOCK_TOKEN,
          expiry_time: 123,
        },
      });

      mockedLoanManagementSystemService.downloadDocumentByToken.mockResolvedValue(
        new Readable()
      );

      const res = await service.getSubscriptionDocument(
        MOCK_SUBSCRIPTION_ID,
        MOCK_DOCUMENT_ID
      );

      expect(res).toBeInstanceOf(Readable);

      expect(
        mockedLoanManagementSystemService.getApplicationDocuments
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.getApplicationDocuments
      ).toHaveBeenCalledWith(MOCK_APPLICATION_ID);

      expect(
        mockedLoanManagementSystemService.getDocument
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.getDocument
      ).toHaveBeenCalledWith(MOCK_DOCUMENT_ID);

      expect(
        mockedLoanManagementSystemService.downloadDocumentByToken
      ).toHaveBeenCalledTimes(1);
      expect(
        mockedLoanManagementSystemService.downloadDocumentByToken
      ).toHaveBeenCalledWith(MOCK_TOKEN);
    });
  });

  describe(
    SubscriptionsService.prototype.cancelPendingSCRSelfServiceSubscriptions,
    () => {
      it('calls the repository to cancel pending self-serviced SCR subscriptions', async () => {
        await service.cancelPendingSCRSelfServiceSubscriptions('PSL-123456');

        expect(
          mockedSubscriptionRepository.cancelPendingSCRSelfServiceSubscriptions
        ).toHaveBeenCalledTimes(1);
        expect(
          mockedSubscriptionRepository.cancelPendingSCRSelfServiceSubscriptions
        ).toHaveBeenCalledWith('PSL-123456');
      });
    }
  );
});
