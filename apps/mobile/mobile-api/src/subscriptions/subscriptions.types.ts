import {
  ActionOwner,
  ActionStatus,
  CheckAffordabilityActionDTO,
  CheckAffordabilityActionDTOTypeEnum,
  CheckAffordabilityDataDTO,
  HomeSurveyActionDTO,
  HomeSurveyActionDTOTypeEnum,
  HomeSurveyActionDataDTO,
  InstallChargingStationActionDTO,
  InstallChargingStationActionDTOTypeEnum,
  InstallChargingStationDataDTO,
  LinkExistingChargerActionDTO,
  LinkExistingChargerActionDTOTypeEnum,
  OrderAddressDTO,
  PayUpfrontFeeActionDTO,
  PayUpfrontFeeActionDTOTypeEnum,
  PersistedSubscriptionDTO,
  PersistedSubscriptionDTOActionsInner,
  PersistedSubscriptionDTOStatusEnum,
  PodDrivePlanDTO,
  PodDrivePlanDTOAllowancePeriodEnum,
  PodDrivePlanDTOTypeEnum,
  PodDriveRewardsPlanDTO,
  PodDriveRewardsPlanDTOTypeEnum,
  SalesforceOrderDTO,
  SalesforceOrderDTOOriginEnum,
  SelfServiceOrderDTO,
  SelfServiceOrderDTOOriginEnum,
  SetupDirectDebitActionDTO as SetupDirectDebitActionDTOAxios,
  SetupDirectDebitActionDTOTypeEnum,
  SignDocumentDTO,
  SignDocumentDTOCodeEnum,
  SignDocumentsActionDTO,
  SignDocumentsActionDTOTypeEnum,
  SignDocumentsDataDTO,
  SignRewardsTOSActionDTO,
  SignRewardsTOSActionDTOTypeEnum,
  SignRewardsTOSActionDataDTO,
  SubscriptionDocumentDTOTypeEnum,
  UpdateAffordabilityActionDTOTypeEnum,
  UpdateCheckAffordabilityDataDTOCircumstancesRequireSupportEnum,
  UpdateCheckAffordabilityDataDTODependenciesEnum,
  UpdateCheckAffordabilityDataDTOEmploymentStatusEnum,
  UpdateCheckAffordabilityDataDTOMaritalStatusEnum,
  UpdateCheckAffordabilityDataDTOResidentialStatusEnum,
  UpdateCheckAffordabilityDataDTOTitleEnum,
  UpdateDirectDebitActionDTOTypeEnum,
} from '@experience/mobile/subscriptions-api/axios';
import { ApiExtraModels, ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString, Matches } from 'class-validator';

class SubscriptionSurveyActionDataDTO implements HomeSurveyActionDataDTO {
  @ApiProperty({
    description: 'URL of the survey',
  })
  surveyUrl: string;
}

class SubscriptionInstallChargingStationActionDataDTO
  implements InstallChargingStationDataDTO
{
  @ApiProperty({
    description: 'The PPID of the charging station. Set once installed.',
    nullable: true,
  })
  ppid: string | null;
}

class SubscriptionCheckAffordabilityDataDTO
  implements CheckAffordabilityDataDTO
{
  @ApiProperty({
    description: 'The ID of the application within the LMS',
    nullable: true,
  })
  applicationId: number | null;

  @ApiProperty({
    description: 'The loan ID within the LMS',
    nullable: true,
  })
  loanId: number | null;
}

class SubscriptionSignLoanAgreementActionSignDocumentDTO
  implements SignDocumentDTO
{
  @ApiProperty({
    description: 'The url to sign the document',
    type: String,
  })
  signingUrl: string;

  @ApiProperty({
    description: 'The document type',
    type: String,
    enum: SignDocumentDTOCodeEnum,
  })
  code: SignDocumentDTOCodeEnum;

  @ApiProperty({
    description: 'Is the document signed',
    type: Boolean,
  })
  signed: boolean;
}

class SubscriptionSignLoanAgreementActionDataDTO
  implements SignDocumentsDataDTO
{
  @ApiProperty({
    description: 'A list of documents to be signed',
    type: SubscriptionSignLoanAgreementActionSignDocumentDTO,
    isArray: true,
  })
  documents: SubscriptionSignLoanAgreementActionSignDocumentDTO[];
}

export class SubscriptionSignRewardsTOSActionDataDTO
  implements SignRewardsTOSActionDataDTO
{
  @ApiProperty({
    description: 'The revision of the T&C',
  })
  revision: string;
}

export class BaseSubscriptionActionDTO {
  @ApiProperty({
    description: 'ID of the subscription the action is associated with',
  })
  subscriptionId: string;

  @ApiProperty({
    description: 'The ID of the action',
  })
  id: string;

  @ApiProperty({
    description: 'Who is required to complete the action',
    enum: ActionOwner,
  })
  owner: ActionOwner;

  @ApiProperty({
    description: 'The status of the action',
    enum: ActionStatus,
  })
  status: ActionStatus;

  @ApiProperty({
    description: 'The type of action',
    enum: [
      HomeSurveyActionDTOTypeEnum,
      PayUpfrontFeeActionDTOTypeEnum,
      SignDocumentsActionDTOTypeEnum,
      SetupDirectDebitActionDTOTypeEnum,
      UpdateDirectDebitActionDTOTypeEnum,
      CheckAffordabilityActionDTOTypeEnum,
      UpdateAffordabilityActionDTOTypeEnum,
      InstallChargingStationActionDTOTypeEnum,
      SignRewardsTOSActionDTOTypeEnum,
      LinkExistingChargerActionDTOTypeEnum,
    ],
  })
  type:
    | HomeSurveyActionDTOTypeEnum
    | PayUpfrontFeeActionDTOTypeEnum
    | SignDocumentsActionDTOTypeEnum
    | SetupDirectDebitActionDTOTypeEnum
    | UpdateDirectDebitActionDTOTypeEnum
    | CheckAffordabilityActionDTOTypeEnum
    | UpdateAffordabilityActionDTOTypeEnum
    | InstallChargingStationActionDTOTypeEnum
    | SignRewardsTOSActionDTOTypeEnum
    | LinkExistingChargerActionDTOTypeEnum;

  @ApiProperty({
    description: 'IDs of actions which this action depends on',
    type: [String],
  })
  dependsOn: string[];
}

export class SubscriptionSurveyActionDTO
  extends BaseSubscriptionActionDTO
  implements HomeSurveyActionDTO
{
  @ApiProperty({
    enum: ['COMPLETE_HOME_SURVEY_V1'],
  })
  type: 'COMPLETE_HOME_SURVEY_V1';

  @ApiProperty({ type: SubscriptionSurveyActionDataDTO })
  data: SubscriptionSurveyActionDataDTO;
}

export class SubscriptionInstallChargingStationActionDTO
  extends BaseSubscriptionActionDTO
  implements InstallChargingStationActionDTO
{
  @ApiProperty({
    enum: ['INSTALL_CHARGING_STATION_V1'],
  })
  type: 'INSTALL_CHARGING_STATION_V1';

  @ApiProperty({ type: SubscriptionInstallChargingStationActionDataDTO })
  data: SubscriptionInstallChargingStationActionDataDTO;
}

export class SubscriptionCheckAffordabilityActionDTO
  extends BaseSubscriptionActionDTO
  implements CheckAffordabilityActionDTO
{
  @ApiProperty({
    enum: ['CHECK_AFFORDABILITY_V1'],
  })
  type: 'CHECK_AFFORDABILITY_V1';

  @ApiProperty({ type: SubscriptionCheckAffordabilityDataDTO })
  data: SubscriptionCheckAffordabilityDataDTO;
}

export class SubscriptionSetupDirectDebitActionDTO
  extends BaseSubscriptionActionDTO
  implements SetupDirectDebitActionDTOAxios
{
  @ApiProperty({
    enum: ['SETUP_DIRECT_DEBIT_V1'],
  })
  type: 'SETUP_DIRECT_DEBIT_V1';

  @ApiProperty({ type: Object })
  data: object;
}

export class SubscriptionSignDocumentsActionDTO
  extends BaseSubscriptionActionDTO
  implements SignDocumentsActionDTO
{
  @ApiProperty({
    enum: ['SIGN_DOCUMENTS_V1'],
  })
  type: 'SIGN_DOCUMENTS_V1';

  @ApiProperty({ type: SubscriptionSignLoanAgreementActionDataDTO })
  data: SubscriptionSignLoanAgreementActionDataDTO;
}

export class SubscriptionSignRewardsTOSActionDTO
  extends BaseSubscriptionActionDTO
  implements SignRewardsTOSActionDTO
{
  @ApiProperty({
    enum: ['SIGN_REWARDS_TOS_V1'],
  })
  type: 'SIGN_REWARDS_TOS_V1';

  @ApiProperty({ type: SubscriptionSignRewardsTOSActionDataDTO })
  data: SubscriptionSignRewardsTOSActionDataDTO;
}

class SubscriptionLinkExistingChargerActionDataDTO {
  @ApiProperty({
    type: String,
    nullable: true,
  })
  ppid: string | null;
}

export class SubscriptionLinkExistingChargerActionDTO
  extends BaseSubscriptionActionDTO
  implements LinkExistingChargerActionDTO
{
  @ApiProperty({
    enum: ['LINK_EXISTING_CHARGER_V1'],
  })
  type: 'LINK_EXISTING_CHARGER_V1';

  @ApiProperty({
    type: SubscriptionLinkExistingChargerActionDataDTO,
  })
  data: SubscriptionLinkExistingChargerActionDataDTO;
}

export class SubscriptionPayUpfrontFeeActionDTO
  extends BaseSubscriptionActionDTO
  implements PayUpfrontFeeActionDTO
{
  @ApiProperty({
    enum: ['PAY_UPFRONT_FEE_V1'],
  })
  type: 'PAY_UPFRONT_FEE_V1';

  @ApiProperty({ type: Object })
  data: object;
}

class SubscriptionOrderAddressDTO implements OrderAddressDTO {
  @ApiProperty({
    description: 'Line 1 of the address',
  })
  line1: string;

  @ApiProperty({
    description: 'Line 2 of the address',
    nullable: true,
  })
  line2: string | null;

  @ApiProperty({
    description: 'Line 3 of the address',
    nullable: true,
  })
  line3: string | null;

  @ApiProperty({
    description: 'Postcode of the address',
  })
  postcode: string;
}

class SubscriptionPodDrivePlanDTO implements PodDrivePlanDTO {
  @ApiProperty({
    description: 'The ID of the plan',
  })
  id: string;

  @ApiProperty({
    description: 'How many miles are allowed to be claimed',
  })
  allowanceMiles: number;

  @ApiProperty({
    description: 'How long the allowance period is',
    enum: PodDrivePlanDTOAllowancePeriodEnum,
  })
  allowancePeriod: PodDrivePlanDTOAllowancePeriodEnum;

  @ApiProperty({
    description: 'The upfront fee required for the plan (in GBP)',
  })
  upfrontFeePounds: number;

  @ApiProperty({
    description: 'The discounted upfront fee in GBP',
    example: 45,
    required: false,
  })
  discountedUpfrontFeePounds?: number;

  @ApiProperty({
    description: 'The monthly fee required for the plan (in GBP)',
  })
  monthlyFeePounds: number;

  @ApiProperty({
    description: 'The number of months the contract lasts',
  })
  contractDurationMonths: number;

  @ApiProperty({
    description: 'The product code of the plan',
  })
  productCode: string;

  @ApiProperty({
    description: 'The conversion rate between miles and KWH',
  })
  rateMilesPerKwh: number;

  @ApiProperty({
    description: 'The conversion rate between pence and miles',
  })
  ratePencePerMile: number;

  @ApiProperty({
    description: 'The type of plan',
    enum: PodDrivePlanDTOTypeEnum,
  })
  type: PodDrivePlanDTOTypeEnum;

  @ApiProperty({
    description: 'When the miles renew',
    type: 'string',
    format: 'date-time',
    nullable: true,
  })
  milesRenewalDate: string | null;
}

class SubscriptionPodDriveRewardsPlanDTO implements PodDriveRewardsPlanDTO {
  @ApiProperty({
    description: 'The ID of the plan',
  })
  id: string;

  @ApiProperty({
    description: 'How many miles are allowed to be claimed',
  })
  allowanceMiles: number;

  @ApiProperty({
    description: 'How long the allowance period is',
    enum: PodDrivePlanDTOAllowancePeriodEnum,
  })
  allowancePeriod: PodDrivePlanDTOAllowancePeriodEnum;

  @ApiProperty({
    description: 'The conversion rate between miles and KWH',
  })
  rateMilesPerKwh: number;

  @ApiProperty({
    description: 'The conversion rate between pence and miles',
  })
  ratePencePerMile: number;

  @ApiProperty({
    description: 'The type of plan',
    enum: PodDriveRewardsPlanDTOTypeEnum,
  })
  type: PodDriveRewardsPlanDTOTypeEnum;

  @ApiProperty({
    description: 'When the miles renew',
    type: 'string',
    format: 'date-time',
    nullable: true,
  })
  milesRenewalDate: string | null;
}

class SubscriptionSalesforceOrderDTO implements SalesforceOrderDTO {
  @ApiProperty({
    description: 'The ID of the order',
  })
  id: string;

  @ApiProperty({
    description: 'When the order was placed',
  })
  orderedAt: string;

  @ApiProperty({
    description: 'Where the order details originate from',
    enum: SalesforceOrderDTOOriginEnum,
  })
  origin: SalesforceOrderDTOOriginEnum;

  @ApiProperty({
    description: 'The address of the person who placed the address',
    type: SubscriptionOrderAddressDTO,
  })
  address: SubscriptionOrderAddressDTO;

  @ApiProperty({
    description: 'The email address of the person who placed the order',
  })
  email: string;

  @ApiProperty({
    description: 'The first name of the person who placed the order',
  })
  firstName: string;

  @ApiProperty({
    description: 'The last name of the person who placed the order',
  })
  lastName: string;

  @ApiProperty({
    description: "The person who placed the order's MPAN",
  })
  mpan: string;

  @ApiProperty({
    description: 'The phone number of the person who placed the order',
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'The eCommerceId associated with the order',
  })
  eCommerceId: string;
}

class SubscriptionSelfServiceOrderDTO implements SelfServiceOrderDTO {
  @ApiProperty({
    description: 'The ID of the order',
  })
  id: string;

  @ApiProperty({
    description: 'When the order was placed',
  })
  orderedAt: string;

  @ApiProperty({
    description: 'Where the order details originate from',
    enum: SelfServiceOrderDTOOriginEnum,
  })
  origin: SelfServiceOrderDTOOriginEnum;

  @ApiProperty({
    description: 'The PPID of the charger related to the subscription',
  })
  ppid: string;
}

@ApiExtraModels(
  SubscriptionSurveyActionDTO,
  SubscriptionInstallChargingStationActionDTO,
  SubscriptionCheckAffordabilityActionDTO,
  SubscriptionSetupDirectDebitActionDTO,
  SubscriptionSignDocumentsActionDTO,
  SubscriptionPayUpfrontFeeActionDTO,
  SubscriptionSalesforceOrderDTO,
  SubscriptionSelfServiceOrderDTO,
  SubscriptionPodDrivePlanDTO,
  SubscriptionPodDriveRewardsPlanDTO,
  SubscriptionLinkExistingChargerActionDTO,
  SubscriptionSignRewardsTOSActionDTO
)
export class SubscriptionDTO
  implements Omit<PersistedSubscriptionDTO, 'userId'>
{
  @ApiProperty({
    oneOf: [
      { $ref: getSchemaPath(SubscriptionSurveyActionDTO) },
      { $ref: getSchemaPath(SubscriptionInstallChargingStationActionDTO) },
      { $ref: getSchemaPath(SubscriptionCheckAffordabilityActionDTO) },
      { $ref: getSchemaPath(SubscriptionSetupDirectDebitActionDTO) },
      { $ref: getSchemaPath(SubscriptionSignDocumentsActionDTO) },
      { $ref: getSchemaPath(SubscriptionPayUpfrontFeeActionDTO) },
      { $ref: getSchemaPath(SubscriptionLinkExistingChargerActionDTO) },
      { $ref: getSchemaPath(SubscriptionSignRewardsTOSActionDTO) },
    ],
  })
  actions: PersistedSubscriptionDTOActionsInner[];

  @ApiProperty({
    description: 'When the subscription was created',
    format: 'date-time',
  })
  createdAt: string;

  @ApiProperty({
    description: 'When the subscription was deleted',
    nullable: true,
    format: 'date-time',
  })
  deletedAt: string | null;

  @ApiProperty({
    description: 'When the subscription was activated',
    nullable: true,
    format: 'date-time',
  })
  activatedAt: string | null;

  @ApiProperty({
    description: 'The ID of the subscription',
  })
  id: string;

  @ApiProperty({
    oneOf: [
      { $ref: getSchemaPath(SubscriptionSalesforceOrderDTO) },
      { $ref: getSchemaPath(SubscriptionSelfServiceOrderDTO) },
    ],
  })
  order: SubscriptionSalesforceOrderDTO | SubscriptionSelfServiceOrderDTO;

  @ApiProperty({
    oneOf: [
      { $ref: getSchemaPath(SubscriptionPodDrivePlanDTO) },
      { $ref: getSchemaPath(SubscriptionPodDriveRewardsPlanDTO) },
    ],
  })
  plan: SubscriptionPodDrivePlanDTO | SubscriptionPodDriveRewardsPlanDTO;

  @ApiProperty({
    description: 'The status of the subscription',
    enum: PersistedSubscriptionDTOStatusEnum,
  })
  status: PersistedSubscriptionDTOStatusEnum;

  @ApiProperty({
    description: 'When the subscription was last updated',
    format: 'date-time',
  })
  updatedAt: string;
}

export class SubscriptionListDTO {
  @ApiProperty({
    type: [SubscriptionDTO],
    description: 'An array of subscriptions associated with the current user',
  })
  data: SubscriptionDTO[];
}

export class SubscriptionDocumentDTO {
  @ApiProperty({
    description: 'When the subscription was last updated',
    format: 'date-time',
    example: '2025-05-28T18:28:21.959Z',
  })
  issued: string;

  @ApiProperty({
    description: 'URL to the document',
    example:
      '/subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48',
  })
  link: string;

  @ApiProperty({
    description: 'Format of the document',
    example: 'PDF',
  })
  format: string;

  @ApiProperty({
    description: 'If this document is active',
    type: Boolean,
    example: true,
  })
  active: boolean;

  @ApiProperty({
    description: 'The type of document',
    enum: SubscriptionDocumentDTOTypeEnum,
  })
  type: SubscriptionDocumentDTOTypeEnum;
}

export class SubscriptionDocumentsDTO {
  @ApiProperty({
    type: [SubscriptionDocumentDTO],
    description: 'An array of documents associated with the subscription',
  })
  documents: SubscriptionDocumentDTO[];
}

export class SubscriptionDirectDebitDTO {
  @ApiProperty({
    description: 'The last 4 digits of the account number',
    example: '1234',
  })
  accountNumberLastDigits: string;

  @ApiProperty({
    description: 'The last 2 digits of the sort code',
    example: '23',
  })
  sortCodeLastDigits: string;

  @ApiProperty({
    description: 'The name associated with the direct debit',
    example: 'Mr Tom Wallace',
  })
  nameOnAccount: string;

  @ApiProperty({
    description: 'The day of the month in which payment is due',
    example: 13,
  })
  monthlyPaymentDay: number;
}

export class SubscriptionConfirmationOfPayeeDTO {
  @ApiProperty({
    description: "The account holder's name",
    example: 'Mr John Smith',
  })
  @IsString()
  accountName: string;

  @ApiProperty({
    description: 'The bank’s sort code',
    example: '123456',
  })
  @IsString()
  @Matches(/^[0-9]{1,6}$/)
  sortCode: string;

  @ApiProperty({
    description: 'The bank account number',
    example: '********',
  })
  @IsString()
  @Matches(/^[0-9]{1,8}$/)
  accountNumber: string;
}

export class SubscriptionConfirmationOfPayeeResponse {
  @ApiProperty({
    description: 'The outcome of the check',
    enum: ['matched', 'partial_match', 'not_matched'],
  })
  status: 'matched' | 'partial_match' | 'not_matched';

  @ApiProperty({
    description: 'Provides context to the status of the check',
    required: false,
    externalDocs: {
      url: 'https://docs.acquired.com/docs/confirmation-of-payee-cop#reason-values',
      description: 'Acquired COP Reason Values',
    },
  })
  reason?: string;

  @ApiProperty({
    description: "The account holder's name",
    example: 'Mr John Smith',
  })
  accountName: string;
}

export interface AcquiredCOPResponse {
  id: string;
  status: 'matched' | 'partial_match' | 'not_matched';
  reason?: string;
  account_name: string;
}

export interface AcquiredCOPValidationResponse {
  status: 'error';
  invalid_parameters: {
    parameter: string;
    reason: string;
  }[];
}

export class SetupDirectDebitActionDataDTO {
  @ApiProperty({
    name: 'accountNumber',
    description: 'Bank account number of the customer',
    type: 'string',
    example: '********',
  })
  @IsString()
  @IsNotEmpty()
  accountNumber: string;

  @ApiProperty({
    name: 'sortCode',
    description: 'Sort code of the customer’s bank',
    type: 'string',
    example: '123456',
  })
  @IsString()
  @IsNotEmpty()
  sortCode: string;

  @ApiProperty({
    name: 'accountName',
    description: 'Name of account holder',
    type: 'string',
    example: 'Joe Bloggs',
  })
  @IsString()
  @IsNotEmpty()
  accountName: string;

  @ApiProperty({
    name: 'requiresMoreThanOneSignatory',
    description: 'Request is from either one or more authorised signatory',
    type: 'boolean',
  })
  @IsBoolean()
  requiresMoreThanOneSignatory: boolean;

  @ApiProperty({
    name: 'understandsDirectDebitGuarantee',
    description: 'The account holder understands the direct debit guarantee',
    type: 'boolean',
  })
  @IsBoolean()
  understandsDirectDebitGuarantee: boolean;
}

export class AffordabilityActionDataBillingAddressDTO {
  @ApiProperty({
    type: String,
    required: false,
  })
  flat?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  number?: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  name?: string;

  @ApiProperty({
    type: String,
  })
  street: string;

  @ApiProperty({
    type: String,
  })
  town: string;

  @ApiProperty({
    type: String,
    required: false,
  })
  county?: string;

  @ApiProperty({
    type: String,
  })
  postcode: string;
}

export class AffordabilityActionDataDTO {
  @ApiProperty({
    description: 'Title of the customer',
    enum: UpdateCheckAffordabilityDataDTOTitleEnum,
  })
  title: UpdateCheckAffordabilityDataDTOTitleEnum;

  @ApiProperty({
    name: 'firstName',
    description: 'First name of the customer',
    type: 'string',
    example: 'John',
  })
  firstName: string;

  @ApiProperty({
    required: false,
  })
  middleNames?: string;

  @ApiProperty({
    name: 'lastName',
    description: 'Last name of the customer',
    type: 'string',
    example: 'Smith',
  })
  lastName: string;

  @ApiProperty({
    name: 'dateOfBirth',
    description: 'Date of birth of the customer',
    type: 'string',
    format: 'date',
    example: '2025-01-01',
  })
  dateOfBirth: string;

  @ApiProperty({
    description: 'Email address of the customer',
    type: 'string',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Telephone number of the customer',
    type: 'string',
    example: '+447234567890',
  })
  phoneNumber: string;

  @ApiProperty({
    enum: UpdateCheckAffordabilityDataDTOMaritalStatusEnum,
  })
  maritalStatus: UpdateCheckAffordabilityDataDTOMaritalStatusEnum;

  @ApiProperty({
    enum: UpdateCheckAffordabilityDataDTOResidentialStatusEnum,
  })
  residentialStatus: UpdateCheckAffordabilityDataDTOResidentialStatusEnum;

  @ApiProperty({
    enum: UpdateCheckAffordabilityDataDTODependenciesEnum,
  })
  dependencies: UpdateCheckAffordabilityDataDTODependenciesEnum;

  @ApiProperty({
    enum: UpdateCheckAffordabilityDataDTOEmploymentStatusEnum,
  })
  employmentStatus: UpdateCheckAffordabilityDataDTOEmploymentStatusEnum;

  @ApiProperty({
    type: 'number',
    example: 1000,
  })
  monthlyTakeHomePay: number;

  @ApiProperty({
    type: 'number',
    example: 500,
  })
  monthlyHousePayments: number;

  @ApiProperty({
    type: 'number',
    example: 200,
  })
  monthlyTravelAndLivingExpenses: number;

  @ApiProperty({
    type: 'number',
    example: 300,
  })
  monthlyHouseholdExpenses: number;

  @ApiProperty({
    type: 'number',
    example: 100,
  })
  monthlyCreditPayments: number;

  @ApiProperty({
    enum: UpdateCheckAffordabilityDataDTOCircumstancesRequireSupportEnum,
  })
  circumstancesRequireSupport: UpdateCheckAffordabilityDataDTOCircumstancesRequireSupportEnum;

  @ApiProperty({
    type: AffordabilityActionDataBillingAddressDTO,
    description:
      "At least one of billingAddress' flat, number or name fields must be provided",
  })
  billingAddress: AffordabilityActionDataBillingAddressDTO;
}

export class AffordabilityActionDTO {
  @ApiProperty({
    enum: ['CHECK_AFFORDABILITY_V1'],
  })
  type: 'CHECK_AFFORDABILITY_V1';

  @ApiProperty({
    type: AffordabilityActionDataDTO,
  })
  data: AffordabilityActionDataDTO;
}

export class SetupDirectDebitActionDTO {
  @ApiProperty({
    enum: ['SETUP_DIRECT_DEBIT_V1'],
  })
  type: 'SETUP_DIRECT_DEBIT_V1';

  @ApiProperty({
    type: SetupDirectDebitActionDataDTO,
  })
  data: SetupDirectDebitActionDataDTO;
}

export class UpdateHomeSurveyActionDTO {
  @ApiProperty({
    enum: ['COMPLETE_HOME_SURVEY_V1'],
  })
  type: 'COMPLETE_HOME_SURVEY_V1';

  @ApiProperty({
    type: Object,
    required: false,
  })
  data: object;
}

export class UpdateSignDocumentsActionDTO {
  @ApiProperty({
    enum: ['SIGN_DOCUMENTS_V1'],
  })
  type: 'SIGN_DOCUMENTS_V1';

  @ApiProperty({
    type: Object,
    required: false,
  })
  data: object;
}

export class UpdateSignRewardsTOSActionDataDTO {
  @ApiProperty({
    description: 'The revision of the T&C signed',
  })
  revision: string;
}

export class UpdateSignRewardsTOSActionDTO {
  @ApiProperty({
    enum: ['SIGN_REWARDS_TOS_V1'],
  })
  type: 'SIGN_REWARDS_TOS_V1';

  @ApiProperty({
    type: UpdateSignRewardsTOSActionDataDTO,
  })
  data: UpdateSignRewardsTOSActionDataDTO;
}

export class UpdateLinkExistingChargerActionDTO {
  @ApiProperty({
    enum: ['LINK_EXISTING_CHARGER_V1'],
  })
  type: 'LINK_EXISTING_CHARGER_V1';

  @ApiProperty({
    type: Object,
    required: false,
  })
  data: object;
}

export type ActionUpdateDTO =
  | SetupDirectDebitActionDTO
  | AffordabilityActionDTO
  | UpdateHomeSurveyActionDTO
  | UpdateSignDocumentsActionDTO
  | UpdateSignRewardsTOSActionDTO
  | UpdateLinkExistingChargerActionDTO;

export class FieldHelpDTO {
  @ApiProperty({
    required: false,
  })
  title?: string;

  @ApiProperty({
    required: false,
  })
  content?: string;
}
export class FieldValueDTO {
  @ApiProperty({
    required: false,
  })
  id?: string;

  @ApiProperty({
    required: false,
  })
  label?: string;

  @ApiProperty()
  value: string;
}
export class FieldValidatorDTO {
  @ApiProperty()
  type: string;
}
export class FormFieldDTO {
  @ApiProperty()
  type: string;

  @ApiProperty()
  label: string;

  @ApiProperty({
    type: FieldValidatorDTO,
    isArray: true,
    required: false,
  })
  validators?: FieldValidatorDTO[];

  @ApiProperty({
    required: false,
  })
  id?: string;
  @ApiProperty({
    required: false,
    type: FieldValueDTO,
    isArray: true,
  })
  values?: FieldValueDTO[];

  @ApiProperty({
    required: false,
    type: FieldHelpDTO,
  })
  help?: FieldHelpDTO;
}
export class FormSectionDTO {
  @ApiProperty()
  title: string;

  @ApiProperty({
    type: FormFieldDTO,
    isArray: true,
  })
  fields: FormFieldDTO[];
}
export class FormDataDTO {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({
    type: FormSectionDTO,
    isArray: true,
  })
  sections: FormSectionDTO[];
}
