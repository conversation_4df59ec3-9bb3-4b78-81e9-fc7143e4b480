package full

import (
	"experience/apps/data-platform/api-e2e/pkg/infra"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/shared/go/service/utils"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func TestAuditWalletCharge(t *testing.T) {
	ctx := t.Context()
	defer fixtures.CleanupAllTestData(ctx, t, testQueries, nil, nil, nil)
	unit, _, location, _ := fixtures.PrepareChargeData(ctx, t, testQueries, false, true)

	charge, err := testQueries.CreateCharge(ctx, fixtures.CreateChargeParamsNoBillingEvent(0, &location, &unit, 150, "1.5", ptr.To(time.Now()), ptr.To(time.Now())))
	assert.NoError(t, err)

	uuid := utils.FabricateUUIDFromNumericID(int(charge.ID))

	completedEvent := random.ChargeCompletedEvent(&random.ChargeCompletedEventParams{
		AggregateID: uuid,
		ChargeID:    int(charge.ID),
		LocationID:  int(location.ID),
	})
	err = seeding.Event(ctx, t, chargeNotificationSQSClient, completedEvent)
	defer infra.CleanupEventData(ctx, t, testQueries, completedEvent.AggregateID)
	require.NoError(t, err)

	done := make(chan bool)
	go func() {
		match := false
		for !match {
			auditableCharge, err := testQueries.FindAuditedChargeProjectionByChargeUUID(ctx, uuid)
			if err == nil {
				match = auditableCharge.AggregateID == completedEvent.AggregateID
			}
			time.Sleep(time.Millisecond * 100)
		}
		done <- true
	}()

	timeout := time.NewTimer(defaultTestTimeout)
	select {
	case <-timeout.C:
		t.Fatal("test didn't finish in time")
	case <-done:
	}
}
