package full

import (
	"context"
	"experience/apps/data-platform/api-e2e/pkg/infra"
	chargestatistics "experience/libs/data-platform/api/contract/gen/charge_statistics"
	"experience/libs/data-platform/api/contract/gen/http/charge_statistics/server"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	"experience/libs/shared/go/service/utils"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"

	"github.com/google/uuid"
)

// This test seeds 10 charges in total for the month of July. All 10 charges
// belong to the same group, but are split equally across 2 sites. All charges
// of the first site are confirmed and have a settlement amount; all charges of
// the second site are unconfirmed and don't have a settlement amount.
func TestGroupChargeStatistics(t *testing.T) {
	baseURL := setup.BaseURL(t, host)
	groupUID := uuid.New().String()
	cleanup := seedChargeProjectionsForGroupChargeStatistics(t, groupUID)
	defer cleanup()

	requestURL := fmt.Sprintf(
		"%s/groups/%s/charge-statistics?from=%s&to=%s",
		baseURL,
		groupUID,
		"2024-07-01",
		"2024-07-31",
	)

	expectedResponse := chargestatistics.ProjectiongroupChargeStatisticsResponse{
		Data: &chargestatistics.GroupChargeStatistics{
			ChargingDuration: 6895,
			Co2Savings:       27.44,
			Energy: &chargestatistics.EnergyStatistics{
				TotalUsage:                    49,
				ClaimedUsage:                  18.5,
				RevenueGeneratingClaimedUsage: 18.5,
				UnclaimedUsage:                30.5,
				Cost:                          260,
			},
			NumberOfCharges:  10,
			NumberOfUsers:    10,
			RevenueGenerated: seeding.FixedRate * 5,
			NumberOfChargers: 2,
			NumberOfSites:    10,
		},
		Meta: &chargestatistics.Meta{
			Params: map[string]any{
				"From":    "2024-07-01",
				"GroupID": groupUID,
				"To":      "2024-07-31",
			},
		},
	}

	sendRequestAndCompareResponse(t, requestURL, http.MethodGet, http.NoBody, expectedResponse)
}

func TestSiteChargeStatistics(t *testing.T) {
	baseURL := setup.BaseURL(t, host)

	siteID := int(fixtures.Sequence.Get())
	siteUUID := utils.FabricateUUIDFromNumericID(siteID).String()

	cleanup := seedChargeProjectionsForSiteChargeStatistics(t, int64(siteID))
	defer cleanup()

	expectedResponse := chargestatistics.ProjectionsiteChargeStatisticsResponse{Data: &chargestatistics.SiteChargeStatistics{
		NumberOfChargers: 6,
		ChargingDuration: 6895,
		Co2Savings:       27.44,
		Energy: &chargestatistics.EnergyStatistics{
			TotalUsage:                    49,
			ClaimedUsage:                  18.5,
			RevenueGeneratingClaimedUsage: 18.5,
			UnclaimedUsage:                30.5,
			Cost:                          260,
		},
		NumberOfCharges:  10,
		NumberOfUsers:    1,
		RevenueGenerated: seeding.FixedRate * 5,
	},
		Meta: &chargestatistics.Meta{
			Params: map[string]interface{}{
				"From":   "2024-07-01",
				"To":     "2024-07-31",
				"SiteID": siteUUID,
			},
		},
	}

	requestURL := fmt.Sprintf("%s/sites/%s/charge-statistics?from=%s&to=%s", baseURL, siteUUID, "2024-07-01", "2024-07-31")

	sendRequestAndCompareResponse(t, requestURL, http.MethodGet, http.NoBody, expectedResponse)
}

// This test seeds 10 charges for the same site and group. Out of these, 5 are
// confirmed and have a settlement of 11 each. The other 5 are not confirmed and
// have no settlement amount.
func TestChargerChargeStatistics(t *testing.T) {
	baseURL := setup.BaseURL(t, host)

	chargerPPID := "PG-58792"

	cleanup := seedChargeProjectionsForChargerChargeStatistics(t, chargerPPID)
	defer cleanup()

	expectedResponse := chargestatistics.ProjectionchargerChargeStatisticsResponse{Data: &chargestatistics.ChargerChargeStatistics{
		NumberOfCharges:  10,
		ChargingDuration: 6895,
		Co2Savings:       27.44,
		Energy: &chargestatistics.EnergyStatistics{
			TotalUsage:                    49,
			ClaimedUsage:                  18.5,
			RevenueGeneratingClaimedUsage: 18.5,
			UnclaimedUsage:                30.5,
			Cost:                          260,
		},
		NumberOfUsers:    2,
		RevenueGenerated: seeding.FixedRate * 5,
	},
		Meta: &chargestatistics.Meta{
			Params: map[string]interface{}{
				"From":      "2024-07-01",
				"To":        "2024-07-31",
				"ChargerID": chargerPPID,
			},
		},
	}

	requestURL := fmt.Sprintf("%s/chargers/%s/charge-statistics?from=%s&to=%s", baseURL, chargerPPID, "2024-07-01", "2024-07-31")

	sendRequestAndCompareResponse(t, requestURL, http.MethodGet, http.NoBody, expectedResponse)
}

func TestGroupUsageSummaries(t *testing.T) {
	baseURL := setup.BaseURL(t, host)

	groupUUID := uuid.New().String()
	from := "2024-07-01"
	to := "2024-07-31"

	cleanup := seedChargeProjectionsForGroupUsageSummaries(t, groupUUID)
	defer cleanup()

	tests := []struct {
		name             string
		interval         string
		expectedResponse server.GroupUsageSummariesResponseBody
	}{
		{
			name:     "group usage by day",
			interval: "day",
			expectedResponse: server.GroupUsageSummariesResponseBody{
				Usage: []*server.UsageResponseBody{
					{
						IntervalStartDate: "2024-07-01",
						TotalUsage:        9.8,
						Cost:              52,
						Co2Savings:        5.49,
						RevenueGenerated:  seeding.FixedRate * 9,
					},
					emptyUsageIntervalWithDate("2024-07-02"),
					emptyUsageIntervalWithDate("2024-07-03"),
					emptyUsageIntervalWithDate("2024-07-04"),
					emptyUsageIntervalWithDate("2024-07-05"),
					emptyUsageIntervalWithDate("2024-07-06"),
					emptyUsageIntervalWithDate("2024-07-07"),
					emptyUsageIntervalWithDate("2024-07-08"),
					emptyUsageIntervalWithDate("2024-07-09"),
					emptyUsageIntervalWithDate("2024-07-10"),
					emptyUsageIntervalWithDate("2024-07-11"),
					emptyUsageIntervalWithDate("2024-07-12"),
					emptyUsageIntervalWithDate("2024-07-13"),
					emptyUsageIntervalWithDate("2024-07-14"),
					{
						IntervalStartDate: "2024-07-15",
						TotalUsage:        9.8,
						Cost:              52,
						Co2Savings:        5.49,
						RevenueGenerated:  seeding.FixedRate * 9,
					},
					emptyUsageIntervalWithDate("2024-07-16"),
					emptyUsageIntervalWithDate("2024-07-17"),
					emptyUsageIntervalWithDate("2024-07-18"),
					emptyUsageIntervalWithDate("2024-07-19"),
					emptyUsageIntervalWithDate("2024-07-20"),
					emptyUsageIntervalWithDate("2024-07-21"),
					emptyUsageIntervalWithDate("2024-07-22"),
					emptyUsageIntervalWithDate("2024-07-23"),
					{
						IntervalStartDate: "2024-07-24",
						TotalUsage:        9.8,
						Cost:              52,
						Co2Savings:        5.49,
						RevenueGenerated:  seeding.FixedRate * 9,
					},
					emptyUsageIntervalWithDate("2024-07-25"),
					emptyUsageIntervalWithDate("2024-07-26"),
					{
						IntervalStartDate: "2024-07-27",
						TotalUsage:        9.8,
						Cost:              52,
						Co2Savings:        5.49,
						RevenueGenerated:  seeding.FixedRate * 9,
					},
					emptyUsageIntervalWithDate("2024-07-28"),
					emptyUsageIntervalWithDate("2024-07-29"),
					emptyUsageIntervalWithDate("2024-07-30"),
					{
						IntervalStartDate: "2024-07-31",
						TotalUsage:        9.8,
						Cost:              52,
						Co2Savings:        5.49,
						RevenueGenerated:  seeding.FixedRate * 9,
					},
				},
				Meta: &server.MetaResponseBody{
					Params: map[string]interface{}{
						"GroupID":  groupUUID,
						"Interval": "day",
						"From":     from,
						"To":       to,
					},
				},
			},
		},
		{
			name:     "group usage by week",
			interval: "week",
			expectedResponse: server.GroupUsageSummariesResponseBody{
				Usage: []*server.UsageResponseBody{
					{
						IntervalStartDate: "2024-07-01",
						TotalUsage:        9.8,
						Cost:              52,
						Co2Savings:        5.49,
						RevenueGenerated:  seeding.FixedRate * 9,
					},
					emptyUsageIntervalWithDate("2024-07-08"),
					{
						IntervalStartDate: "2024-07-15",
						TotalUsage:        9.8,
						Cost:              52,
						Co2Savings:        5.49,
						RevenueGenerated:  seeding.FixedRate * 9,
					},
					{
						IntervalStartDate: "2024-07-22",
						TotalUsage:        19.6,
						Cost:              104,
						Co2Savings:        10.98,
						RevenueGenerated:  198,
					},
					{
						IntervalStartDate: "2024-07-29",
						TotalUsage:        9.8,
						Cost:              52,
						Co2Savings:        5.49,
						RevenueGenerated:  seeding.FixedRate * 9,
					},
				},
				Meta: &server.MetaResponseBody{
					Params: map[string]interface{}{
						"GroupID":  groupUUID,
						"Interval": "week",
						"From":     from,
						"To":       to,
					},
				},
			},
		},
		{
			name:     "group usage by month",
			interval: "month",
			expectedResponse: server.GroupUsageSummariesResponseBody{
				Usage: []*server.UsageResponseBody{
					{
						IntervalStartDate: "2024-07-01",
						TotalUsage:        49,
						Cost:              260,
						Co2Savings:        27.44,
						RevenueGenerated:  495,
					},
				},
				Meta: &server.MetaResponseBody{
					Params: map[string]interface{}{
						"GroupID":  groupUUID,
						"Interval": "month",
						"From":     from,
						"To":       to,
					},
				},
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			sendRequestAndCompareResponse(
				t,
				fmt.Sprintf("%s/groups/%s/charge-statistics/%s?from=%s&to=%s", baseURL, groupUUID, test.interval, "2024-07-01", "2024-07-31"),
				http.MethodGet,
				http.NoBody,
				test.expectedResponse,
			)
		})
	}
}

func TestGroupAndSiteUsageSummaries(t *testing.T) {
	baseURL := setup.BaseURL(t, host)

	groupUUID := uuid.New().String()
	siteID := fixtures.Sequence.Get()
	siteUUID := utils.FabricateUUIDFromNumericID(int(siteID)).String()

	cleanup := seedChargeProjectionsForGroupAndSiteUsageSummaries(t, groupUUID, siteID)
	defer cleanup()

	tests := []struct {
		name             string
		interval         string
		expectedResponse server.GroupAndSiteUsageSummariesResponseBody
	}{
		{
			name:     "group and site usage by day",
			interval: "day",
			expectedResponse: server.GroupAndSiteUsageSummariesResponseBody{
				Usage: []*server.UsageResponseBody{
					{
						IntervalStartDate: "2024-07-01",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					emptyUsageIntervalWithDate("2024-07-02"),
					emptyUsageIntervalWithDate("2024-07-03"),
					emptyUsageIntervalWithDate("2024-07-04"),
					emptyUsageIntervalWithDate("2024-07-05"),
					emptyUsageIntervalWithDate("2024-07-06"),
					emptyUsageIntervalWithDate("2024-07-07"),
					emptyUsageIntervalWithDate("2024-07-08"),
					emptyUsageIntervalWithDate("2024-07-09"),
					emptyUsageIntervalWithDate("2024-07-10"),
					emptyUsageIntervalWithDate("2024-07-11"),
					emptyUsageIntervalWithDate("2024-07-12"),
					emptyUsageIntervalWithDate("2024-07-13"),
					emptyUsageIntervalWithDate("2024-07-14"),
					{
						IntervalStartDate: "2024-07-15",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					emptyUsageIntervalWithDate("2024-07-16"),
					emptyUsageIntervalWithDate("2024-07-17"),
					emptyUsageIntervalWithDate("2024-07-18"),
					emptyUsageIntervalWithDate("2024-07-19"),
					emptyUsageIntervalWithDate("2024-07-20"),
					emptyUsageIntervalWithDate("2024-07-21"),
					emptyUsageIntervalWithDate("2024-07-22"),
					emptyUsageIntervalWithDate("2024-07-23"),
					{
						IntervalStartDate: "2024-07-24",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					emptyUsageIntervalWithDate("2024-07-25"),
					emptyUsageIntervalWithDate("2024-07-26"),
					{
						IntervalStartDate: "2024-07-27",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					emptyUsageIntervalWithDate("2024-07-28"),
					emptyUsageIntervalWithDate("2024-07-29"),
					emptyUsageIntervalWithDate("2024-07-30"),
					{
						IntervalStartDate: "2024-07-31",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
				},
				Meta: &server.MetaResponseBody{
					Params: map[string]interface{}{
						"From":     "2024-07-01",
						"To":       "2024-07-31",
						"GroupID":  groupUUID,
						"SiteID":   siteUUID,
						"Interval": "day",
					},
				},
			},
		},
		{
			name:     "group and site usage by week",
			interval: "week",
			expectedResponse: server.GroupAndSiteUsageSummariesResponseBody{
				Usage: []*server.UsageResponseBody{
					{
						IntervalStartDate: "2024-07-01",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					emptyUsageIntervalWithDate("2024-07-08"),
					{
						IntervalStartDate: "2024-07-15",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					{
						IntervalStartDate: "2024-07-22",
						TotalUsage:        19.6,
						Co2Savings:        10.98,
						RevenueGenerated:  84,
						Cost:              104,
					},
					{
						IntervalStartDate: "2024-07-29",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
				},
				Meta: &server.MetaResponseBody{
					Params: map[string]interface{}{
						"From":     "2024-07-01",
						"To":       "2024-07-31",
						"GroupID":  groupUUID,
						"SiteID":   siteUUID,
						"Interval": "week",
					},
				},
			},
		},
		{
			name:     "group and site usage by month",
			interval: "month",
			expectedResponse: server.GroupAndSiteUsageSummariesResponseBody{
				Usage: []*server.UsageResponseBody{
					{
						IntervalStartDate: "2024-07-01",
						TotalUsage:        49,
						Co2Savings:        27.44,
						RevenueGenerated:  210,
						Cost:              260,
					},
				},
				Meta: &server.MetaResponseBody{
					Params: map[string]interface{}{
						"From":     "2024-07-01",
						"To":       "2024-07-31",
						"GroupID":  groupUUID,
						"SiteID":   siteUUID,
						"Interval": "month",
					},
				},
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			sendRequestAndCompareResponse(
				t,
				fmt.Sprintf("%s/groups/%s/sites/%s/charge-statistics/%s?from=%s&to=%s", baseURL, groupUUID, siteUUID, test.interval, "2024-07-01", "2024-07-31"),
				http.MethodGet,
				http.NoBody,
				test.expectedResponse,
			)
		})
	}
}

func TestGroupAndChargerUsageSummaries(t *testing.T) {
	baseURL := setup.BaseURL(t, host)

	groupUUID := uuid.New().String()
	chargerID := "PSL-1337"

	cleanup := seedChargeProjectionsForGroupAndChargerUsageSummaries(t, groupUUID, chargerID)
	defer cleanup()

	tests := []struct {
		name             string
		interval         string
		expectedResponse server.GroupAndChargerUsageSummariesResponseBody
	}{
		{
			name:     "group and charger usage by day",
			interval: "day",
			expectedResponse: server.GroupAndChargerUsageSummariesResponseBody{
				Usage: []*server.UsageResponseBody{
					{
						IntervalStartDate: "2024-07-01",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					emptyUsageIntervalWithDate("2024-07-02"),
					emptyUsageIntervalWithDate("2024-07-03"),
					emptyUsageIntervalWithDate("2024-07-04"),
					emptyUsageIntervalWithDate("2024-07-05"),
					emptyUsageIntervalWithDate("2024-07-06"),
					emptyUsageIntervalWithDate("2024-07-07"),
					emptyUsageIntervalWithDate("2024-07-08"),
					emptyUsageIntervalWithDate("2024-07-09"),
					emptyUsageIntervalWithDate("2024-07-10"),
					emptyUsageIntervalWithDate("2024-07-11"),
					emptyUsageIntervalWithDate("2024-07-12"),
					emptyUsageIntervalWithDate("2024-07-13"),
					emptyUsageIntervalWithDate("2024-07-14"),
					{
						IntervalStartDate: "2024-07-15",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					emptyUsageIntervalWithDate("2024-07-16"),
					emptyUsageIntervalWithDate("2024-07-17"),
					emptyUsageIntervalWithDate("2024-07-18"),
					emptyUsageIntervalWithDate("2024-07-19"),
					emptyUsageIntervalWithDate("2024-07-20"),
					emptyUsageIntervalWithDate("2024-07-21"),
					emptyUsageIntervalWithDate("2024-07-22"),
					emptyUsageIntervalWithDate("2024-07-23"),
					{
						IntervalStartDate: "2024-07-24",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					emptyUsageIntervalWithDate("2024-07-25"),
					emptyUsageIntervalWithDate("2024-07-26"),
					{
						IntervalStartDate: "2024-07-27",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					emptyUsageIntervalWithDate("2024-07-28"),
					emptyUsageIntervalWithDate("2024-07-29"),
					emptyUsageIntervalWithDate("2024-07-30"),
					{
						IntervalStartDate: "2024-07-31",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
				},
				Meta: &server.MetaResponseBody{
					Params: map[string]interface{}{
						"From":      "2024-07-01",
						"To":        "2024-07-31",
						"GroupID":   groupUUID,
						"ChargerID": chargerID,
						"Interval":  "day",
					},
				},
			},
		},
		{
			name:     "group and charger usage by week",
			interval: "week",
			expectedResponse: server.GroupAndChargerUsageSummariesResponseBody{
				Usage: []*server.UsageResponseBody{
					{
						IntervalStartDate: "2024-07-01",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					emptyUsageIntervalWithDate("2024-07-08"),
					{
						IntervalStartDate: "2024-07-15",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
					{
						IntervalStartDate: "2024-07-22",
						TotalUsage:        19.6,
						Co2Savings:        10.98,
						RevenueGenerated:  84,
						Cost:              104,
					},
					{
						IntervalStartDate: "2024-07-29",
						TotalUsage:        9.8,
						Co2Savings:        5.49,
						RevenueGenerated:  42,
						Cost:              52,
					},
				},
				Meta: &server.MetaResponseBody{
					Params: map[string]interface{}{
						"From":      "2024-07-01",
						"To":        "2024-07-31",
						"GroupID":   groupUUID,
						"ChargerID": chargerID,
						"Interval":  "week",
					},
				},
			},
		},
		{
			name:     "group and charger usage by month",
			interval: "month",
			expectedResponse: server.GroupAndChargerUsageSummariesResponseBody{
				Usage: []*server.UsageResponseBody{
					{
						IntervalStartDate: "2024-07-01",
						TotalUsage:        49,
						Co2Savings:        27.44,
						RevenueGenerated:  210,
						Cost:              260,
					},
				},
				Meta: &server.MetaResponseBody{
					Params: map[string]interface{}{
						"From":      "2024-07-01",
						"To":        "2024-07-31",
						"GroupID":   groupUUID,
						"ChargerID": chargerID,
						"Interval":  "month",
					},
				},
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			sendRequestAndCompareResponse(
				t,
				fmt.Sprintf("%s/groups/%s/chargers/%s/charge-statistics/%s?from=%s&to=%s", baseURL, groupUUID, chargerID, test.interval, "2024-07-01", "2024-07-31"),
				http.MethodGet,
				http.NoBody,
				test.expectedResponse,
			)
		})
	}
}

func seedChargeProjectionsForGroupChargeStatistics(t *testing.T, groupUID string) func() {
	t.Helper()
	ctx := context.Background()

	daysOfMonth := []int{1, 15, 24, 27, 31}
	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound) // Starting charge ID -- is increased with every seed

	aggregateIDsToCleanUp := make([]uuid.UUID, 0, len(daysOfMonth)*2)
	addressIDsToCleanUp := make([]int, 0, len(daysOfMonth)*2)

	for _, dayOfMonth := range daysOfMonth {
		aggregateID := utils.FabricateUUIDFromNumericID(chargeID)

		seededData := seeding.ClaimChargeData(t, testQueries, seeding.ClaimChargeDataParams{
			GroupID: groupUID,
			PPID:    "PSL-1337",
		})

		chargeCompletedParams := random.ChargeCompletedEventParams{
			AggregateID: aggregateID,
			ChargeID:    chargeID,
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(3.7),
				EnergyCost:  ptr.To(278),
			},
			Duration:         ptr.To(42),
			StartedAt:        ptr.To(time.Date(2024, 7, dayOfMonth, 14, 0, 0, 0, time.UTC)),
			EndedAt:          ptr.To(time.Date(2024, 7, dayOfMonth, 17, 15, 16, 0, time.UTC)),
			PluggedInAt:      ptr.To(time.Date(2024, 7, dayOfMonth, 13, 59, 55, 0, time.UTC)),
			UnpluggedAt:      ptr.To(time.Date(2024, 7, dayOfMonth, 19, 34, 29, 0, time.UTC)),
			SettlementAmount: ptr.To(42),
			UnitPPID:         chargers.StationID(seededData.Unit.Ppid),
			LocationID:       int(seededData.Location.ID),
			AuthoriserPK:     int(seededData.Authoriser.ID),
			AuthoriserType:   ptr.To(charges.User),
		}
		seedCompletedEvent := random.ChargeCompletedEvent(&chargeCompletedParams)
		err := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent)
		require.NoError(t, err)

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData.Address.ID))

		chargeID++

		aggregateID2 := utils.FabricateUUIDFromNumericID(chargeID)

		seededData2 := seeding.ClaimChargeData(t, testQueries, seeding.ClaimChargeDataParams{
			GroupID: groupUID,
			PPID:    "PSL-4200",
		})

		chargeCompletedParams2 := random.ChargeCompletedEventParams{
			AggregateID: aggregateID2,
			ChargeID:    chargeID,
			Duration:    ptr.To(1337),
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(6.1),
				EnergyCost:  ptr.To(453),
			},
			PluggedInAt:       ptr.To(time.Date(2024, 7, dayOfMonth, 20, 59, 55, 0, time.UTC)),
			UnpluggedAt:       ptr.To(time.Date(2024, 7, dayOfMonth, 20, 34, 29, 0, time.UTC)),
			SettlementAmount:  ptr.To(57),
			UnitPPID:          chargers.StationID(seededData2.Unit.Ppid),
			LocationID:        int(seededData2.Location.ID),
			AuthoriserPK:      int(seededData2.Authoriser.ID),
			UnconfirmedCharge: true,
			AuthoriserType:    ptr.To(charges.User),
		}
		seedCompletedEvent2 := random.ChargeCompletedEvent(&chargeCompletedParams2)
		err2 := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent2)
		require.NoError(t, err2)

		chargeID++

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID2)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData2.Address.ID))
	}

	return func() {
		cleanupData(ctx, t, aggregateIDsToCleanUp, addressIDsToCleanUp)
	}
}

func seedChargeProjectionsForSiteChargeStatistics(t *testing.T, siteID int64) func() {
	t.Helper()
	ctx := context.Background()

	daysOfMonth := []int{1, 15, 24, 27, 31}
	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound) // Starting charge ID -- is increased with every seed

	aggregateIDsToCleanUp := make([]uuid.UUID, 0, len(daysOfMonth)*2)
	addressIDsToCleanUp := make([]int, 0, len(daysOfMonth)*2)

	seededData := seeding.ClaimChargeData(t, testQueries, seeding.ClaimChargeDataParams{
		GroupID: uuid.New().String(),
		SiteID:  &siteID,
	})

	for _, dayOfMonth := range daysOfMonth {
		aggregateID := utils.FabricateUUIDFromNumericID(chargeID)

		chargeCompletedParams := random.ChargeCompletedEventParams{
			AggregateID: aggregateID,
			ChargeID:    chargeID,
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(3.7),
				EnergyCost:  ptr.To(278),
			},
			Duration:         ptr.To(42),
			StartedAt:        ptr.To(time.Date(2024, 7, dayOfMonth, 14, 0, 0, 0, time.UTC)),
			EndedAt:          ptr.To(time.Date(2024, 7, dayOfMonth, 17, 15, 16, 0, time.UTC)),
			PluggedInAt:      ptr.To(time.Date(2024, 7, dayOfMonth, 13, 59, 55, 0, time.UTC)),
			UnpluggedAt:      ptr.To(time.Date(2024, 7, dayOfMonth, 19, 34, 29, 0, time.UTC)),
			SettlementAmount: ptr.To(42),
			UnitPPID:         random.CommercialChargerID(),
			LocationID:       int(seededData.Location.ID),
			AuthoriserPK:     int(seededData.Authoriser.ID),
			AuthoriserType:   ptr.To(charges.User),
		}
		seedCompletedEvent := random.ChargeCompletedEvent(&chargeCompletedParams)
		err := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent)
		require.NoError(t, err)

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData.Address.ID))

		chargeID++

		aggregateID2 := utils.FabricateUUIDFromNumericID(chargeID)

		chargeCompletedParams2 := random.ChargeCompletedEventParams{
			AggregateID: aggregateID2,
			ChargeID:    chargeID,
			Duration:    ptr.To(1337),
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(6.1),
				EnergyCost:  ptr.To(453),
			},
			PluggedInAt:       ptr.To(time.Date(2024, 7, dayOfMonth, 20, 59, 55, 0, time.UTC)),
			UnpluggedAt:       ptr.To(time.Date(2024, 7, dayOfMonth, 20, 34, 29, 0, time.UTC)),
			SettlementAmount:  ptr.To(57),
			UnitPPID:          chargers.StationID(seededData.Unit.Ppid),
			LocationID:        int(seededData.Location.ID),
			AuthoriserPK:      int(seededData.Authoriser.ID),
			UnconfirmedCharge: true,
		}
		seedCompletedEvent2 := random.ChargeCompletedEvent(&chargeCompletedParams2)
		err2 := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent2)
		require.NoError(t, err2)

		chargeID++

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID2)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData.Address.ID))
	}

	return func() {
		cleanupData(ctx, t, aggregateIDsToCleanUp, addressIDsToCleanUp)
	}
}

func seedChargeProjectionsForChargerChargeStatistics(t *testing.T, chargerPPID string) func() {
	t.Helper()
	ctx := context.Background()

	daysOfMonth := []int{1, 15, 24, 27, 31}
	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound) // Starting charge ID -- is increased with every seed

	aggregateIDsToCleanUp := make([]uuid.UUID, 0, len(daysOfMonth)*2)
	addressIDsToCleanUp := make([]int, 0, len(daysOfMonth)*2)

	seededData := seeding.ClaimChargeData(t, testQueries, seeding.ClaimChargeDataParams{})
	seededData2 := seeding.ClaimChargeData(t, testQueries, seeding.ClaimChargeDataParams{})

	for _, dayOfMonth := range daysOfMonth {
		aggregateID := utils.FabricateUUIDFromNumericID(chargeID)

		chargeCompletedParams := random.ChargeCompletedEventParams{
			AggregateID: aggregateID,
			ChargeID:    chargeID,
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(3.7),
				EnergyCost:  ptr.To(278),
			},
			Duration:         ptr.To(42),
			StartedAt:        ptr.To(time.Date(2024, 7, dayOfMonth, 14, 0, 0, 0, time.UTC)),
			EndedAt:          ptr.To(time.Date(2024, 7, dayOfMonth, 17, 15, 16, 0, time.UTC)),
			PluggedInAt:      ptr.To(time.Date(2024, 7, dayOfMonth, 13, 59, 55, 0, time.UTC)),
			UnpluggedAt:      ptr.To(time.Date(2024, 7, dayOfMonth, 19, 34, 29, 0, time.UTC)),
			SettlementAmount: ptr.To(42),
			UnitPPID:         chargers.StationID(chargerPPID),
			LocationID:       int(seededData.Location.ID),
			AuthoriserPK:     int(seededData.Authoriser.ID),
			AuthoriserType:   ptr.To(charges.User),
		}
		seedCompletedEvent := random.ChargeCompletedEvent(&chargeCompletedParams)
		err := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent)
		require.NoError(t, err)

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData.Address.ID))

		chargeID++

		aggregateID2 := utils.FabricateUUIDFromNumericID(chargeID)

		chargeCompletedParams2 := random.ChargeCompletedEventParams{
			AggregateID: aggregateID2,
			ChargeID:    chargeID,
			Duration:    ptr.To(1337),
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(6.1),
				EnergyCost:  ptr.To(453),
			},
			PluggedInAt:       ptr.To(time.Date(2024, 7, dayOfMonth, 20, 59, 55, 0, time.UTC)),
			UnpluggedAt:       ptr.To(time.Date(2024, 7, dayOfMonth, 20, 34, 29, 0, time.UTC)),
			SettlementAmount:  ptr.To(57),
			UnitPPID:          chargers.StationID(chargerPPID),
			LocationID:        int(seededData.Location.ID),
			AuthoriserPK:      int(seededData2.Authoriser.ID),
			UnconfirmedCharge: true,
			AuthoriserType:    ptr.To(charges.User),
		}
		seedCompletedEvent2 := random.ChargeCompletedEvent(&chargeCompletedParams2)
		err2 := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent2)
		require.NoError(t, err2)

		chargeID++

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID2)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData.Address.ID))
	}

	return func() {
		cleanupData(ctx, t, aggregateIDsToCleanUp, addressIDsToCleanUp)
	}
}

func seedChargeProjectionsForGroupUsageSummaries(t *testing.T, groupUID string) func() {
	t.Helper()
	ctx := context.Background()

	daysOfMonth := []int{1, 15, 24, 27, 31}
	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound) // Starting charge ID -- is increased with every seed

	aggregateIDsToCleanUp := make([]uuid.UUID, 0, len(daysOfMonth)*2)
	addressIDsToCleanUp := make([]int, 0, len(daysOfMonth)*2)

	for _, day := range daysOfMonth {
		aggregateID := utils.FabricateUUIDFromNumericID(chargeID)

		seededData := seeding.ClaimChargeData(t, testQueries, seeding.ClaimChargeDataParams{
			GroupID: groupUID,
		})

		chargeCompletedParams := random.ChargeCompletedEventParams{
			AggregateID: aggregateID,
			ChargeID:    chargeID,
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(3.7),
				EnergyCost:  ptr.To(278),
			},
			StartedAt:        ptr.To(time.Date(2024, 7, day, 14, 0, 0, 0, time.UTC)),
			EndedAt:          ptr.To(time.Date(2024, 7, day, 17, 15, 16, 0, time.UTC)),
			PluggedInAt:      ptr.To(time.Date(2024, 7, day, 13, 59, 55, 0, time.UTC)),
			UnpluggedAt:      ptr.To(time.Date(2024, 7, day, 19, 34, 29, 0, time.UTC)),
			SettlementAmount: ptr.To(42),
			UnitPPID:         chargers.StationID(seededData.Unit.Ppid),
			LocationID:       int(seededData.Location.ID),
		}
		seedCompletedEvent := random.ChargeCompletedEvent(&chargeCompletedParams)
		err := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent)
		require.NoError(t, err)

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData.Address.ID))

		chargeID++

		aggregateID2 := utils.FabricateUUIDFromNumericID(chargeID)

		seededData2 := seeding.ClaimChargeData(t, testQueries, seeding.ClaimChargeDataParams{
			GroupID: groupUID,
		})

		chargeCompletedParams2 := random.ChargeCompletedEventParams{
			AggregateID: aggregateID2,
			ChargeID:    chargeID,
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(6.1),
				EnergyCost:  ptr.To(453),
			},
			PluggedInAt:      ptr.To(time.Date(2024, 7, day, 20, 59, 55, 0, time.UTC)),
			UnpluggedAt:      ptr.To(time.Date(2024, 7, day, 20, 34, 29, 0, time.UTC)),
			SettlementAmount: ptr.To(57),
			UnitPPID:         chargers.StationID(seededData2.Unit.Ppid),
			LocationID:       int(seededData2.Location.ID),
		}
		seedCompletedEvent2 := random.ChargeCompletedEvent(&chargeCompletedParams2)
		err2 := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent2)
		require.NoError(t, err2)

		chargeID++

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID2)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData2.Address.ID))
	}

	return func() {
		infra.CleanupEventsData(ctx, t, testQueries, aggregateIDsToCleanUp)
		infra.CleanupSiteStatsProjectionData(ctx, t, testQueries, addressIDsToCleanUp)
		fixtures.CleanupAllTestData(ctx, t, testQueries, nil, nil, nil)
	}
}

func seedChargeProjectionsForGroupAndSiteUsageSummaries(t *testing.T, groupUUID string, siteID int64) func() {
	t.Helper()
	ctx := context.Background()

	chargerPPID := "PSL-1337"

	daysOfMonth := []int{1, 15, 24, 27, 31}
	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound) // Starting charge ID -- is increased with every seed

	aggregateIDsToCleanUp := make([]uuid.UUID, 0, len(daysOfMonth)*2)
	addressIDsToCleanUp := make([]int, 0, len(daysOfMonth)*2)

	seededData := seeding.ClaimChargeData(t, testQueries, seeding.ClaimChargeDataParams{
		GroupID: groupUUID,
		PPID:    chargerPPID,
		SiteID:  &siteID,
	})

	for _, dayOfMonth := range daysOfMonth {
		aggregateID := utils.FabricateUUIDFromNumericID(chargeID)

		chargeCompletedParams := random.ChargeCompletedEventParams{
			AggregateID: aggregateID,
			ChargeID:    chargeID,
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(3.7),
				EnergyCost:  ptr.To(278),
			},
			Duration:         ptr.To(42),
			StartedAt:        ptr.To(time.Date(2024, 7, dayOfMonth, 14, 0, 0, 0, time.UTC)),
			EndedAt:          ptr.To(time.Date(2024, 7, dayOfMonth, 17, 15, 16, 0, time.UTC)),
			PluggedInAt:      ptr.To(time.Date(2024, 7, dayOfMonth, 13, 59, 55, 0, time.UTC)),
			UnpluggedAt:      ptr.To(time.Date(2024, 7, dayOfMonth, 19, 34, 29, 0, time.UTC)),
			SettlementAmount: ptr.To(42),
			UnitPPID:         chargers.StationID(chargerPPID),
			LocationID:       int(seededData.Location.ID),
			AuthoriserPK:     int(seededData.Authoriser.ID),
		}
		seedCompletedEvent := random.ChargeCompletedEvent(&chargeCompletedParams)
		err := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent)
		require.NoError(t, err)

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData.Address.ID))

		chargeID++

		aggregateID2 := utils.FabricateUUIDFromNumericID(chargeID)

		chargeCompletedParams2 := random.ChargeCompletedEventParams{
			AggregateID: aggregateID2,
			ChargeID:    chargeID,
			Duration:    ptr.To(1337),
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(6.1),
				EnergyCost:  ptr.To(453),
			},
			PluggedInAt:       ptr.To(time.Date(2024, 7, dayOfMonth, 20, 59, 55, 0, time.UTC)),
			UnpluggedAt:       ptr.To(time.Date(2024, 7, dayOfMonth, 20, 34, 29, 0, time.UTC)),
			SettlementAmount:  ptr.To(57),
			UnitPPID:          chargers.StationID(chargerPPID),
			LocationID:        int(seededData.Location.ID),
			AuthoriserPK:      int(seededData.Authoriser.ID),
			UnconfirmedCharge: true,
		}
		seedCompletedEvent2 := random.ChargeCompletedEvent(&chargeCompletedParams2)
		err2 := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent2)
		require.NoError(t, err2)

		chargeID++

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID2)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData.Address.ID))
	}

	return func() {
		cleanupData(ctx, t, aggregateIDsToCleanUp, addressIDsToCleanUp)
	}
}

func seedChargeProjectionsForGroupAndChargerUsageSummaries(t *testing.T, groupUUID, chargerID string) func() {
	t.Helper()
	ctx := context.Background()

	daysOfMonth := []int{1, 15, 24, 27, 31}
	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound) // Starting charge ID -- is increased with every seed

	aggregateIDsToCleanUp := make([]uuid.UUID, 0, len(daysOfMonth)*2)
	addressIDsToCleanUp := make([]int, 0, len(daysOfMonth)*2)

	seededData := seeding.ClaimChargeData(t, testQueries, seeding.ClaimChargeDataParams{
		GroupID: groupUUID,
		PPID:    chargerID,
	})

	for _, dayOfMonth := range daysOfMonth {
		aggregateID := utils.FabricateUUIDFromNumericID(chargeID)

		chargeCompletedParams := random.ChargeCompletedEventParams{
			AggregateID: aggregateID,
			ChargeID:    chargeID,
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(3.7),
				EnergyCost:  ptr.To(278),
			},
			Duration:         ptr.To(42),
			StartedAt:        ptr.To(time.Date(2024, 7, dayOfMonth, 14, 0, 0, 0, time.UTC)),
			EndedAt:          ptr.To(time.Date(2024, 7, dayOfMonth, 17, 15, 16, 0, time.UTC)),
			PluggedInAt:      ptr.To(time.Date(2024, 7, dayOfMonth, 13, 59, 55, 0, time.UTC)),
			UnpluggedAt:      ptr.To(time.Date(2024, 7, dayOfMonth, 19, 34, 29, 0, time.UTC)),
			SettlementAmount: ptr.To(42),
			UnitPPID:         chargers.StationID(chargerID),
			LocationID:       int(seededData.Location.ID),
			AuthoriserPK:     int(seededData.Authoriser.ID),
		}

		seedCompletedEvent := random.ChargeCompletedEvent(&chargeCompletedParams)
		err := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent)
		require.NoError(t, err)

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData.Address.ID))

		chargeID++

		aggregateID2 := utils.FabricateUUIDFromNumericID(chargeID)

		chargeCompletedParams2 := random.ChargeCompletedEventParams{
			AggregateID: aggregateID2,
			ChargeID:    chargeID,
			Duration:    ptr.To(1337),
			Energy: &random.EnergyParams{
				EnergyTotal: ptr.To(6.1),
				EnergyCost:  ptr.To(453),
			},
			PluggedInAt:       ptr.To(time.Date(2024, 7, dayOfMonth, 20, 59, 55, 0, time.UTC)),
			UnpluggedAt:       ptr.To(time.Date(2024, 7, dayOfMonth, 20, 34, 29, 0, time.UTC)),
			SettlementAmount:  ptr.To(57),
			UnitPPID:          chargers.StationID(chargerID),
			LocationID:        int(seededData.Location.ID),
			AuthoriserPK:      int(seededData.Authoriser.ID),
			UnconfirmedCharge: true,
		}

		seedCompletedEvent2 := random.ChargeCompletedEvent(&chargeCompletedParams2)
		err2 := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent2)
		require.NoError(t, err2)

		chargeID++

		aggregateIDsToCleanUp = append(aggregateIDsToCleanUp, aggregateID2)
		addressIDsToCleanUp = append(addressIDsToCleanUp, int(seededData.Address.ID))
	}

	return func() {
		cleanupData(ctx, t, aggregateIDsToCleanUp, addressIDsToCleanUp)
	}
}

func cleanupData(ctx context.Context, t *testing.T, aggregateIDsToCleanUp []uuid.UUID, addressIDsToCleanUp []int) {
	t.Helper()

	infra.CleanupEventsData(ctx, t, testQueries, aggregateIDsToCleanUp)
	infra.CleanupSiteStatsProjectionData(ctx, t, testQueries, addressIDsToCleanUp)
	fixtures.CleanupAllTestData(ctx, t, testQueries, nil, nil, nil)
}

func emptyUsageIntervalWithDate(date string) *server.UsageResponseBody {
	return &server.UsageResponseBody{
		IntervalStartDate: date,
	}
}
