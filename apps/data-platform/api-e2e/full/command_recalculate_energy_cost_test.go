package full

import (
	"context"
	"database/sql"
	"experience/libs/data-platform/api/contract/gen/drivers"
	"experience/libs/data-platform/event-sourcing/domain/commands/charges"
	chargeevents "experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	"experience/libs/data-platform/test/sqlc"
	"experience/libs/shared/go/service/utils"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func TestRecalculateChargeEnergyCost(t *testing.T) {
	ctx := context.Background()
	baseURL := setup.BaseURL(t, host)

	ppid := random.ChargerID()
	rate := 10000

	cleanup, chargeID, locationID, userID, userUID := seedRecalculateChargeEnergyCost(t, testQueries, ppid, int64(rate))
	defer cleanup()

	chargeEvent, apiChargeResponse, cleanup := seedCompletedEvent(t, userID, locationID, chargeID, ppid)
	defer cleanup()

	linkUserToCharger(t, baseURL, userUID, string(chargeEvent.Payload.ChargingStation.ID))

	from := time.Date(2023, time.November, 1, 0, 0, 0, 0, time.UTC)
	to := time.Date(2023, time.November, 2, 0, 0, 0, 0, time.UTC)
	expectedResponse := buildExpectedResponse(apiChargeResponse, from, to, userUID)

	// double the rate
	rate *= 2
	updateTariffRate(ctx, t, chargeEvent, int64(rate))

	recalculateEnergyCost(ctx, t, chargeEvent)

	expectedResponse.Data.Charges[0].Cost.Amount *= 2

	url := fmt.Sprintf("%s/drivers/%s/charges?from=%s&to=%s", baseURL, userUID, from.Format(time.DateOnly), to.Format(time.DateOnly))
	sendRequestAndCompareResponse(t, url, http.MethodGet, http.NoBody, expectedResponse)
}

func buildExpectedResponse(apiChargeResponse *drivers.Charge, from, to time.Time, userUID string) drivers.DriversChargesResponse {
	return drivers.DriversChargesResponse{
		Data: &drivers.Charges{
			Count:   1,
			Charges: []*drivers.Charge{apiChargeResponse},
		},
		Meta: &drivers.Meta{
			Params: map[string]any{
				"From":     from.Format(time.DateOnly),
				"To":       to.Format(time.DateOnly),
				"DriverID": userUID,
			},
		},
	}
}

func recalculateEnergyCost(ctx context.Context, t *testing.T, chargeEvent *chargeevents.Completed) {
	t.Helper()
	err := seeding.Command(ctx, t, chargeCommandSQSClient, ptr.To(charges.NewRecalculateEnergyCost(chargeEvent.GetAggregateID(), t.Name())))
	assert.NoError(t, err)
}

func updateTariffRate(ctx context.Context, t *testing.T, chargeEvent *chargeevents.Completed, rate int64) {
	t.Helper()
	err := testQueries.UpdateTariffTierRatesForLocationID(ctx, sqlc.UpdateTariffTierRatesForLocationIDParams{
		ID:   int64(chargeEvent.Payload.Location.ID),
		Rate: rate,
	})
	assert.NoError(t, err)
}

func seedCompletedEvent(t *testing.T, userID, locationID, chargeID int64, unitID string) (*chargeevents.Completed, *drivers.Charge, func()) {
	t.Helper()
	ctx := context.Background()

	aggregateID := utils.FabricateUUIDFromNumericID(int(chargeID))

	startedAt := time.Date(2023, time.November, 1, 14, 0, 0, 0, time.UTC)
	endedAt := time.Date(2023, time.November, 1, 17, 15, 16, 0, time.UTC)
	pluggedInAt := time.Date(2023, time.November, 1, 14, 1, 0, 0, time.UTC)
	unpluggedAt := time.Date(2023, time.November, 1, 17, 16, 0, 0, time.UTC)
	duration := 42
	energyCost := 44
	energyTotal := 44.2

	chargeCompletedParams := random.ChargeCompletedEventParams{
		ChargeID:    int(chargeID),
		AggregateID: aggregateID,
		LocationID:  int(locationID),
		Duration:    ptr.To(duration),
		StartedAt:   ptr.To(startedAt),
		EndedAt:     ptr.To(endedAt),
		PluggedInAt: ptr.To(pluggedInAt),
		UnpluggedAt: ptr.To(unpluggedAt),
		Energy: &random.EnergyParams{
			EnergyCost:  ptr.To(energyCost),
			EnergyTotal: ptr.To(energyTotal),
		},
		SettlementAmount:   ptr.To(135),
		SettlementCurrency: ptr.To("GBP"),
		AuthoriserPK:       int(userID),
		UnconfirmedCharge:  false,
		UnitPPID:           chargers.StationID(unitID),
	}

	completedEvent := random.ChargeCompletedEvent(&chargeCompletedParams)
	err := seeding.Event(ctx, t, chargeNotificationSQSClient, completedEvent)
	require.NoError(t, err)

	expectedResponse := drivers.Charge{
		ID:                    aggregateID.String(),
		StartedAt:             ptr.To(startedAt.Format(time.RFC3339)),
		EndedAt:               ptr.To(endedAt.Format(time.RFC3339)),
		Duration:              ptr.To(duration),
		EnergyTotal:           ptr.To(energyTotal),
		GenerationEnergyTotal: ptr.To(energyTotal),
		GridEnergyTotal:       ptr.To(energyTotal),
		Cost: &drivers.Money{
			Amount:   energyCost,
			Currency: "GBP", // hard-coded -- comes from the driver's home tariff
		},
		Charger: &drivers.Charger{
			Type:              "home",
			ID:                unitID,
			Door:              completedEvent.Payload.ChargingStation.DoorID,
			PluggedInAt:       ptr.To(pluggedInAt.Format(time.RFC3339)),
			UnpluggedAt:       ptr.To(unpluggedAt.Format(time.RFC3339)),
			PluggedInDuration: ptr.To(int(unpluggedAt.Sub(pluggedInAt).Seconds())),
		},
	}

	return completedEvent, &expectedResponse, func() {
		cleanupData(ctx, t, []uuid.UUID{aggregateID}, []int{})
	}
}

//nolint:thelper // if the seeding fails, we want to know which seeding failed
func seedRecalculateChargeEnergyCost(t *testing.T, queries *sqlc.Queries, ppid string, rate int64) (cleanup func(), chargeID, locationID, userID int64, userUID string) {
	ctx := context.Background()

	userID = fixtures.Sequence.Get()
	userUID = uuid.NewString()

	_, err := queries.CreateAuthoriser(ctx, sqlc.CreateAuthoriserParams{ID: userID, Uid: userUID, Type: "user"})
	assert.NoError(t, err)

	user, err := queries.CreateUser(ctx, fixtures.CreateUserParams(userID, userUID, sqlc.PodpointGroup{}, "<EMAIL>", "Tam", "Cowan"))
	assert.NoError(t, err)

	_, err = queries.CreateBillingAccount(ctx, fixtures.CreateBillingAccountParams(userID, 898, "EUR"))
	assert.NoError(t, err)

	tariff, err := queries.CreateTariff(ctx, sqlc.CreateTariffParams{ID: fixtures.Sequence.Get(), Currency: "GBP", Name: t.Name()})
	assert.NoError(t, err)

	_, err = queries.CreateTariffTier(ctx, sqlc.CreateTariffTierParams{ID: fixtures.Sequence.Get(), TariffID: tariff.ID, Rate: rate})
	assert.NoError(t, err)

	address, err := queries.CreateAddress(ctx, sqlc.CreateAddressParams{
		ID:           fixtures.Sequence.Get(),
		BusinessName: t.Name(),
		GroupID:      sql.NullInt64{Valid: false},
		TariffID:     sql.NullInt64{Int64: tariff.ID, Valid: true}})
	assert.NoError(t, err)

	unit, err := queries.CreateUnit(ctx, sqlc.CreateUnitParams{
		ID:   fixtures.Sequence.Get(),
		Ppid: ppid,
		Name: sql.NullString{Valid: false},
	})
	assert.NoError(t, err)

	locationID = fixtures.Sequence.Get()
	location, err := queries.CreateLocation(ctx, sqlc.CreateLocationParams{
		ID:          locationID,
		Uuid:        uuid.NewString(),
		AddressID:   address.ID,
		IsPublic:    0,
		IsHome:      1,
		UnitID:      sql.NullInt64{Int64: unit.ID, Valid: true},
		Timezone:    sql.NullString{String: "UTC", Valid: true},
		Longitude:   "-0.02560169",
		Latitude:    "51.70536849",
		Geohash:     sql.NullInt64{Int64: 114719790754337000, Valid: true},
		PaygEnabled: 0,
	})
	assert.NoError(t, err)

	chargeID = fixtures.Sequence.Get()
	_, err = queries.CreateCharge(ctx, sqlc.CreateChargeParams{
		ID:              chargeID,
		LocationID:      sql.NullInt64{Int64: location.ID, Valid: true},
		UnitID:          unit.ID,
		Door:            0,
		EnergyCost:      sql.NullInt32{},
		BillingEventID:  sql.NullInt64{},
		KwhUsed:         "20.01",
		Duration:        sql.NullInt64{},
		IsClosed:        0,
		GroupID:         sql.NullInt64{},
		ClaimedChargeID: sql.NullInt64{},
	})
	require.NoError(t, err)

	return func() {
		t.Helper()
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)
	}, chargeID, location.ID, user.ID, userUID
}
