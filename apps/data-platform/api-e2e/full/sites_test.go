package full

import (
	"context"
	"experience/apps/data-platform/api-e2e/pkg/infra"
	contractsites "experience/libs/data-platform/api/contract/gen/sites"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	"experience/libs/shared/go/service/utils"
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/google/uuid"
	"k8s.io/utils/ptr"
)

func TestRetrieveChargeStatsGroupedBySite(t *testing.T) {
	ctx := context.Background()
	baseURL := setup.BaseURL(t, host)
	groupID := uuid.New()
	groupName := random.SiteName(t)
	siteID := utils.FabricateUUIDFromNumericID(234)
	siteName := random.SiteName(t)
	fromDate := time.Now().Add(-24 * time.Hour)
	toDate := time.Now()
	chargerType := random.CommercialChargerTypeNullable()

	// arrange
	chargesSeeded, chargeToClearUUID, _ := seeding.ChargesToRetrieveBySite(ctx, t, chargesQueries, groupID, siteID, fromDate, siteName, groupName, chargerType, nil)

	chargeUUIDs := make([]uuid.UUID, len(chargesSeeded))
	for i, charge := range chargesSeeded {
		chargeUUIDs[i] = charge.ChargeUUID
	}

	chargeUUIDs = append(chargeUUIDs, chargeToClearUUID)

	defer infra.CleanupEventsData(ctx, t, testQueries, chargeUUIDs)

	totalEnergy := 0.0
	revenueGenerated := 0

	for i := 0; i < len(chargesSeeded); i++ {
		energy, _ := strconv.ParseFloat(chargesSeeded[i].EnergyTotal.String, 64)
		totalEnergy += energy
		revenueGenerated += int(chargesSeeded[i].SettlementAmount.Int32)
	}

	want := contractsites.SiteStatsResponse{
		From: fromDate.Format(time.DateOnly),
		To:   toDate.Format(time.DateOnly),
		Data: []*contractsites.SiteStats{
			{
				ID:               "234",
				Name:             ptr.To(siteName),
				GroupID:          ptr.To(groupID.String()),
				GroupName:        ptr.To(groupName),
				TotalEnergy:      totalEnergy,
				RevenueGenerated: revenueGenerated,
			},
		},
	}

	sendRequestAndRunCustomAssertionOnResponse(
		t,
		fmt.Sprintf("%s/sites?from=%s&to=%s", baseURL, fromDate.Format(time.DateOnly), toDate.Format(time.DateOnly)),
		http.MethodGet,
		http.NoBody,
		func(response contractsites.SiteStatsResponse) bool {
			if !assert.ObjectsAreEqual(want.From, response.From) {
				return false
			}
			if !assert.ObjectsAreEqual(want.To, response.To) {
				return false
			}
			return assertResponseContainsSiteStats(t, want.Data[0], response.Data)
		},
	)
}

func assertResponseContainsSiteStats(t *testing.T, wantSite *contractsites.SiteStats, gotSites []*contractsites.SiteStats) bool {
	t.Helper()

	for _, gotSite := range gotSites {
		if gotSite.ID == wantSite.ID {
			return assert.ObjectsAreEqual(wantSite, gotSite)
		}
	}

	return false
}
