package full

import (
	"context"
	"encoding/json"
	"experience/apps/data-platform/api-e2e/pkg/infra"
	groupstats "experience/libs/data-platform/api/contract/gen/projection_group_statistics"
	charge "experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/service/utils"
	shared_test "experience/libs/shared/go/test"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func TestProjectionsMonthlySiteStatsAttributesChargeDataToSiteGroupAndMonth(t *testing.T) {
	ctx := context.Background()

	groupID := uuid.New()
	siteIDs := []int{int(fixtures.Sequence.Get())}

	chargeEvents := generateNChargesPerSite(t, len(siteIDs), 3)
	defer infra.CleanupEventsData(ctx, t, testQueries, chargeEvents.aggregateIDs)
	defer infra.CleanupSiteStatsProjectionData(ctx, t, testQueries, siteIDs)

	cleanup := seeding.Sites(ctx, t, testQueries, groupID, siteIDs, chargeEvents.drivers, chargeEvents.chargers, nil)
	defer cleanup()

	for _, event := range chargeEvents.events {
		err := seeding.Event(ctx, t, chargeNotificationSQSClient, event)
		assert.NoError(t, err)
	}

	expectedResponse := groupstats.GroupSitesStatsResponse{Data: []*groupstats.GroupSitesStats{
		{
			SiteID:           utils.FabricateUUIDFromNumericID(siteIDs[0]).String(),
			EnergyUsageKwh:   300.0,
			EnergyCost:       300,
			Co2AvoidedKg:     168,
			RevenueGenerated: seeding.FixedRate * len(chargeEvents.events),
			NumberOfCharges:  3,
			NumberOfDrivers:  1,
			TotalDuration:    30,
		},
	},
		Meta: &groupstats.Meta{
			Params: map[string]interface{}{
				"Year":    float64(2024),
				"Month":   float64(1),
				"GroupID": groupID.String(),
			},
		},
	}

	requestURL := fmt.Sprintf("%s/charges/groups/%s/sites?year=%d&month=%d", setup.BaseURL(t, host), groupID, 2024, 1)

	sendRequestAndCompareResponse(t, requestURL, http.MethodGet, http.NoBody, expectedResponse)
}

func TestProjectionsMonthlySiteStatsAttributesChargeDataToSiteGroupAndMonthMulti(t *testing.T) {
	ctx := context.Background()

	groupID := uuid.New()

	n := 5 // replace with the number of siteIDs you want to generate
	siteIDs := make([]int, n)
	for i := 0; i < n; i++ {
		siteIDs[i] = int(fixtures.Sequence.Get())
	}

	chargeEvents := generateNChargesPerSite(t, len(siteIDs), 10)
	defer infra.CleanupEventsData(ctx, t, testQueries, chargeEvents.aggregateIDs)
	defer infra.CleanupSiteStatsProjectionData(ctx, t, testQueries, siteIDs)

	cleanup := seeding.Sites(ctx, t, testQueries, groupID, siteIDs, chargeEvents.drivers, chargeEvents.chargers, nil)
	defer cleanup()

	for _, event := range chargeEvents.events {
		err := seeding.Event(ctx, t, chargeNotificationSQSClient, event)
		assert.NoError(t, err)
	}

	timeout := time.NewTimer(chargeEvents.timeout * time.Second)
	done := make(chan bool)
	var lastResult *shared_test.AsyncResult[groupstats.GroupSitesStatsResponse]
	go func() {
		match := false
		for !match {
			er := groupstats.GroupSitesStatsResponse{
				Data: createExpectedResponses(siteIDs, 10),
				Meta: &groupstats.Meta{
					Params: map[string]interface{}{
						"Year":    float64(2024),
						"Month":   float64(1),
						"GroupID": groupID.String(),
					},
				},
			}
			requestURL := fmt.Sprintf("%s/charges/groups/%s/sites?year=%d&month=%d", setup.BaseURL(t, host), groupID, 2024, 1)
			lastResult, match = checkResponseOnSitesMonthlyStats(t, requestURL, &er)
			time.Sleep(1000 * time.Millisecond)
		}
		done <- true
	}()

	select {
	case <-timeout.C:
		if lastResult != nil {
			lastResult.AssertEqual(t)
		}
		t.Fatal("test didn't finish in time")
	case <-done:
	}
}

// checkResponseOnSitesMonthlyStats is used when checking the response from the API.
// if the response is never matched in responseOnGroupSiteStatsComparison you are likely to see connection refused errors after test timeout.
func checkResponseOnSitesMonthlyStats(t *testing.T, requestURL string, expected *groupstats.GroupSitesStatsResponse) (*shared_test.AsyncResult[groupstats.GroupSitesStatsResponse], bool) {
	t.Helper()

	response := sendRequest(t, http.MethodGet, requestURL, http.NoBody)
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		return nil, false
	}

	var responseStruct groupstats.GroupSitesStatsResponse
	bodyBytes, err := io.ReadAll(response.Body)
	require.NoError(t, err)
	require.NoError(t, json.Unmarshal(bodyBytes, &responseStruct))

	if responseOnGroupSiteStatsComparison(t, expected, &responseStruct)() {
		return nil, true
	}

	return ptr.To(shared_test.NewAsyncResult(*expected, responseStruct)), false
}

func responseOnGroupSiteStatsComparison(t *testing.T, expected, actual *groupstats.GroupSitesStatsResponse) assert.Comparison {
	t.Helper()

	return func() (success bool) {
		if expected == nil && actual == nil {
			return true
		}
		if expected == nil || actual == nil {
			return false
		}
		if len(expected.Data) != len(actual.Data) {
			return false
		}

		return assert.ElementsMatch(&testing.T{}, expected.Data, actual.Data)
	}
}

type eventsFixture struct {
	drivers      []*random.Driver
	chargers     []*random.Charger
	events       []eventstore.Event
	aggregateIDs []uuid.UUID
	timeout      time.Duration
}

func generateNChargesPerSite(t *testing.T, nSites, nCharges int) eventsFixture {
	t.Helper()
	drivers := make([]*random.Driver, nSites)
	siteChargers := make([]*random.Charger, nSites)
	chargeIDs := make([]int, nCharges*nSites)
	aggregateIDs := make([]uuid.UUID, nCharges*nSites)
	events := make([]eventstore.Event, nCharges*nSites)

	for i := 0; i < nSites; i++ {
		ppid := random.CommercialChargerID()
		locationID := int(fixtures.Sequence.Get())
		authoriserID := fixtures.Sequence.Get()
		drivers[i] = &random.Driver{
			DriverID:     ptr.To(uuid.New()),
			AuthoriserID: authoriserID,
		}
		siteChargers[i] = &random.Charger{
			ID:       string(ppid),
			Name:     ptr.To(random.ChargerName()),
			Timezone: ptr.To(random.TimezoneString()),
			SiteName: ptr.To(random.SiteName(t)),
			Location: int64(locationID),
			Access:   ptr.To("private"),
		}

		for j := 0; j < nCharges; j++ {
			newIndex := i*nCharges + j
			chargeID, aggregateID := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
			chargeIDs[newIndex] = chargeID
			aggregateIDs[newIndex] = aggregateID
			event := random.ChargeCompletedEvent(&random.ChargeCompletedEventParams{
				ChargeID:       chargeID,
				AggregateID:    aggregateID,
				AuthoriserPK:   int(authoriserID),
				AuthoriserType: ptr.To(charge.User),
				LocationID:     locationID,
				UnitPPID:       ppid,
				PluggedInAt:    ptr.To(time.Date(2024, 1, 1, 5, 0, 0, 0, time.UTC)),
				Duration:       ptr.To(10),
				Energy: &random.EnergyParams{
					EnergyCost:  ptr.To(100),
					EnergyTotal: ptr.To(100.0),
				},
				SettlementAmount:   ptr.To(seeding.FixedRate),
				SettlementCurrency: ptr.To("GBP"),
			})
			event.Historic = true
			events[newIndex] = event
		}
	}

	return eventsFixture{
		drivers:      drivers,
		chargers:     siteChargers,
		events:       events,
		aggregateIDs: aggregateIDs,
		timeout:      30,
	}
}

func createExpectedResponses(siteIDs []int, nCharges int) []*groupstats.GroupSitesStats {
	expectedResponses := make([]*groupstats.GroupSitesStats, len(siteIDs))

	for i, siteID := range siteIDs {
		expectedResponses[i] = &groupstats.GroupSitesStats{
			SiteID:           utils.FabricateUUIDFromNumericID(siteID).String(),
			EnergyUsageKwh:   float64(100 * nCharges),
			EnergyCost:       100 * nCharges,
			Co2AvoidedKg:     560,
			RevenueGenerated: seeding.FixedRate * nCharges,
			NumberOfCharges:  1 * nCharges,
			NumberOfDrivers:  1,
			TotalDuration:    10 * nCharges,
		}
	}

	return expectedResponses
}

// future tests for:
// - multiple monthly periods
