package full

import (
	"context"
	"experience/apps/data-platform/api-e2e/pkg/infra"
	drivercharges "experience/libs/data-platform/api/contract/gen/driver_charges"
	organisationcharges "experience/libs/data-platform/api/contract/gen/organisation_charges"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	"experience/libs/shared/go/service/test/assertions"
	"experience/libs/shared/go/service/utils"
	"fmt"
	"net/http"
	"net/url"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestOrganisationChargesForOrganisation(t *testing.T) {
	baseURL := setup.BaseURL(t, host)
	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
	cleanup, completedEvent, claimedEvent := seedChargeProjection(t, chargeID)
	defer cleanup()

	organisationID, _ := uuid.Parse(claimedEvent.Group.Uid)
	startedAt := completedEvent.Payload.Charge.StartedAt.Format(time.RFC3339)
	endedAt := completedEvent.Payload.Charge.EndedAt.Format(time.RFC3339)
	pluggedInAt := completedEvent.Payload.Charge.PluggedInAt.Format(time.RFC3339)
	unPluggedAt := completedEvent.Payload.Charge.UnpluggedAt.Format(time.RFC3339)
	energyCost := int(ptr.Deref(completedEvent.Payload.Charge.EnergyTotal, 0) * float64(defaultTariff))

	type Input struct {
		OrganisationID uuid.UUID
		Status         string
		ChargeID       int
	}

	tests := []struct {
		name             string
		args             Input
		expectedResponse organisationcharges.OrganisationChargesResponse
		seed             func(t *testing.T, input Input) (func() error, error)
	}{
		{
			"Expected response from organisation expenses endpoint for mix of NEW and PROCESSED",
			Input{
				OrganisationID: organisationID,
				Status:         "",
				ChargeID:       chargeID,
			},
			organisationcharges.OrganisationChargesResponse{
				ExpensableCharges: []*organisationcharges.ExpensableCharge{
					{
						Driver:        MakeDriver("John", "Smith", "<EMAIL>"),
						StartTime:     startedAt,
						EndTime:       endedAt,
						PluggedInAt:   &pluggedInAt,
						UnpluggedAt:   &unPluggedAt,
						SubmittedTime: "2023-12-24T21:34:10Z",
						ChargeCost:    energyCost,
						EnergyUsage:   0.61,
						Location: &organisationcharges.SubmittedChargeLocation{
							LocationType: "home",
							Address: &organisationcharges.SubmittedChargeAddress{
								Country:     ptr.To("GB"),
								Town:        "London",
								Line1:       "234 Banner St",
								Line2:       ptr.To("Line 2"),
								Postcode:    ptr.To("EC1Y 8QE"),
								PrettyPrint: "234 Banner St, London, EC1Y 8QE",
							},
						},
						ChargerName:         "PP-10001",
						ProcessedTime:       nil,
						ProcessedByFullName: nil,
					},
					{
						Driver:        MakeDriver("Steven", "Flynn", "<EMAIL>"),
						StartTime:     "2022-04-11T20:34:00Z",
						EndTime:       "2022-04-11T21:34:10Z",
						PluggedInAt:   nil,
						UnpluggedAt:   nil,
						SubmittedTime: "2023-12-24T21:34:10Z",
						ChargeCost:    1500,
						EnergyUsage:   15.00,
						Location: &organisationcharges.SubmittedChargeLocation{
							LocationType: "public",
							Address: &organisationcharges.SubmittedChargeAddress{
								Country:     ptr.To("GB"),
								Town:        "London",
								Line1:       "234 Banner St",
								Line2:       ptr.To("Line 2"),
								Postcode:    ptr.To("EC1Y 8QE"),
								PrettyPrint: "234 Banner St, London, EC1Y 8QE",
							},
						},
						ChargerName:         "Kent-Jake",
						ProcessedTime:       ptr.To("2022-04-11T21:34:00Z"),
						ProcessedByFullName: ptr.To("Fleet Manager"),
					},
				},
			},
			func(t *testing.T, input Input) (func() error, error) {
				t.Helper()
				return seeding.NewAndProcessedSubmittedChargesForOrg(t, testQueries, input.OrganisationID, input.ChargeID)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clean, err := tt.seed(t, tt.args)

			defer func() {
				err2 := clean()
				require.NoError(t, err2)
			}()
			require.NoError(t, err)

			from := time.Date(2023, time.November, 25, 0, 0, 0, 0, time.UTC).Format(time.DateOnly)
			to := time.Date(2023, time.December, 25, 0, 0, 0, 0, time.UTC).Format(time.DateOnly)

			queryParams := url.Values{}
			queryParams.Add("from", from)
			queryParams.Add("to", to)
			if tt.args.Status != "" {
				queryParams.Add("status", tt.args.Status)
			}

			sendRequestAndRunCustomAssertionOnResponse(
				t,
				fmt.Sprintf("%s/organisations/%s/submitted-charges?%s", baseURL, tt.args.OrganisationID.String(), queryParams.Encode()),
				http.MethodGet,
				http.NoBody,
				func(response organisationcharges.OrganisationChargesResponse) bool {
					return assertions.AssertEqualityForAllFieldsExceptExcluded(t, tt.expectedResponse, response, []string{"ID"})
				},
			)
		})
	}
}

func TestOrganisationChargesForOrganisationGroupedByDriver(t *testing.T) {
	type Input struct {
		OrganisationID uuid.UUID
		Status         string
	}

	baseURL := setup.BaseURL(t, host)

	tests := []struct {
		name             string
		args             Input
		expectedResponse organisationcharges.OrganisationChargesDriverSummaryResponse
		seed             func(t *testing.T, input Input) (func() error, error)
	}{
		{
			"Expected response from organisation expenses endpoint",
			Input{
				OrganisationID: uuid.New(),
				Status:         "",
			},
			organisationcharges.OrganisationChargesDriverSummaryResponse{
				DriverExpensableChargeSummaries: []*organisationcharges.ExpensableChargeDriverSummary{
					{ // has home and public charges
						Driver:             MakeDriver("Steven", "Flynn", "<EMAIL>"),
						TotalCharges:       3,
						SubmittedChargeIds: []int64{850000028, 850000029, 850000030},
						TotalUsage:         MakeTotalUsage(20.01, 20.02),
						TotalCost:          MakeTotalCost(2001, 2002),
					},
					{ // has only home charges
						Driver:             MakeDriver("John", "Smith", "<EMAIL>"),
						TotalCharges:       2,
						SubmittedChargeIds: []int64{850000010, 850000011},
						TotalUsage:         MakeTotalUsage(1.01, 0.0),
						TotalCost:          MakeTotalCost(101, 0),
					},
				},
			},
			func(t *testing.T, input Input) (func() error, error) {
				t.Helper()
				return seeding.SubmittedChargesForOrgGroupedByDriver(t, testQueries, input.OrganisationID)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// arrange
			clean, err := tt.seed(t, tt.args)

			defer func() {
				cleanErr := clean()
				require.NoError(t, cleanErr)
			}()
			require.NoError(t, err)

			sendRequestAndRunCustomAssertionOnResponse(
				t,
				fmt.Sprintf(
					"%s/organisations/%s/submitted-charges/drivers?from=%s&to=%s&status=%s",
					baseURL,
					tt.args.OrganisationID.String(),
					time.Now().Add(time.Hour*24*-29).Format(time.DateOnly),
					time.Now().Add(time.Hour*48).Format(time.DateOnly),
					tt.args.Status,
				),
				http.MethodGet,
				http.NoBody,
				func(response organisationcharges.OrganisationChargesDriverSummaryResponse) bool {
					return assertions.AssertEqualityForAllFieldsExceptExcluded(t, tt.expectedResponse, response, []string{"ID"})
				},
			)
		})
	}
}

func TestOrganisationChargesForOrganisationDriver(t *testing.T) {
	baseURL := setup.BaseURL(t, host)

	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
	cleanup, completedEvent, claimedEvent := seedChargeProjection(t, chargeID)
	defer cleanup()

	organisationID, _ := uuid.Parse(claimedEvent.Group.Uid)
	startedAt := completedEvent.Payload.Charge.StartedAt.Format(time.RFC3339)
	endedAt := completedEvent.Payload.Charge.EndedAt.Format(time.RFC3339)
	pluggedInAt := completedEvent.Payload.Charge.PluggedInAt.Format(time.RFC3339)
	unPluggedAt := completedEvent.Payload.Charge.UnpluggedAt.Format(time.RFC3339)
	energyCost := int(ptr.Deref(completedEvent.Payload.Charge.EnergyTotal, 0) * float64(defaultTariff))

	type Input struct {
		OrganisationID uuid.UUID
		DriverID       int64
		ChargeID       int
	}

	tests := []struct {
		name             string
		args             Input
		expectedResponse organisationcharges.SubmittedChargesResponse
		seed             func(t *testing.T, input Input) (func() error, error)
	}{
		{
			"Test 200 response from organisation driver expenses endpoint",
			Input{
				OrganisationID: organisationID,
				DriverID:       fixtures.Sequence.Get(),
				ChargeID:       chargeID,
			},
			organisationcharges.SubmittedChargesResponse{
				Driver: MakeDriver("John", "Smith", "<EMAIL>"),
				TotalCost: &organisationcharges.TotalCost{
					Home:   126,
					Public: 42,
				},
				TotalUsage: &organisationcharges.TotalUsage{
					Home:   52.5,
					Public: 17.5,
				},
				SubmittedCharges: []*organisationcharges.SubmittedCharge{
					{
						StartTime:           "2022-04-11T21:34:00Z",
						EndTime:             "2022-04-11T21:34:10Z",
						SubmittedTime:       "0001-01-01T00:00:00Z",
						ChargeCost:          42,
						EnergyUsage:         17.5,
						Duration:            23,
						Location:            MakeLocation("home"),
						ChargerName:         "PP-10001",
						Status:              "NEW",
						ProcessedTime:       nil,
						ProcessedByFullName: nil,
						PluggedInAt:         nil,
						UnpluggedAt:         nil,
					},
					{
						StartTime:           "2022-04-11T20:34:00Z",
						EndTime:             "2022-04-11T21:34:10Z",
						SubmittedTime:       "0001-01-01T00:00:00Z",
						ChargeCost:          42,
						EnergyUsage:         17.5,
						Duration:            23,
						Location:            MakeLocation("home"),
						ChargerName:         "PP-10001",
						Status:              "NEW",
						ProcessedTime:       nil,
						ProcessedByFullName: nil,
						PluggedInAt:         nil,
						UnpluggedAt:         nil,
					},
					{
						StartTime:           "2022-04-11T19:34:00Z",
						EndTime:             "2022-04-11T21:34:10Z",
						SubmittedTime:       "0001-01-01T00:00:00Z",
						ChargeCost:          42,
						EnergyUsage:         17.5,
						Duration:            23,
						Location:            MakeLocation("home"),
						ChargerName:         "PP-10001",
						Status:              "NEW",
						ProcessedTime:       nil,
						ProcessedByFullName: nil,
						PluggedInAt:         nil,
						UnpluggedAt:         nil,
					},
					{
						StartTime:           startedAt,
						EndTime:             endedAt,
						SubmittedTime:       "0001-01-01T00:00:00Z",
						ChargeCost:          energyCost,
						EnergyUsage:         17.5,
						Duration:            23,
						Location:            MakeLocation("public"),
						ChargerName:         "Kent-Jake",
						Status:              "PROCESSED",
						ProcessedTime:       ptr.To("0001-01-01T00:00:00Z"),
						ProcessedByFullName: ptr.To("John Smith"),
						PluggedInAt:         ptr.To(pluggedInAt),
						UnpluggedAt:         ptr.To(unPluggedAt),
					},
				},
			},
			func(t *testing.T, input Input) (func() error, error) {
				t.Helper()
				return seeding.SubmittedChargesForOrganisationAndDriver(t, testQueries, input.OrganisationID, input.DriverID, input.ChargeID)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clean, err := tt.seed(t, tt.args)
			require.NoError(t, err)
			defer func() {
				cleanErr := clean()
				require.NoError(t, cleanErr)
			}()

			sendRequestAndRunCustomAssertionOnResponse(
				t,
				fmt.Sprintf("%s/organisations/%s/submitted-charges/%d", baseURL, tt.args.OrganisationID, tt.args.DriverID),
				http.MethodGet,
				http.NoBody,
				func(response organisationcharges.SubmittedChargesResponse) bool {
					return assertions.AssertEqualityForAllFieldsExceptExcluded(t, tt.expectedResponse, response, []string{"ID"})
				},
			)

			fixtures.Sequence.Reset()
		})
	}
}

func TestOrganisationChargesMarkAsProcessed(t *testing.T) {
	// arrange
	baseURL := setup.BaseURL(t, host)
	orgUUID := uuid.New()
	podadminDBState := fixtures.NewDBState("podadmin", podadminQueries)

	clean, organisation, driver, charges, err := seeding.SubmittedChargesToMarkAsProcessed(t, testQueries, podadminQueries, podadminDBState, orgUUID)
	require.NoError(t, err)

	submittedChargeUUIDs := make([]uuid.UUID, 0)

	payload := drivercharges.CreateDriverExpensesPayload{
		Expenses: []*drivercharges.CreateExpenseRequest{
			{
				ChargeID: int(charges[0].ID),
			},
			{
				ChargeID: int(charges[1].ID),
			},
		},
	}
	body, _, _ := createOrganisationCharges(t, baseURL, payload, int(driver.ID), int(organisation.ID))

	submittedEpensesIDs := make([]int, 0)

	for _, submittedExpense := range body.Expenses {
		submittedEpensesIDs = append(submittedEpensesIDs, submittedExpense.ID)
		submittedChargeUUIDs = append(submittedChargeUUIDs, utils.FabricateUUIDFromNumericID(submittedExpense.ChargeID))
	}

	defer func() {
		// TODO: This sleep is not pretty but it is necessary.
		// "CleanupEventsData" is cleaning up the correct data, but it is
		// possible that the projection.charges entry which is supposed to be
		// cleaned up doesn't exist yet. I don't like a sleep in the tests, but
		// a failing pipeline is worse. We can improve it later.
		time.Sleep(5 * time.Second)
		cleanErr := clean()
		assert.NoError(t, cleanErr)
		err := testQueries.DeleteOrganisation(context.Background(), organisation.ID)
		assert.NoError(t, err)
		err = testQueries.DeleteCharges(context.Background(), fixtures.ConvertIDsToInt32Slice(podadminDBState.GetIDsForTable(fixtures.Charges)))
		assert.NoError(t, err)
		infra.CleanupEventsData(context.Background(), t, testQueries, submittedChargeUUIDs)
	}()

	// act
	responseCode, _ := processCharges(t, baseURL, driver, submittedEpensesIDs)
	assert.Equal(t, 200, responseCode)

	// assert
	from := time.Now().Add(time.Hour * 24 * -30).Format(time.DateOnly)
	to := time.Now().Add(time.Hour * 24).Format(time.DateOnly)

	sendRequestAndRunCustomAssertionOnResponse(
		t,
		fmt.Sprintf("%s/organisations/%s/submitted-charges/drivers?from=%s&to=%s&status=%s", baseURL, organisation.Uid, from, to, "PROCESSED"),
		http.MethodGet,
		http.NoBody,
		func(response organisationcharges.OrganisationChargesDriverSummaryResponse) bool {
			if len(response.DriverExpensableChargeSummaries) != 1 {
				return false
			}
			if response.DriverExpensableChargeSummaries[0].Driver.ID != int(driver.ID) {
				return false
			}
			if response.DriverExpensableChargeSummaries[0].TotalCharges != 2 {
				return false
			}
			return true
		},
	)

	// What matters for the query is the submitted_at time and that is now,
	// even though the test inserts a charge in April 2022
	sendRequestAndRunCustomAssertionOnResponse(
		t,
		fmt.Sprintf("%s/organisations/%s/submitted-charges/drivers?from=%s&to=%s&status=%s", baseURL, organisation.Uid, from, to, "NEW"),
		http.MethodGet,
		http.NoBody,
		func(response organisationcharges.OrganisationChargesDriverSummaryResponse) bool {
			return len(response.DriverExpensableChargeSummaries) == 0
		},
	)
}

func TestOrganisationChargesFleetUsage(t *testing.T) {
	// arrange
	baseURL := setup.BaseURL(t, host)
	orgUUID := uuid.New()
	podadminDBState := fixtures.NewDBState("podadmin", podadminQueries)

	clean, organisation, driver, charge, err := seeding.SubmittedChargesForFleetUsage(t, testQueries, podadminQueries, podadminDBState, orgUUID)
	require.NoError(t, err)

	defer func() {
		// TODO: This sleep is not pretty but it is necessary.
		// "CleanupEventsData" is cleaning up the correct data, but it is
		// possible that the projection.charges entry which is supposed to be
		// cleaned up doesn't exist yet. I don't like a sleep in the tests, but
		// a failing pipeline is worse. We can improve it later.
		time.Sleep(5 * time.Second)
		cleanErr := clean()
		assert.NoError(t, cleanErr)
		err := testQueries.DeleteOrganisation(context.Background(), organisation.ID)
		assert.NoError(t, err)
		err = testQueries.DeleteCharge(context.Background(), charge.ID)
		assert.NoError(t, err)
		infra.CleanupEventData(context.Background(), t, testQueries, utils.FabricateUUIDFromNumericID(int(charge.ID)))
	}()

	expenseRequest := []*drivercharges.CreateExpenseRequest{{
		ChargeID: int(charge.ID),
	}}
	payload := drivercharges.CreateDriverExpensesPayload{Expenses: expenseRequest}

	totalUsage := float64(10)
	expectedResponse := organisationcharges.FleetUsageResponse{
		TotalCharges: &organisationcharges.TotalCharges{
			Total:  1,
			Home:   1,
			Public: 0,
		},
		TotalUsage: &organisationcharges.TotalUsage{
			Total:  &totalUsage,
			Home:   10,
			Public: 0,
		},
		Co2Savings:      5.6,
		NumberOfDrivers: 1,
	}

	// act
	_, _, serviceError := createOrganisationCharges(t, baseURL, payload, int(driver.ID), int(organisation.ID))
	if serviceError != nil {
		t.Fatalf("failed %v", serviceError)
	}

	// assert
	sendRequestAndCompareResponse(
		t,
		fmt.Sprintf("%s/organisations/%s/fleet-usage", baseURL, orgUUID.String()),
		http.MethodGet,
		http.NoBody,
		expectedResponse,
	)
}
