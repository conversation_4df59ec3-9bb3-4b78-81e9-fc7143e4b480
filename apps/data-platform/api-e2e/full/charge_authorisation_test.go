package full

import (
	"context"
	"database/sql"
	"experience/apps/data-platform/api-e2e/env"
	chargeauthorisation "experience/libs/data-platform/api/contract/gen/charge_authorisation"
	contract "experience/libs/data-platform/api/contract/gen/http/charge_authorisation/client"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	"experience/libs/shared/go/numbers"
	"experience/libs/shared/go/service/utils"
	"fmt"
	"net/http"
	"strconv"
	"testing"

	"goa.design/clue/log"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"k8s.io/utils/ptr"
)

func TestChargeAuthorisationRFID(t *testing.T) {
	ctx := context.Background()
	baseURL := setup.BaseURL(t, host)
	podadminDBState := fixtures.NewDBState("podadmin", podadminQueries)

	groupID := uuid.New()
	siteNames := []string{random.SiteName(t)}
	locationID := int(fixtures.Sequence.Get())
	rfidTestTag := "test-rfid-tag"

	charger := random.Charger{
		ID:       string(random.CommercialChargerID()),
		Name:     ptr.To(random.ChargerName()),
		Timezone: ptr.To(random.TimezoneString()),
		SiteName: ptr.To(siteNames[0]),
		Location: int64(locationID),
		Access:   ptr.To("private"),
	}

	cleanup, authoriserID := seeding.ChargeAuthorisationRFID(ctx, t, testQueries, podadminQueries, podadminDBState, groupID, &charger, env.IsLocal(host), rfidTestTag)
	defer cleanup()

	convertedAuthoriserID, conversionErr := numbers.Convert[int64, int32](authoriserID)
	assert.NoError(t, conversionErr)
	convertedPodadminAuthoriserID, conversionErr := numbers.Convert[int64, int32](podadminDBState.GetIDsForTable(fixtures.Authorisers)[0])
	assert.NoError(t, conversionErr)

	payload := contract.AuthoriseChargeRequestBody{
		Token:     rfidTestTag,
		ChargerID: charger.ID,
		Door:      "A",
	}

	metaParams := map[string]interface{}{
		"AuthorisationMethod": "rfid",
		"ChargerID":           charger.ID,
		"Door":                "A",
		"Token":               rfidTestTag,
	}

	sendRequestAndRunCustomAssertionOnResponse(
		t,
		fmt.Sprintf("%s/charge-authorisations/rfid", baseURL),
		http.MethodPost,
		payload,
		func(response chargeauthorisation.ChargeAuthorisationResponse) bool {
			if !assert.ObjectsAreEqual(metaParams, response.Meta.Params) {
				return false
			}
			paClaimedCharge, err := podadminQueries.RetrievePodadminClaimedChargesByAuthoriser(ctx, convertedAuthoriserID)
			if err != nil {
				return false
			}

			if !assert.ObjectsAreEqual(convertedPodadminAuthoriserID, paClaimedCharge.AuthoriserID) {
				return false
			}

			convertedClaimID, err := numbers.Convert[uint32, int](paClaimedCharge.ID)
			if err != nil {
				log.Warnf(ctx, "failed to convert claim ID: %v", err)
				return false
			}

			if !assert.Equal(t, *response.ID, utils.AuthorisedEventUUIDFromNumericID(convertedClaimID).String()) {
				return false
			}

			return true
		},
	)
}

func TestChargeAuthorisationOCPI(t *testing.T) {
	ctx := context.Background()
	baseURL := setup.BaseURL(t, host)
	podadminDBState := fixtures.NewDBState("podadmin", podadminQueries)

	token := t.Name() + "-token"
	config := seeding.PodadminHelperConfig{
		AuthoriserUID:      &token,
		AuthoriserType:     ptr.To("ocpi"),
		ReplicationSeeding: true,
	}
	cleanup, authoriserID, chargerID := seeding.ChargeAuthorisationOCPI(ctx, t, testQueries, podadminQueries, podadminDBState, env.IsLocal(host), &config)
	defer cleanup()

	payload := contract.AuthoriseChargeRequestBody{
		Token:     token,
		ChargerID: ptr.Deref(chargerID, ""),
		Door:      "A",
	}

	metaParams := map[string]interface{}{
		"AuthorisationMethod": "ocpi",
		"ChargerID":           ptr.Deref(chargerID, ""),
		"Door":                "A",
		"Token":               token,
	}

	convertedAuthoriserID, err := numbers.Convert[int64, int32](authoriserID)
	assert.NoError(t, err)

	sendRequestAndRunCustomAssertionOnResponse(
		t,
		fmt.Sprintf("%s/charge-authorisations/ocpi", baseURL),
		http.MethodPost,
		payload,
		func(response chargeauthorisation.ChargeAuthorisationResponse) bool {
			if !assert.ObjectsAreEqual(metaParams, response.Meta.Params) {
				return false
			}
			paClaimedCharge, err := podadminQueries.RetrievePodadminClaimedChargesByAuthoriser(ctx, convertedAuthoriserID)
			if err != nil {
				return false
			}

			convertedClaimID, err := numbers.Convert[uint32, int](paClaimedCharge.ID)
			if err != nil {
				log.Warnf(ctx, "failed to convert claim ID: %v", err)
				return false
			}

			if !assert.Equal(t, *response.ID, utils.AuthorisedEventUUIDFromNumericID(convertedClaimID).String()) {
				return false
			}
			return true
		},
	)
}

func TestChargeAuthorisationGuest(t *testing.T) {
	ctx := context.Background()
	baseURL := setup.BaseURL(t, host)
	podadminDBState := fixtures.NewDBState("podadmin", podadminQueries)

	token := t.Name() + "-token"
	config := seeding.PodadminHelperConfig{
		AuthoriserUID:      &token,
		AuthoriserType:     ptr.To("guest"),
		ReplicationSeeding: true,
		IsPublicLocation:   true,
	}

	cleanup, billingEventID, chargerID := seeding.ChargeAuthorisationGuest(ctx, t, testQueries, podadminQueries, podadminDBState, env.IsLocal(host), &config)
	defer cleanup()

	intBillingEventID, err := numbers.Convert[int64, int](billingEventID)
	assert.NoError(t, err)

	payload := contract.AuthoriseChargeRequestBody{
		Token:     strconv.Itoa(intBillingEventID),
		ChargerID: chargerID,
		Door:      "A",
	}

	metaParams := map[string]interface{}{
		"AuthorisationMethod": "guest",
		"ChargerID":           chargerID,
		"Door":                "A",
		"Token":               strconv.Itoa(intBillingEventID),
	}

	int32BillingEventID, err := numbers.Convert[int64, int32](billingEventID)
	assert.NoError(t, err)

	sendRequestAndRunCustomAssertionOnResponse(
		t,
		fmt.Sprintf("%s/charge-authorisations/guest", baseURL),
		http.MethodPost,
		payload,
		func(response chargeauthorisation.ChargeAuthorisationResponse) bool {
			if !assert.ObjectsAreEqual(metaParams, response.Meta.Params) {
				return false
			}
			paClaimedCharge, err := podadminQueries.RetrievePodadminClaimedChargeByBillingEventID(ctx, sql.NullInt32{Int32: int32BillingEventID, Valid: true})
			if err != nil {
				return false
			}

			convertedClaimID, err := numbers.Convert[uint32, int](paClaimedCharge.ID)
			if err != nil {
				log.Warnf(ctx, "failed to convert claim ID: %v", err)
				return false
			}

			if !assert.Equal(t, *response.ID, utils.AuthorisedEventUUIDFromNumericID(convertedClaimID).String()) {
				return false
			}
			return true
		},
	)
}
