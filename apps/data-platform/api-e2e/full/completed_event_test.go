package full

import (
	"encoding/json"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/shared/go/service/utils"
	shared_test "experience/libs/shared/go/test"
	"testing"

	"github.com/google/uuid"
	"k8s.io/utils/ptr"
)

func TestRewardedEligibleEnergy(t *testing.T) {
	stopProcessingSubscription := startProcessingSubscription(t)
	defer stopProcessingSubscription()

	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
	cleanup, completedEvent, _ := seedChargeProjection(t, chargeID, WithHomeCharger(), WithUnconfirmedCharge())
	defer cleanup()

	expectedRewardIntegrationPayload := &rewards{
		EligibleEnergy: ptr.To(float32(20.45)),                                                  // value from api stub
		VehicleID:      ptr.To(uuid.MustParse("2f727f8b-7c0e-43b3-b455-a1d7f07ea036").String()), // value from api stub
	}

	t.Logf("polling rewarded eligible energy...")
	handleTimeoutAndAsyncResult(t, defaultTestTimeout, defaultSleepInterval, func() (*shared_test.AsyncResult[rewards], bool) {
		messages, err := chargeCompletedSQSClient.ReceiveMessage(t.Context())
		if err != nil {
			t.Logf("failed to receive message: %v", err)
			return nil, false
		}

		var integrationEvent eventBridgeWrapper[event]
		if len(messages) > 0 {
			b := messages[0].Body
			t.Logf("received message: %s", *b)
			err = chargeCompletedSQSClient.DeleteMessage(t.Context(), messages[0].ReceiptHandle)
			if err != nil {
				t.Fatalf("failed to delete message: %v", err)
			}

			err = json.Unmarshal([]byte(*b), &integrationEvent)
			if err != nil {
				t.Fatalf("failed to unmarshal integration event: %v", err)
			}

			if compareRewardPayloads(integrationEvent, expectedRewardIntegrationPayload, completedEvent.AggregateID) {
				return nil, true
			}
		}

		return ptr.To(shared_test.AsyncResult[rewards]{
			Expected: expectedRewardIntegrationPayload,
			Actual:   &integrationEvent.Detail.Rewards,
		}), false
	})
}

func TestBillable(t *testing.T) {
	stopProcessingSubscription := startProcessingSubscription(t)
	defer stopProcessingSubscription()

	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
	cleanup, completedEvent, claimChargeData := seedChargeProjection(t, chargeID, WithBillableStartDate(), WithSettlementAmount(1234), WithSettlementCurrency("GBP"))
	defer cleanup()

	expectedRevenueIntegrationPayload := &revenue{
		BillingType:        ptr.To("unsupported"),
		SettlementAmount:   ptr.To(completedEvent.Payload.Billing.SettlementAmount),
		SettlementCurrency: ptr.To(completedEvent.Payload.Billing.SettlementCurrency),
	}

	expectedAuthorisationIntegrationPayload := &authorisation{
		ID:           utils.FabricateUUIDFromNumericID(*completedEvent.Payload.Authorisation.ClaimedChargeID),
		AuthoriserID: claimChargeData.Authoriser.Uid,
		Type:         completedEvent.Payload.Authorisation.Type,
	}

	t.Logf("polling billable...")
	handleTimeoutAndAsyncResult(t, defaultTestTimeout, defaultSleepInterval, func() (*shared_test.AsyncResult[revenue], bool) {
		messages, err := chargeCompletedSQSClient.ReceiveMessage(t.Context())
		if err != nil {
			t.Logf("failed to receive message: %v", err)
			return nil, false
		}

		var integrationEvent eventBridgeWrapper[event]
		if len(messages) > 0 {
			b := messages[0].Body
			t.Logf("received message: %s", *b)
			err = chargeCompletedSQSClient.DeleteMessage(t.Context(), messages[0].ReceiptHandle)
			if err != nil {
				t.Fatalf("failed to delete message: %v", err)
			}

			err = json.Unmarshal([]byte(*b), &integrationEvent)
			if err != nil {
				t.Fatalf("failed to unmarshal integration event: %v", err)
			}

			if compareRevenuePayloads(integrationEvent, expectedRevenueIntegrationPayload, completedEvent.AggregateID) &&
				compareAuthoriserPayloads(integrationEvent, expectedAuthorisationIntegrationPayload, completedEvent.AggregateID) {
				return nil, true
			}
		}

		return ptr.To(shared_test.AsyncResult[revenue]{
			Expected: expectedRevenueIntegrationPayload,
			Actual:   &integrationEvent.Detail.Revenue,
		}), false
	})
}

func startProcessingSubscription(t *testing.T) (stopProcessingSubscription func()) {
	t.Helper()
	err := testQueries.StartProcessingSubscription(t.Context(), "publish_charge_completed_to_eventbridge")
	if err != nil {
		t.Fatalf("failed to start reward subscription: %v", err)
	}
	return func() {
		if err = testQueries.StopProcessingSubscription(t.Context(), "publish_charge_completed_to_eventbridge"); err != nil {
			t.Fatalf("failed to delete event subscription: %v", err)
		}
	}
}

func compareRewardPayloads(integrationEvent eventBridgeWrapper[event], expectedRewardIntegrationPayload *rewards, expectedChargeID uuid.UUID) bool {
	actual := integrationEvent.Detail.Rewards
	if actual.VehicleID == nil || actual.EligibleEnergy == nil {
		return false
	}

	return integrationEvent.Detail.Charge.ID == expectedChargeID.String() &&
		*integrationEvent.Detail.Rewards.EligibleEnergy == *expectedRewardIntegrationPayload.EligibleEnergy &&
		*integrationEvent.Detail.Rewards.VehicleID == *expectedRewardIntegrationPayload.VehicleID
}

func compareRevenuePayloads(integrationEvent eventBridgeWrapper[event], expectedRevenueIntegrationPayload *revenue, expectedChargeID uuid.UUID) bool {
	actual := integrationEvent.Detail.Revenue
	if actual.BillingType == nil || actual.SettlementAmount == nil || actual.SettlementCurrency == nil {
		return false
	}

	return integrationEvent.Detail.Charge.ID == expectedChargeID.String() &&
		*integrationEvent.Detail.Revenue.BillingType == *expectedRevenueIntegrationPayload.BillingType &&
		*integrationEvent.Detail.Revenue.SettlementAmount == *expectedRevenueIntegrationPayload.SettlementAmount &&
		*integrationEvent.Detail.Revenue.SettlementCurrency == *expectedRevenueIntegrationPayload.SettlementCurrency
}

func compareAuthoriserPayloads(integrationEvent eventBridgeWrapper[event], expectedAuthoriserIntegrationPayload *authorisation, expectedChargeID uuid.UUID) bool {
	actual := integrationEvent.Detail.Authorisation
	if actual.Type == nil {
		return false
	}

	return integrationEvent.Detail.Charge.ID == expectedChargeID.String() &&
		integrationEvent.Detail.Authorisation.ID == expectedAuthoriserIntegrationPayload.ID &&
		integrationEvent.Detail.Authorisation.AuthoriserID == expectedAuthoriserIntegrationPayload.AuthoriserID &&
		*integrationEvent.Detail.Authorisation.Type == *expectedAuthoriserIntegrationPayload.Type
}

type eventBridgeWrapper[T any] struct {
	Detail T `json:"detail"`
}

type event struct {
	Charge struct {
		ChargeDurationTotal   float64 `json:"chargeDurationTotal"`
		EndedAt               string  `json:"endedAt"`
		EnergyTotal           float64 `json:"energyTotal"`
		GenerationEnergyTotal float64 `json:"generationEnergyTotal"`
		GridEnergyTotal       float64 `json:"gridEnergyTotal"`
		ID                    string  `json:"id"`
		PluggedInAt           string  `json:"pluggedInAt"`
		StartedAt             string  `json:"startedAt"`
		UnpluggedAt           string  `json:"unpluggedAt"`
	} `json:"charge"`
	ChargingStation struct {
		DoorID string `json:"doorId"`
		ID     string `json:"id"`
	} `json:"chargingStation"`
	Energy struct {
		Cost         int    `json:"cost"`
		CostCurrency string `json:"costCurrency"`
	} `json:"energy"`
	Rewards       rewards       `json:"rewards"`
	Revenue       revenue       `json:"revenue"`
	Authorisation authorisation `json:"authorisation"`
	PublishedAt   string        `json:"publishedAt"`
}

type rewards struct {
	EligibleEnergy *float32 `json:"eligibleEnergy"`
	VehicleID      *string  `json:"vehicleID"`
}

type revenue struct {
	BillingType        *string `json:"billingType"`
	SettlementAmount   *int    `json:"settlementAmount"`
	SettlementCurrency *string `json:"settlementCurrency"`
}

type authorisation struct {
	ID           uuid.UUID `json:"id"`
	AuthoriserID string    `json:"authoriserID"`
	Type         *string   `json:"type"`
}
