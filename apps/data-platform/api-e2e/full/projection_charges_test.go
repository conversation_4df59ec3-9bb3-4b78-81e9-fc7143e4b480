package full

import (
	"encoding/json"
	projectioncharges "experience/libs/data-platform/api/contract/gen/projection_charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	carbonsavings "experience/libs/shared/go/carbon-savings"
	"experience/libs/shared/go/service/utils"
	shared_test "experience/libs/shared/go/test"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

var defaultTariff = 14

// Leaving a property as an empty string means it won't be included in the API request
type listChargeStatisticsParams struct {
	groupID   string
	siteID    string
	chargerID string
	from      string
	to        string
}

func TestGroupLevelChargeStatisticsReturnsDataIfRequiredParamsAreGiven(t *testing.T) {
	baseURL := setup.BaseURL(t, host)

	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
	cleanup, completedEvent, claimChargeData := seedChargeProjection(t, chargeID, WithAuthoriserType(charges.User))
	defer cleanup()

	siteID := utils.FabricateUUIDFromNumericID(int(claimChargeData.Location.AddressID)).String()

	tests := []struct {
		testName string
		params   listChargeStatisticsParams
	}{
		{
			testName: "requesting with all properties, valid from and to",
			params: listChargeStatisticsParams{
				groupID:   claimChargeData.Group.Uid,
				siteID:    siteID,
				chargerID: claimChargeData.Unit.Ppid,
				from:      "1960-01-01",
				to:        "2077-01-01",
			},
		},
		{
			testName: "requesting with groupId only, valid from and to",
			params: listChargeStatisticsParams{
				groupID: claimChargeData.Group.Uid,
				from:    "1960-01-01",
				to:      "2077-01-01",
			},
		},
		{
			testName: "requesting with siteId only, valid from and to",
			params: listChargeStatisticsParams{
				siteID: siteID,
				from:   "1960-01-01",
				to:     "2077-01-01",
			},
		},
		{
			testName: "requesting with chargerId only, valid from and to",
			params: listChargeStatisticsParams{
				chargerID: claimChargeData.Unit.Ppid,
				from:      "1960-01-01",
				to:        "2077-01-01",
			},
		},
		{
			testName: "requesting with groupId and siteId only, valid from and to",
			params: listChargeStatisticsParams{
				groupID: claimChargeData.Group.Uid,
				siteID:  siteID,
				from:    "1960-01-01",
				to:      "2077-01-01",
			},
		},
		{
			testName: "requesting with groupId and chargerId only, valid from and to",
			params: listChargeStatisticsParams{
				groupID:   claimChargeData.Group.Uid,
				chargerID: claimChargeData.Unit.Ppid,
				from:      "1960-01-01",
				to:        "2077-01-01",
			},
		},
		{
			testName: "requesting with siteId and chargerId only, valid from and to",
			params: listChargeStatisticsParams{
				siteID:    siteID,
				chargerID: claimChargeData.Unit.Ppid,
				from:      "1960-01-01",
				to:        "2077-01-01",
			},
		},
	}

	for _, test := range tests {
		t.Run(test.testName, func(t *testing.T) {
			timeout := time.NewTimer(10 * time.Second)
			done := make(chan bool)

			params := test.params

			expectedResponse := projectioncharges.ProjectionChargesResponse{
				Data: []*projectioncharges.Charges{
					expectedChargeProjection(completedEvent, claimChargeData),
				},
				Meta: generateMetaParams(&params),
			}

			var lastResult *shared_test.AsyncResult[projectioncharges.ProjectionChargesResponse]
			go func() {
				match := false
				for !match {
					lastResult, match = callProjectionChargesEndpointAndCheckForResponse(t, baseURL, &params, &expectedResponse)
					time.Sleep(1000 * time.Millisecond)
				}
				done <- true
			}()

			select {
			case <-timeout.C:
				if lastResult != nil {
					lastResult.AssertEqual(t)
				}
				t.Fatal("test didn't finish in time")
			case <-done:
			}
		})
	}
}

func TestHomeChargeProjections(t *testing.T) {
	baseURL := setup.BaseURL(t, host)

	chargeID, _ := random.ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
	cleanup, completedEvent, claimChargeData := seedChargeProjection(t, chargeID, WithAuthoriserType(charges.User), WithHomeCharger())
	defer cleanup()

	params := listChargeStatisticsParams{
		chargerID: claimChargeData.Unit.Ppid,
		from:      "1960-01-01",
		to:        "2077-01-01",
	}

	expectedResponse := projectioncharges.ProjectionChargesResponse{
		Data: []*projectioncharges.Charges{
			expectedChargeProjection(completedEvent, claimChargeData),
		},
		Meta: generateMetaParams(&params),
	}

	timeout := time.NewTimer(10 * time.Second)
	done := make(chan bool)

	var lastResult *shared_test.AsyncResult[projectioncharges.ProjectionChargesResponse]
	go func() {
		match := false
		for !match {
			lastResult, match = callProjectionChargesEndpointAndCheckForResponse(t, baseURL, &params, &expectedResponse)
			time.Sleep(1000 * time.Millisecond)
		}
		done <- true
	}()

	select {
	case <-timeout.C:
		if lastResult != nil {
			lastResult.AssertEqual(t)
		}
		t.Fatal("test didn't finish in time")
	case <-done:
	}
}

func generateQueryParams(t *testing.T, params *listChargeStatisticsParams) (queryParams url.Values) {
	t.Helper()

	queryParams = url.Values{}

	if params.groupID != "" {
		queryParams.Add("groupId", params.groupID)
	}
	if params.siteID != "" {
		queryParams.Add("siteId", params.siteID)
	}
	if params.chargerID != "" {
		queryParams.Add("chargerId", params.chargerID)
	}
	if params.from != "" {
		queryParams.Add("from", params.from)
	}
	if params.to != "" {
		queryParams.Add("to", params.to)
	}

	return queryParams
}

func callProjectionChargesEndpointAndCheckForResponse(t *testing.T, baseURL string, params *listChargeStatisticsParams, expectedResponse *projectioncharges.ProjectionChargesResponse) (*shared_test.AsyncResult[projectioncharges.ProjectionChargesResponse], bool) {
	t.Helper()

	queryParams := url.Values{}
	if params.groupID != "" {
		queryParams.Add("groupId", params.groupID)
	}
	if params.siteID != "" {
		queryParams.Add("siteId", params.siteID)
	}
	if params.chargerID != "" {
		queryParams.Add("chargerId", params.chargerID)
	}
	if params.from != "" {
		queryParams.Add("from", params.from)
	}
	if params.to != "" {
		queryParams.Add("to", params.to)
	}

	requestURL := fmt.Sprintf("%s/charges?%s", baseURL, queryParams.Encode())
	response := sendRequest(t, http.MethodGet, requestURL, http.NoBody)
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		return nil, false
	}

	var responseStruct projectioncharges.ProjectionChargesResponse
	bodyBytes, err := io.ReadAll(response.Body)
	require.NoError(t, err)
	require.NoError(t, json.Unmarshal(bodyBytes, &responseStruct))

	if assert.ObjectsAreEqual(expectedResponse, &responseStruct) {
		return nil, true
	}

	return ptr.To(shared_test.NewAsyncResult(*expectedResponse, responseStruct)), false
}

func expectedChargeProjection(completedEvent *charges.Completed, claimChargeData *seeding.CreatedClaimChargeData) *projectioncharges.Charges {
	revenueGenerated := seeding.FixedRate
	if claimChargeData.Location.IsHome == 1 {
		revenueGenerated = 0
	}

	currentEnergyCost := int(ptr.Deref(completedEvent.Payload.Charge.EnergyTotal, 0) * float64(defaultTariff))

	return &projectioncharges.Charges{
		ID:                    completedEvent.AggregateID.String(),
		ChargeDurationTotal:   int(*completedEvent.Payload.Charge.ChargeDurationTotal),
		ChargerID:             claimChargeData.Unit.Ppid,
		ChargerName:           claimChargeData.Unit.Name.String,
		Co2Avoided:            carbonsavings.Co2Savings(*completedEvent.Payload.Charge.EnergyTotal),
		Door:                  completedEvent.Payload.ChargingStation.DoorID,
		DriverIDs:             []string{claimChargeData.Authoriser.Uid},
		EndedAt:               completedEvent.Payload.Charge.EndedAt.Format(time.RFC3339),
		EnergyCost:            currentEnergyCost,
		EnergyTotal:           *completedEvent.Payload.Charge.EnergyTotal,
		GenerationEnergyTotal: *completedEvent.Payload.Charge.GenerationEnergyTotal,
		GridEnergyTotal:       *completedEvent.Payload.Charge.GridEnergyTotal,
		PluggedInAt:           completedEvent.Payload.Charge.PluggedInAt.Format(time.RFC3339),
		RevenueGenerated:      revenueGenerated,
		SiteName:              claimChargeData.Address.BusinessName,
		StartedAt:             completedEvent.Payload.Charge.StartedAt.Format(time.RFC3339),
		UnpluggedAt:           completedEvent.Payload.Charge.UnpluggedAt.Format(time.RFC3339),
		Confirmed:             completedEvent.Payload.Authorisation.ClaimedChargeID != nil,
	}
}

func generateMetaParams(params *listChargeStatisticsParams) *projectioncharges.Meta {
	metaParams := map[string]interface{}{
		"From": params.from,
		"To":   params.to,
	}

	paramMap := map[string]string{
		"GroupID":   params.groupID,
		"SiteID":    params.siteID,
		"ChargerID": params.chargerID,
	}

	for key, value := range paramMap {
		if value != "" {
			metaParams[key] = value
		}
	}

	return &projectioncharges.Meta{
		Params: metaParams,
	}
}
