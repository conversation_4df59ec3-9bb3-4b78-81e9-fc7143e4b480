package full

import (
	"context"
	"experience/apps/data-platform/api-e2e/pkg/infra"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/shared/go/service/utils"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"k8s.io/utils/ptr"
)

type ChargeProjectionParams struct {
	ChargeCompletedEventParams random.ChargeCompletedEventParams
	ChargeClaimedEventParams   seeding.ClaimChargeDataParams
}

type ChargeProjectionOption = func(p *ChargeProjectionParams)

func WithSettlementAmount(settlementAmount int) func(p *ChargeProjectionParams) {
	return func(p *ChargeProjectionParams) {
		p.ChargeCompletedEventParams.SettlementAmount = ptr.To(settlementAmount)
	}
}

func WithSettlementCurrency(settlementCurrency string) func(p *ChargeProjectionParams) {
	return func(p *ChargeProjectionParams) {
		p.ChargeCompletedEventParams.SettlementCurrency = ptr.To(settlementCurrency)
	}
}

func WithAuthoriserPK(authoriserPK int) func(p *ChargeProjectionParams) {
	return func(p *ChargeProjectionParams) {
		p.ChargeCompletedEventParams.AuthoriserPK = authoriserPK
	}
}

func WithBillableStartDate() func(p *ChargeProjectionParams) {
	return func(p *ChargeProjectionParams) {
		newDate := time.Now().UTC().Add(-time.Hour * 7 * 24)
		p.ChargeCompletedEventParams.PluggedInAt = &newDate
	}
}

func WithAuthoriserType(authoriserType charges.AuthoriserType) func(p *ChargeProjectionParams) {
	return func(p *ChargeProjectionParams) {
		p.ChargeCompletedEventParams.AuthoriserType = ptr.To(authoriserType)
	}
}

func WithAuthoriserTypePayter() func(p *ChargeProjectionParams) {
	return func(p *ChargeProjectionParams) {
		p.ChargeCompletedEventParams.AuthoriserType = ptr.To(charges.Payter)
	}
}

func WithChargerID(chargerID chargers.StationID) func(p *ChargeProjectionParams) {
	return func(p *ChargeProjectionParams) {
		p.ChargeCompletedEventParams.UnitPPID = chargerID
		p.ChargeClaimedEventParams.PPID = string(chargerID)
	}
}

func WithUnconfirmedCharge() func(p *ChargeProjectionParams) {
	return func(p *ChargeProjectionParams) {
		p.ChargeCompletedEventParams.UnconfirmedCharge = true
	}
}

func WithHomeCharger() func(p *ChargeProjectionParams) {
	return func(p *ChargeProjectionParams) {
		p.ChargeClaimedEventParams.IsHome = true
	}
}

func RemoveBillingPayload() func(p *ChargeProjectionParams) {
	return func(p *ChargeProjectionParams) {
		p.ChargeCompletedEventParams.RemoveBillingPayload = true
	}
}

func seedChargeProjection(t *testing.T, chargeID int, seedChargeProjectionOptions ...ChargeProjectionOption) (cleanup func(), completedEvent *charges.Completed, claimChargeData *seeding.CreatedClaimChargeData) {
	t.Helper()
	ctx := context.Background()

	aggregateID := utils.FabricateUUIDFromNumericID(chargeID)

	pluggedInTime := time.Now().UTC().Add(-time.Hour * 128 * 24)
	chargeCompletedParams := random.ChargeCompletedEventParams{
		ChargeID:    chargeID,
		AggregateID: aggregateID,
		Energy: &random.EnergyParams{
			EnergyCost:  ptr.To(1),
			EnergyTotal: ptr.To(72.1),
		},
		PluggedInAt: &pluggedInTime,
	}

	params := &ChargeProjectionParams{
		ChargeCompletedEventParams: chargeCompletedParams,
		ChargeClaimedEventParams:   seeding.ClaimChargeDataParams{},
	}

	for _, seedChargeProjectionOption := range seedChargeProjectionOptions {
		seedChargeProjectionOption(params)
	}

	seededData := seeding.ClaimChargeData(t, testQueries, params.ChargeClaimedEventParams)
	if params.ChargeCompletedEventParams.AuthoriserPK == 0 {
		params.ChargeCompletedEventParams.AuthoriserPK = int(seededData.Authoriser.ID)
	}
	if params.ChargeCompletedEventParams.UnitPPID == "" {
		params.ChargeCompletedEventParams.UnitPPID = chargers.StationID(seededData.Unit.Ppid)
	}
	if params.ChargeCompletedEventParams.LocationID == 0 {
		params.ChargeCompletedEventParams.LocationID = int(seededData.Location.ID)
	}

	seedCompletedEvent := random.ChargeCompletedEvent(&params.ChargeCompletedEventParams)

	err := seeding.Event(ctx, t, chargeNotificationSQSClient, seedCompletedEvent)
	assert.NoError(t, err)
	return func() {
		infra.CleanupEventData(ctx, t, testQueries, aggregateID)
		infra.CleanupSiteStatsProjectionData(ctx, t, testQueries, []int{int(seededData.Address.ID)})
		fixtures.CleanupAllTestData(ctx, t, testQueries, nil, nil, nil)
	}, seedCompletedEvent, seededData
}
