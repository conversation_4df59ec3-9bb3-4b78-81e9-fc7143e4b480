package charges

import (
	"bytes"
	"context"
	"errors"
	mockcharges "experience/apps/data-platform/api/pkg/charges/mock"
	"experience/apps/data-platform/api/pkg/charges/models"
	"experience/apps/data-platform/api/pkg/common/groups"
	mockgroups "experience/apps/data-platform/api/pkg/common/groups/mock"
	groupsmodels "experience/apps/data-platform/api/pkg/common/groups/models"
	"experience/apps/data-platform/api/pkg/common/users"
	mockusers "experience/apps/data-platform/api/pkg/common/users/mock"
	usersmodels "experience/apps/data-platform/api/pkg/common/users/models"
	mocksubmittedcharges "experience/apps/data-platform/api/pkg/submittedcharges/mock"
	mock_charges "experience/libs/data-platform/event-sourcing/domain/events/charges/mock"
	"experience/libs/data-platform/test/random"
	"experience/libs/shared/go/numbers"
	"experience/libs/shared/go/service"
	"log"
	"os"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

type SubmitForExpensingInput struct {
	submittedCharges models.SubmittedCharges
	orgID            int64
	driverID         int64
}

func TestChargesService_SubmitForExpensing(t *testing.T) {
	ctrl := gomock.NewController(t)
	logger := log.New(os.Stderr, "[driver_unit_tests] ", log.Ltime)

	tests := []struct {
		name       string
		args       SubmitForExpensingInput
		createErr  error
		updateErr  error
		expected   models.SubmittedCharges
		assertErrs func(t *testing.T, err error)
	}{
		{
			name: "SubmitForExpensing returns created charges when repositories throw no errors.",
			args: SubmitForExpensingInput{
				submittedCharges: models.SubmittedCharges{
					{
						ChargeID:       235,
						OrganisationID: 1,
						DriverID:       987,
						SubmittedAt:    time.Date(2022, time.April, 11, 21, 34, 0, 0, time.UTC),
						CreatedBy:      "user",
						CreatedAt:      time.Date(2022, time.April, 11, 21, 34, 1, 0, time.UTC),
					},
				},
			},
			expected: models.SubmittedCharges{
				{
					ID:             1,
					ChargeID:       235,
					OrganisationID: 1,
					DriverID:       987,
					SubmittedAt:    time.Date(2022, time.April, 11, 21, 34, 0, 0, time.UTC),
					CreatedBy:      "user",
					CreatedAt:      time.Date(2022, time.April, 11, 21, 34, 1, 0, time.UTC),
				},
			},
			assertErrs: func(t *testing.T, err error) {
				t.Helper()
				require.NoError(t, err)
			},
		},
		{
			name: "SubmitForExpensing returns error from submittedCharges repository and does not call podadmin repository.",
			args: SubmitForExpensingInput{
				submittedCharges: models.SubmittedCharges{
					{
						ChargeID:       235,
						OrganisationID: 1,
						DriverID:       987,
						SubmittedAt:    time.Date(2021, time.April, 11, 21, 34, 0, 0, time.UTC),
						CreatedBy:      "user",
						CreatedAt:      time.Date(2021, time.April, 11, 21, 34, 1, 0, time.UTC),
					},
				},
			},

			createErr: ErrDriverNotFound,

			expected: nil,
			assertErrs: func(t *testing.T, err error) {
				t.Helper()
				require.ErrorContains(t, err, "driver not found")
				require.Equal(t, ErrDriverNotFound, err)
			},
		},
	}
	for _, tt := range tests {
		ctx := context.Background()

		mockRepository := mocksubmittedcharges.NewMockRepository(ctrl)
		mockChargesRepository := mockcharges.NewMockRepository(ctrl)
		mockUsersRepository := mockusers.NewMockRepository(ctrl)
		mockGroupsRepository := mockgroups.NewMockRepository(ctrl)
		mockExpenseChargesBatchService := mock_charges.NewMockExpenseChargesBatchService(ctrl)

		mockRepository.
			EXPECT().
			Create(ctx, gomock.Eq(tt.args.submittedCharges), gomock.Eq(tt.args.orgID), gomock.Eq(tt.args.driverID)).
			Return(tt.expected, tt.createErr).
			AnyTimes()

		sut := NewService(logger, mockRepository, mockChargesRepository, mockUsersRepository, mockGroupsRepository, mockExpenseChargesBatchService)

		response, err := sut.SubmitForExpensing(ctx, tt.args.submittedCharges, tt.args.orgID, tt.args.driverID)
		tt.assertErrs(t, err)
		require.Equal(t, tt.expected, response)
	}
}

func TestChargesService_GetForDriver(t *testing.T) {
	logger := log.New(os.Stderr, "[driver_unit_tests] ", log.Ltime)

	tests := []struct {
		name           string
		userID         uuid.UUID
		organisationID uuid.UUID
		expected       models.DriverChargeCollection
		stubUser       *usersmodels.User
		findUserError  error
		stubGroup      *groupsmodels.Group
		findGroupError error
		wantError      error
	}{
		{
			name:          "Returns driver not found when user is not found",
			findUserError: users.ErrUserNotFound,
			wantError:     ErrDriverNotFound,
		},
		{
			name:          "Returns unhandled error from user repository",
			findUserError: errors.New("unhandled error"),
			wantError:     errors.New("unhandled error"),
		},
		{
			name:           "Return driver charges when no errors",
			userID:         uuid.New(),
			organisationID: uuid.New(),
			expected: models.DriverChargeCollection{
				models.NewDriverCharge(),
			},
		},
		{
			name:           "Return org not found when group is not found",
			findGroupError: groups.ErrGroupNotFound,
			wantError:      ErrOrganisationNotFound,
		},
		{
			name:           "Returns unhandled error from group repository",
			findGroupError: errors.New("unhandled error"),
			wantError:      errors.New("unhandled error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(st *testing.T) {
			ctx := context.Background()

			ctrl := gomock.NewController(st)
			mockRepository := mocksubmittedcharges.NewMockRepository(ctrl)
			mockChargesRepository := mockcharges.NewMockRepository(ctrl)
			mockUsersRepository := mockusers.NewMockRepository(ctrl)
			mockGroupsRepository := mockgroups.NewMockRepository(ctrl)
			mockExpenseChargesBatchService := mock_charges.NewMockExpenseChargesBatchService(ctrl)

			mockUsersRepository.
				EXPECT().
				FindUserByUUID(gomock.AssignableToTypeOf(context.Background()), gomock.Eq(tt.userID)).
				Return(tt.stubUser, tt.findUserError).AnyTimes()

			mockGroupsRepository.
				EXPECT().FindGroupByUUID(gomock.AssignableToTypeOf(context.Background()), gomock.Eq(tt.organisationID)).
				Return(tt.stubGroup, tt.findGroupError).AnyTimes()

			mockChargesRepository.EXPECT().GetForDriver(ctx, tt.userID.String(), tt.organisationID.String(), gomock.Any(), gomock.Any()).
				Return(tt.expected, nil).AnyTimes()

			sut := NewService(logger, mockRepository, mockChargesRepository, mockUsersRepository, mockGroupsRepository, mockExpenseChargesBatchService)

			response, err := sut.GetForDriver(ctx, tt.userID, tt.organisationID, time.Now(), time.Now())

			if tt.wantError != nil {
				require.Equal(st, tt.wantError, err)
			}
			require.Equal(st, tt.expected, response)
		})
	}
}

func TestChargesService_GetForOrganisationDrivers(t *testing.T) {
	logger := log.New(os.Stderr, "[driver_unit_tests] ", log.Ltime)

	tests := []struct {
		name           string
		userIDs        []uuid.UUID
		organisationID uuid.UUID
		expected       models.OrganisationDriversChargeStatsCollection
		stubUser       *usersmodels.User
		findUserError  error
		stubGroup      *groupsmodels.Group
		findGroupError error
		wantError      error
	}{
		{
			name:           "Return driver charges when no errors",
			userIDs:        []uuid.UUID{uuid.New()},
			organisationID: uuid.New(),
			expected:       models.OrganisationDriversChargeStatsCollection{},
		},
		{
			name:           "Return org not found when group is not found",
			findGroupError: groups.ErrGroupNotFound,
			wantError:      ErrOrganisationNotFound,
		},
		{
			name:           "Returns unhandled error from group repository",
			findGroupError: errors.New("unhandled error"),
			wantError:      errors.New("unhandled error"),
		},
		{
			name: "Returning nil charges from repository results in empty slice",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(st *testing.T) {
			ctx := context.Background()

			ctrl := gomock.NewController(st)
			mockRepository := mocksubmittedcharges.NewMockRepository(ctrl)
			mockChargesRepository := mockcharges.NewMockRepository(ctrl)
			mockUsersRepository := mockusers.NewMockRepository(ctrl)
			mockGroupsRepository := mockgroups.NewMockRepository(ctrl)
			mockExpenseChargesBatchService := mock_charges.NewMockExpenseChargesBatchService(ctrl)

			mockUsersRepository.
				EXPECT().
				FindUserByUUID(gomock.AssignableToTypeOf(context.Background()), gomock.Eq(tt.userIDs)).
				Return(tt.stubUser, tt.findUserError).AnyTimes()

			mockGroupsRepository.
				EXPECT().FindGroupByUUID(gomock.AssignableToTypeOf(context.Background()), gomock.Eq(tt.organisationID)).
				Return(tt.stubGroup, tt.findGroupError).AnyTimes()

			mockChargesRepository.EXPECT().GetForOrganisationDrivers(ctx, tt.organisationID.String(), service.ToStringsIDs(tt.userIDs), gomock.Any(), gomock.Any()).
				Return(tt.expected, nil).AnyTimes()

			sut := NewService(logger, mockRepository, mockChargesRepository, mockUsersRepository, mockGroupsRepository, mockExpenseChargesBatchService)

			response, err := sut.GetForOrganisationDrivers(ctx, tt.organisationID, tt.userIDs, time.Now(), time.Now())

			if tt.wantError != nil {
				require.Equal(st, tt.wantError, err)
			}
			require.Equal(st, tt.expected, response)
		})
	}
}

func TestChargesService_InvokeExpenseChargeCommand(t *testing.T) {
	ctrl := gomock.NewController(t)
	groupUID := uuid.New()
	groupName := random.SiteName(t)
	tests := []struct {
		name                   string
		groupID                int64
		mockGroup              *groupsmodels.Group
		submittedCharges       models.SubmittedCharges
		expenseChargeCallCount int
	}{
		{
			name:    "calls expense charge command",
			groupID: gofakeit.Int64(),
			mockGroup: &groupsmodels.Group{
				ID:   gofakeit.Int64(),
				UID:  groupUID.String(),
				Name: groupName,
			},
			submittedCharges: models.SubmittedCharges{
				{
					ChargeID: int64(gofakeit.Uint32()),
				},
			},
			expenseChargeCallCount: 1,
		},
		{
			name:      "does not call expense charge command when group not found",
			groupID:   gofakeit.Int64(),
			mockGroup: nil,
			submittedCharges: models.SubmittedCharges{
				{
					ChargeID: int64(gofakeit.Uint32()),
				},
			},
			expenseChargeCallCount: 0,
		},
		{
			name:    "does not call expense charge command when group UID is not a uuid",
			groupID: gofakeit.Int64(),
			mockGroup: &groupsmodels.Group{
				ID:   gofakeit.Int64(),
				UID:  "bad uid",
				Name: groupName,
			},
			submittedCharges: models.SubmittedCharges{
				{
					ChargeID: int64(gofakeit.Uint32()),
				},
			},
			expenseChargeCallCount: 0,
		},
	}
	for _, tt := range tests {
		ctx := context.Background()
		mockGroupsRepository := mockgroups.NewMockRepository(ctrl)
		mockExpenseChargesBatchService := mock_charges.NewMockExpenseChargesBatchService(ctrl)

		mockGroupsRepository.EXPECT().FindGroupByID(ctx, tt.groupID).Return(tt.mockGroup, nil).Times(1)
		chargeIDs, conversionErr := numbers.Convert[int64, uint32](tt.submittedCharges[0].ChargeID)
		require.NoError(t, conversionErr)
		mockExpenseChargesBatchService.EXPECT().ExpenseChargesBatch(ctx, []uint32{chargeIDs}, groupUID, groupName).Times(tt.expenseChargeCallCount)

		var buff bytes.Buffer
		mockLogger := log.New(&buff, "[processor_test]", log.Lmsgprefix)

		sut := NewService(mockLogger, nil, nil, nil, mockGroupsRepository, mockExpenseChargesBatchService)
		sut.InvokeExpenseChargeCommand(ctx, tt.submittedCharges, tt.groupID)
	}
}
