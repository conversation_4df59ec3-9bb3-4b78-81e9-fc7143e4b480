package submittedcharges_test

import (
	"bytes"
	"context"
	"database/sql"
	"experience/apps/data-platform/api/pkg/charges"
	chargesmodels "experience/apps/data-platform/api/pkg/charges/models"
	"experience/apps/data-platform/api/pkg/common"
	submittedchargesinfra "experience/apps/data-platform/api/pkg/infra/submittedcharges"
	"experience/apps/data-platform/api/pkg/infra_test"
	submittedchargesdomain "experience/apps/data-platform/api/pkg/submittedcharges"
	"experience/apps/data-platform/api/pkg/submittedcharges/models"
	projectioncharges "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges"
	projectionsqlc "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/setup"
	"experience/libs/data-platform/test/sqlc"
	"experience/libs/shared/go/db"
	"experience/libs/shared/go/db/postgres/test"
	"experience/libs/shared/go/service/utils"
	"fmt"
	"log"
	"os"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

var (
	submittedTime  = time.Date(2022, time.April, 8, 21, 34, 0, 0, time.UTC)
	submittedTime2 = time.Date(2023, time.April, 8, 21, 34, 0, 0, time.UTC)
	april2022Start = time.Date(2022, time.April, 1, 0, 0, 0, 0, time.UTC)
	april2022End   = time.Date(2022, time.April, 30, 0, 0, 0, 0, time.UTC)
	april2023Start = time.Date(2023, time.April, 1, 0, 0, 0, 0, time.UTC)
	april2023End   = time.Date(2023, time.April, 30, 0, 0, 0, 0, time.UTC)
)

type SubmittedChargesRepositoryTestSuite struct {
	suite.Suite
	testDB            *test.Database
	queries           *sqlc.Queries
	projectionQueries *projectionsqlc.Queries

	underTest submittedchargesdomain.Repository
	logger    *log.Logger

	organisation                       sqlc.PodpointGroup
	organisation2                      sqlc.PodpointGroup
	organisation3                      sqlc.PodpointGroup
	organisation4                      sqlc.PodpointGroup
	submittedCharge                    sqlc.CommercialSubmittedCharge
	submittedCharge12                  sqlc.CommercialSubmittedCharge
	submittedCharge13                  sqlc.CommercialSubmittedCharge
	submittedCharge21                  sqlc.CommercialSubmittedCharge
	submittedCharge22                  sqlc.CommercialSubmittedCharge
	submittedCharge23                  sqlc.CommercialSubmittedCharge
	submittedHomeChargeNoEnergyCost    sqlc.CommercialSubmittedCharge
	submittedPublicChargeNoEnergyCost1 sqlc.CommercialSubmittedCharge
	submittedPublicChargeNoEnergyCost2 sqlc.CommercialSubmittedCharge
	submittedCharge41                  sqlc.CommercialSubmittedCharge
	submittedCharge51                  sqlc.CommercialSubmittedCharge
	submittedChargeForUnnamedUnit      sqlc.CommercialSubmittedCharge

	locationHome1           sqlc.PodpointPodLocation
	locationHome2           sqlc.PodpointPodLocation
	locationPublic2         sqlc.PodpointPodLocation
	locationWithUnnamedUnit sqlc.PodpointPodLocation

	charge1                         sqlc.PodpointCharge
	charge2                         sqlc.PodpointCharge
	charge3                         sqlc.PodpointCharge
	chargeCreate1                   sqlc.PodpointCharge
	chargeCreate2                   sqlc.PodpointCharge
	chargeCreate3                   sqlc.PodpointCharge
	user                            sqlc.PodpointUser
	driver1                         sqlc.PodpointUser
	driver2                         sqlc.PodpointUser
	driverForUnnamedUnit            sqlc.PodpointUser
	processedAt                     time.Time
	organisationWithNoCharges       sqlc.PodpointGroup
	organisationWithMultipleCharges sqlc.PodpointGroup
	organisationWithMultipleDrivers sqlc.PodpointGroup
	organisationWithOldCharges      sqlc.PodpointGroup
	organisationWithUnnamedUnit     sqlc.PodpointGroup
}

func TestSubmittedChargesRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping SubmittedChargesRepositoryTestSuite integration test")
	}
	suite.Run(t, new(SubmittedChargesRepositoryTestSuite))
}

func (s *SubmittedChargesRepositoryTestSuite) SetupSuite() {
	var err error
	ctx := s.T().Context()
	fixtures.Sequence.Reset()
	t := s.T()
	database := setup.NewPostgresTestDB(t, "file://../../../../../../libs/data-platform/golang-migrate/migration")
	s.testDB = database.TestDB
	s.queries = sqlc.New(database.DBHandle)
	s.projectionQueries = projectionsqlc.New(database.DBHandle)
	s.testDB = database.TestDB
	s.logger = log.New(os.Stderr, "[SubmittedChargesRepositoryTestSuite] ", log.Ltime)

	s.organisation, err = s.queries.CreateOrganisation(
		ctx,
		fixtures.CreateOrganisationParams(0, uuid.NewString(), "Test org1"),
	)
	require.NoError(t, err)

	s.organisation2, err = s.queries.CreateOrganisation(
		ctx,
		fixtures.CreateOrganisationParams(0, uuid.NewString(), "Test org2"),
	)
	require.NoError(t, err)

	s.organisation3, err = s.queries.CreateOrganisation(
		ctx,
		fixtures.CreateOrganisationParams(0, uuid.NewString(), "Test org3"),
	)
	require.NoError(t, err)

	s.organisation4, err = s.queries.CreateOrganisation(
		ctx,
		fixtures.CreateOrganisationParams(204, uuid.NewString(), "Test org4"),
	)
	require.NoError(t, err)

	_, _, s.locationHome1, _ = fixtures.PrepareChargeData(ctx, t, s.queries, true, false) //nolint:dogsled // test data that isn't needed

	// driver1 - only 2 home charges
	s.driver1, err = s.queries.CreateUser(
		ctx,
		fixtures.CreateUserParams(-101, uuid.NewString(), s.organisation, "<EMAIL>", "John", "Smith"),
	)
	require.NoError(t, err)

	chargeHome11, err := s.queries.CreateCharge(ctx, fixtures.CreateChargeParamsWithNullBillingAndUnit(0, &s.locationHome1, 61, "0.61"))
	require.NoError(t, err)
	chargeHome12, err := s.queries.CreateCharge(ctx, fixtures.CreateChargeParamsWithNullBillingAndUnit(0, &s.locationHome1, 40, "0.4"))
	require.NoError(t, err)
	chargeHome13, err := s.queries.CreateCharge(ctx, fixtures.CreateChargeParamsWithNullBillingAndUnit(0, &s.locationHome1, 77, "7"))
	require.NoError(t, err)
	// charge that is not submitted for expensing - to check it won't be included in totals
	_, err = s.queries.CreateCharge(ctx, fixtures.CreateChargeParamsWithNullBillingAndUnit(0, &s.locationHome1, 150, "1.5"))
	require.NoError(t, err)

	s.submittedCharge, err = s.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(s.organisation, s.driver1, chargeHome11, "John Doe", "NEW", submittedTime),
	)
	require.NoError(t, err)

	s.submittedCharge12, err = s.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(s.organisation, s.driver1, chargeHome12, "John Doe", "NEW", submittedTime),
	)
	require.NoError(t, err)

	s.submittedCharge13, err = s.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(s.organisation3, s.driver1, chargeHome13, "John Doe", "NEW", submittedTime),
	)
	require.NoError(t, err)

	// driver2 - 1 home and 2 public charges
	s.driver2, err = s.queries.CreateUser(
		ctx,
		fixtures.CreateUserParams(-102, uuid.NewString(), s.organisation, "<EMAIL>", "Steven", "Flynn"),
	)
	require.NoError(t, err)

	var unitHome1 sqlc.PodpointPodUnit
	var unitPublic1 sqlc.PodpointPodUnit
	unitHome1, _, s.locationHome2, _ = fixtures.PrepareChargeData(ctx, t, s.queries, true, false)
	unitPublic1, _, s.locationPublic2, _ = fixtures.PrepareChargeData(ctx, t, s.queries, false, false)

	chargeHome21, err := s.queries.CreateCharge(ctx, fixtures.CreateChargeParamsNoBillingEvent(0, &s.locationHome2, &unitHome1, 2001, "20.01", ptr.To(time.Date(2022, time.April, 8, 21, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC))))
	require.NoError(t, err)

	billingEvent21, err := s.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-1500))
	require.NoError(t, err)
	chargePublic21, err := s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, s.locationPublic2, unitPublic1, billingEvent21, 1500, "15.0", ptr.To(time.Date(2022, time.April, 8, 20, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	billingEvent22, err := s.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-502))
	require.NoError(t, err)

	params := fixtures.CreateChargeParamsNoBillingEvent(0, &s.locationHome2, &unitHome1, 0, "20.01", ptr.To(time.Date(2023, time.April, 8, 21, 34, 0, 0, time.UTC)), ptr.To(time.Date(2023, time.April, 11, 21, 34, 10, 0, time.UTC)))
	params.EnergyCost.Valid = false
	homeChargeWithNoEnergyCost, err := s.queries.CreateCharge(ctx, params)
	require.NoError(t, err)

	billingEvent23, err := s.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-1500))
	require.NoError(t, err)
	params = fixtures.CreateChargeParams(0, s.locationPublic2, unitPublic1, billingEvent23, 0, "15.0", ptr.To(time.Date(2023, time.April, 8, 20, 34, 0, 0, time.UTC)), ptr.To(time.Date(2023, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0)
	params.EnergyCost.Valid = false
	publicChargeWithNoEnergyCost1, err := s.queries.CreateCharge(ctx, params)
	require.NoError(t, err)

	billingEvent24, err := s.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-1500))
	require.NoError(t, err)
	params = fixtures.CreateChargeParams(0, s.locationPublic2, unitPublic1, billingEvent24, 0, "5.02", ptr.To(time.Date(2023, time.April, 8, 19, 34, 0, 0, time.UTC)), ptr.To(time.Date(2023, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0)
	params.EnergyCost.Valid = false
	publicChargeWithNoEnergyCost2, err := s.queries.CreateCharge(ctx, params)
	require.NoError(t, err)

	chargePublic22, err := s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, s.locationPublic2, unitPublic1, billingEvent22, 502, "5.02", ptr.To(time.Date(2022, time.April, 8, 19, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	s.submittedCharge21, err = s.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(s.organisation, s.driver2, chargeHome21, "Steven Flynn", "NEW", submittedTime),
	)
	require.NoError(t, err)

	s.submittedCharge22, err = s.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(s.organisation, s.driver2, chargePublic21, "Steven Flynn", "NEW", submittedTime),
	)
	require.NoError(t, err)

	s.submittedCharge23, err = s.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(s.organisation, s.driver2, chargePublic22, "xdp-api", "NEW", submittedTime),
	)
	require.NoError(t, err)

	s.submittedHomeChargeNoEnergyCost, err = s.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(s.organisation, s.driver2, homeChargeWithNoEnergyCost, "Steven Flynn", "NEW", submittedTime2),
	)
	require.NoError(t, err)

	s.submittedPublicChargeNoEnergyCost1, err = s.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(s.organisation, s.driver2, publicChargeWithNoEnergyCost1, "Steven Flynn", "NEW", submittedTime2),
	)
	require.NoError(t, err)

	s.submittedPublicChargeNoEnergyCost2, err = s.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(s.organisation, s.driver2, publicChargeWithNoEnergyCost2, "xdp-api", "NEW", submittedTime2),
	)
	require.NoError(t, err)

	// Create a separate user who approved a submitted charge
	fleetManager, err := s.queries.CreateUser(
		ctx,
		fixtures.CreateUserParams(0, uuid.NewString(), s.organisation, "<EMAIL>", "Fleet", "Manager"),
	)
	require.NoError(t, err)

	_, err = s.queries.SetStatusOnSubmittedChargesById(ctx, sqlc.SetStatusOnSubmittedChargesByIdParams{
		Status: "PROCESSED",
		ProcessedBy: sql.NullInt64{
			Int64: fleetManager.ID,
			Valid: true,
		},
		ProcessedAt: sql.NullTime{
			Time:  time.Date(2022, time.October, 8, 21, 34, 0, 0, time.UTC),
			Valid: true,
		},
		ID: []int64{s.submittedCharge23.ID, s.submittedCharge12.ID},
	})
	require.NoError(t, err)

	// driver3 - no charges
	_, err = s.queries.CreateUser(
		ctx,
		fixtures.CreateUserParams(0, uuid.NewString(), s.organisation, "<EMAIL>", "Gregory", "Ward"),
	)
	require.NoError(t, err)

	// driver4 - other organisation
	driver4, err := s.queries.CreateUser(
		ctx,
		fixtures.CreateUserParams(-104, uuid.NewString(), s.organisation2, "<EMAIL>", "Tom", "Banks"),
	)
	require.NoError(t, err)

	chargeHome41, err := s.queries.CreateCharge(ctx, fixtures.CreateChargeParamsWithNullBillingAndUnit(0, &s.locationHome1, 41, "0.82"))
	require.NoError(t, err)
	s.submittedCharge41, err = s.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(s.organisation2, driver4, chargeHome41, "Tom Banks", "NEW", submittedTime),
	)
	require.NoError(t, err)

	// driver5 - soft-deleted
	driver5, err := s.queries.CreateUser(
		ctx,
		fixtures.CreateDeletedUserParams(-105, uuid.NewString(), &s.organisation4, "<EMAIL>", "Abi", "Hill"),
	)
	require.NoError(t, err)

	chargeHome51, err := s.queries.CreateCharge(ctx, fixtures.CreateChargeParamsWithNullBillingAndUnit(0, &s.locationHome1, 41, "0.82"))
	require.NoError(t, err)
	s.submittedCharge51, err = s.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(s.organisation4, driver5, chargeHome51, "Abi Hill", "NEW", submittedTime),
	)
	require.NoError(t, err)

	//--------------- update seeder ------------
	unit, err := s.queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	require.NoError(t, err)

	address, err := s.queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(-900))
	require.NoError(t, err)

	location, err := s.queries.CreateLocation(ctx, fixtures.CreatePodPointLocationParams(&address, &unit, true, false))
	require.NoError(t, err)

	billingEvent21, err = s.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	billingEvent22, err = s.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	billingEventThree, err := s.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	s.charge1, err = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(1, location, unit, billingEvent21, 10, "7", ptr.To(time.Date(2022, time.April, 11, 21, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	s.charge2, err = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(2, location, unit, billingEvent22, 10, "7", ptr.To(time.Date(2022, time.April, 11, 21, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	s.charge3, err = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(3, location, unit, billingEventThree, 10, "7", ptr.To(time.Date(2022, time.April, 11, 21, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	s.user, err = s.queries.CreateUser(ctx, sqlc.CreateUserParams{})
	require.NoError(t, err)

	//--------------- create seeder ----------
	billingEvent31, err := s.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	billingEvent32, err := s.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	billingEvent33, err := s.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	s.chargeCreate1, err = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(4, location, unit, billingEvent31, 16, "10", ptr.To(time.Date(2022, time.April, 11, 21, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	s.chargeCreate2, err = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(5, location, unit, billingEvent32, 16, "10", ptr.To(time.Date(2022, time.April, 11, 21, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	s.chargeCreate3, err = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(6, location, unit, billingEvent33, 16, "10", ptr.To(time.Date(2022, time.April, 11, 21, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	// Fleet usage
	s.organisationWithNoCharges, err = s.queries.CreateOrganisation(ctx, fixtures.CreateOrganisationParams(0, uuid.NewString(), "Test org"))
	require.NoError(t, err)

	s.organisationWithMultipleCharges = organisationWithMultipleSubmittedCharges(ctx, s)
	s.organisationWithMultipleDrivers = organisationWithMultipleDrivers(ctx, s)
	s.organisationWithOldCharges = organisationWithOldCharges(ctx, s)
	s.organisationWithUnnamedUnit = organisationWithUnnameddUnit(ctx, s)

	dateTime := "25 Oct 22 12:00 +0000"
	clock := infra_test.FixedClock{T: t, Value: dateTime}
	s.processedAt, err = time.Parse(time.RFC822, dateTime)
	require.NoError(t, err)

	s.underTest = submittedchargesinfra.NewSubmittedChargesRepository(database.ReadWriteDB, clock, s.logger)
}

func (s *SubmittedChargesRepositoryTestSuite) TearDownSuite() {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

func organisationWithMultipleSubmittedCharges(ctx context.Context, testSuite *SubmittedChargesRepositoryTestSuite) sqlc.PodpointGroup {
	t := testSuite.T()
	organisation, err := testSuite.queries.CreateOrganisation(ctx, fixtures.CreateOrganisationParams(0, uuid.NewString(), "Test org"))
	require.NoError(t, err)

	unit, err := testSuite.queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	require.NoError(t, err)

	address, err := testSuite.queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(0))
	require.NoError(t, err)

	homeLocation := aLocation(ctx, testSuite, &address, &unit, true)

	billingEvent1, err := testSuite.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	charge1, err := testSuite.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, homeLocation, unit, billingEvent1, 16, "10", ptr.To(time.Now()), ptr.To(time.Now()), 0, 1, 1, 0))
	require.NoError(t, err)

	billingEvent2, err := testSuite.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	charge2, err := testSuite.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, homeLocation, unit, billingEvent2, 16, "10", ptr.To(time.Now()), ptr.To(time.Now()), 0, 1, 1, 0))
	require.NoError(t, err)

	billingEvent3, err := testSuite.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	charge3, err := testSuite.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, homeLocation, unit, billingEvent3, 16, "10", ptr.To(time.Now()), ptr.To(time.Now()), 0, 1, 1, 0))
	require.NoError(t, err)

	publicUnit, err := testSuite.queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	require.NoError(t, err)

	publicAddress, err := testSuite.queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(0))
	require.NoError(t, err)

	publicLocation := aLocation(ctx, testSuite, &publicAddress, &publicUnit, false)

	billingEvent4, err := testSuite.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	charge4, err := testSuite.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, publicLocation, unit, billingEvent4, 16, "10", ptr.To(time.Now()), ptr.To(time.Now()), 0, 1, 1, 0))
	require.NoError(t, err)

	user, err := testSuite.queries.CreateUser(
		ctx,
		fixtures.CreateUserParams(0, uuid.NewString(), organisation, "<EMAIL>", "John", "Smith"),
	)
	require.NoError(t, err)

	submitCharge(ctx, testSuite, &organisation, &user, &charge1)
	submitCharge(ctx, testSuite, &organisation, &user, &charge2)
	submitCharge(ctx, testSuite, &organisation, &user, &charge3)
	submitCharge(ctx, testSuite, &organisation, &user, &charge4)

	return organisation
}

func organisationWithMultipleDrivers(ctx context.Context, testSuite *SubmittedChargesRepositoryTestSuite) sqlc.PodpointGroup {
	t := testSuite.T()

	organisation, err := testSuite.queries.CreateOrganisation(ctx, fixtures.CreateOrganisationParams(0, uuid.NewString(), "Test org"))
	require.NoError(t, err)

	unit, err := testSuite.queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	require.NoError(t, err)

	address, err := testSuite.queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(0))
	require.NoError(t, err)

	homeLocation := aLocation(ctx, testSuite, &address, &unit, true)

	billingEvent1, err := testSuite.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	charge1, err := testSuite.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, homeLocation, unit, billingEvent1, 16, "10", ptr.To(time.Now()), ptr.To(time.Now()), 0, 1, 1, 0))
	require.NoError(t, err)

	billingEvent2, err := testSuite.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	charge2, err := testSuite.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, homeLocation, unit, billingEvent2, 16, "10", ptr.To(time.Now()), ptr.To(time.Now()), 0, 1, 1, 0))
	require.NoError(t, err)

	billingEvent3, err := testSuite.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	charge3, err := testSuite.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, homeLocation, unit, billingEvent3, 16, "10", ptr.To(time.Now()), ptr.To(time.Now()), 0, 1, 1, 0))
	require.NoError(t, err)

	publicUnit, err := testSuite.queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	require.NoError(t, err)

	publicAddress, err := testSuite.queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(0))
	require.NoError(t, err)

	publicLocation := aLocation(ctx, testSuite, &publicAddress, &publicUnit, false)

	billingEvent4, err := testSuite.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	charge4, err := testSuite.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, publicLocation, unit, billingEvent4, 16, "10", ptr.To(time.Now()), ptr.To(time.Now()), 0, 1, 1, 0))
	require.NoError(t, err)

	user, err := testSuite.queries.CreateUser(
		ctx,
		fixtures.CreateUserParams(0, uuid.NewString(), organisation, "<EMAIL>", "John", "Smith"),
	)
	require.NoError(t, err)

	user1, err := testSuite.queries.CreateUser(
		ctx,
		fixtures.CreateUserParams(0, uuid.NewString(), organisation, "<EMAIL>", "Dave", "Jones"),
	)
	require.NoError(t, err)

	submitCharge(ctx, testSuite, &organisation, &user, &charge1)
	submitCharge(ctx, testSuite, &organisation, &user, &charge2)
	submitCharge(ctx, testSuite, &organisation, &user, &charge3)
	submitCharge(ctx, testSuite, &organisation, &user1, &charge4)

	return organisation
}

func organisationWithUnnameddUnit(ctx context.Context, s *SubmittedChargesRepositoryTestSuite) sqlc.PodpointGroup {
	t := s.T()
	organisation, err := s.queries.CreateOrganisation(ctx, fixtures.CreateOrganisationParams(0, uuid.NewString(), uuid.NewString()))
	require.NoError(t, err)

	createUnitParams := sqlc.CreateUnitParams{ID: fixtures.Sequence.Get(), Ppid: random.ChargerID(), ModelID: fixtures.Sequence.Get(), Name: sql.NullString{}}
	oldUnit, err := s.queries.CreateUnit(ctx, createUnitParams)
	require.NoError(t, err)

	newUnit, err := s.queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, string(random.CommercialChargerID())))
	require.NoError(t, err)

	address, err := s.queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(0))
	require.NoError(t, err)

	s.locationWithUnnamedUnit = aLocation(ctx, s, &address, &newUnit, false)

	billingEvent, err := s.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	charge, err := s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, s.locationWithUnnamedUnit, oldUnit, billingEvent, 16, "10", ptr.To(time.Date(2022, time.April, 11, 21, 34, 0, 0, time.UTC)), ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	s.driverForUnnamedUnit, err = s.queries.CreateUser(
		ctx,
		fixtures.CreateUserParams(0, uuid.NewString(), organisation, "<EMAIL>", "John", "Smith"),
	)
	require.NoError(t, err)

	s.submittedChargeForUnnamedUnit = submitCharge(ctx, s, &organisation, &s.driverForUnnamedUnit, &charge)

	return organisation
}

func organisationWithOldCharges(ctx context.Context, testSuite *SubmittedChargesRepositoryTestSuite) sqlc.PodpointGroup {
	t := testSuite.T()
	organisation, err := testSuite.queries.CreateOrganisation(ctx, fixtures.CreateOrganisationParams(0, uuid.NewString(), "Test org"))
	require.NoError(t, err)

	unit, err := testSuite.queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	require.NoError(t, err)

	address, err := testSuite.queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(0))
	require.NoError(t, err)

	homeLocation := aLocation(ctx, testSuite, &address, &unit, true)

	billingEvent1, err := testSuite.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	outwithEndsAt := ptr.To(time.Date(2022, time.April, 8, 21, 34, 0, 0, time.UTC))
	charge1, err := testSuite.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, homeLocation, unit, billingEvent1, 16, "10", outwithEndsAt, ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	billingEvent2, err := testSuite.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	charge2, err := testSuite.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, homeLocation, unit, billingEvent2, 16, "10", outwithEndsAt, ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	billingEvent3, err := testSuite.queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750))
	require.NoError(t, err)

	charge3, err := testSuite.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, homeLocation, unit, billingEvent3, 16, "10", outwithEndsAt, ptr.To(time.Date(2022, time.April, 11, 21, 34, 10, 0, time.UTC)), 0, 1, 1, 0))
	require.NoError(t, err)

	user, err := testSuite.queries.CreateUser(
		ctx,
		fixtures.CreateUserParams(0, uuid.NewString(), organisation, "<EMAIL>", "John", "Smith"),
	)
	require.NoError(t, err)

	submitCharge(ctx, testSuite, &organisation, &user, &charge1)
	submitCharge(ctx, testSuite, &organisation, &user, &charge2)
	submitCharge(ctx, testSuite, &organisation, &user, &charge3)

	return organisation
}

func aLocation(ctx context.Context, testSuite *SubmittedChargesRepositoryTestSuite, address *sqlc.PodpointPodAddress, unit *sqlc.PodpointPodUnit, isHome bool) sqlc.PodpointPodLocation {
	t := testSuite.T()
	publicLocation, err := testSuite.queries.CreateLocation(ctx, fixtures.CreatePodPointLocationParams(address, unit, isHome, false))
	require.NoError(t, err)
	return publicLocation
}

func submitCharge(ctx context.Context, testSuite *SubmittedChargesRepositoryTestSuite, organisation *sqlc.PodpointGroup, user *sqlc.PodpointUser, charge *sqlc.PodpointCharge) sqlc.CommercialSubmittedCharge {
	sc, err := testSuite.queries.CreateSubmittedCharge(
		ctx,
		fixtures.CreateSubmittedChargeParams(*organisation, *user, *charge, "John Doe", "NEW", submittedTime),
	)
	require.NoError(testSuite.T(), err)
	return sc
}

// Does not run after every test, so we can make use of the seed data in SetupSuite() in the retrieve tests
func (s *SubmittedChargesRepositoryTestSuite) clearDB() {
	clearDBTable(s.T(), s.testDB)
}

type SubmittedChargeInput struct {
	OrganisationID string
	Status         string
	DriverID       int64
	From           time.Time
	To             time.Time
}

func (s *SubmittedChargesRepositoryTestSuite) TestRetrieveSubmittedChargesByOrganisation() {
	tests := []struct {
		name        string
		args        SubmittedChargeInput
		expected    models.SubmittedChargeCollection
		expectedErr error
	}{
		{
			name: "It returns charge data for an organisation",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation.Uid,
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeCollection{
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge21.ID),
					Driver:        common.NewDriver(int(s.submittedCharge21.DriverID), "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					StartTime:     "2022-04-08T21:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    2001,
					EnergyUsage:   20.01,
					Location: models.Location{
						ID:           int(s.locationHome2.ID),
						LocationType: "home",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "PP-10001",
					Status:      "NEW",
					ChargeID:    int(s.submittedCharge21.ChargeID),
				},
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge22.ID),
					Driver:        common.NewDriver(int(s.submittedCharge22.DriverID), "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					StartTime:     "2022-04-08T20:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    1500,
					EnergyUsage:   15,
					Location: models.Location{
						ID:           int(s.locationPublic2.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "Kent-Jake",
					Status:      "NEW",
					ChargeID:    int(s.submittedCharge22.ChargeID),
				},
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge23.ID),
					Driver:        common.NewDriver(int(s.submittedCharge23.DriverID), "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					StartTime:     "2022-04-08T19:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    502,
					EnergyUsage:   5.02,
					Location: models.Location{
						ID:           int(s.locationPublic2.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName:          "Kent-Jake",
					Status:               "PROCESSED",
					ProcessedByFirstName: "Fleet",
					ProcessedByLastName:  "Manager",
					ProcessedAt:          time.Date(2022, time.October, 8, 21, 34, 0, 0, time.UTC),
					ChargeID:             int(s.submittedCharge23.ChargeID),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It returns unknown unit when name is blank (XDP-1144)",
			args: SubmittedChargeInput{
				OrganisationID: s.organisationWithUnnamedUnit.Uid,
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeCollection{
				&models.SubmittedCharge{
					ID:            int(s.submittedChargeForUnnamedUnit.ID),
					Driver:        common.NewDriver(int(s.submittedChargeForUnnamedUnit.DriverID), "John", "Smith", "<EMAIL>", uuid.UUID{}),
					StartTime:     "2022-04-11T21:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					Location: models.Location{
						ID:           int(s.locationWithUnnamedUnit.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "UNKNOWN",
					ChargeCost:  6750,
					EnergyUsage: 10,
					Status:      "NEW",
					ChargeID:    int(s.submittedChargeForUnnamedUnit.ChargeID),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It returns charge data for an organisation when the events happened within the the specified time window",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation.Uid,
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeCollection{
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge21.ID),
					Driver:        common.NewDriver(int(s.submittedCharge21.DriverID), "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					StartTime:     "2022-04-08T21:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    2001,
					EnergyUsage:   20.01,
					Location: models.Location{
						ID:           int(s.locationHome2.ID),
						LocationType: "home",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "PP-10001",
					Status:      "NEW",
					ChargeID:    int(s.submittedCharge21.ChargeID),
				},
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge22.ID),
					Driver:        common.NewDriver(int(s.submittedCharge22.DriverID), "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					StartTime:     "2022-04-08T20:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    1500,
					EnergyUsage:   15,
					Location: models.Location{
						ID:           int(s.locationPublic2.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "Kent-Jake",
					Status:      "NEW",
					ChargeID:    int(s.submittedCharge22.ChargeID),
				},
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge23.ID),
					Driver:        common.NewDriver(int(s.submittedCharge23.DriverID), "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					StartTime:     "2022-04-08T19:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    502,
					EnergyUsage:   5.02,
					Location: models.Location{
						ID:           int(s.locationPublic2.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName:          "Kent-Jake",
					Status:               "PROCESSED",
					ProcessedByFirstName: "Fleet",
					ProcessedByLastName:  "Manager",
					ProcessedAt:          time.Date(2022, time.October, 8, 21, 34, 0, 0, time.UTC),
					ChargeID:             int(s.submittedCharge23.ChargeID),
				},
			},
			expectedErr: nil,
		},
		{
			name:        "It does not return charge data for an organisation when the events did not happen within the the specified time window",
			args:        SubmittedChargeInput{OrganisationID: s.organisation.Uid, From: time.Date(2078, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location()), To: time.Date(2099, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location())},
			expected:    models.SubmittedChargeCollection{},
			expectedErr: nil,
		},
	}

	for i := range tests {
		tt := tests[i]
		s.Run(tt.name, func() {
			expectedChargeUIDs := make([]uuid.UUID, len(tt.expected))
			for i, charge := range tt.expected {
				expectedChargeUIDs[i] = utils.FabricateUUIDFromNumericID(charge.ChargeID)
			}

			orgUID, _ := uuid.Parse(tt.args.OrganisationID)
			actual, actualChargeUIDs, err := s.underTest.GetByOrganisation(s.T().Context(), orgUID, tt.args.From, tt.args.To)
			t := s.T()
			if tt.expectedErr != nil {
				require.Equal(t, tt.expectedErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expected, actual)
				require.ElementsMatch(t, expectedChargeUIDs, actualChargeUIDs)
			}
		})
	}
}

func (s *SubmittedChargesRepositoryTestSuite) TestRetrieveSubmittedChargesByOrganisationAndStatus() {
	t := s.T()

	tests := []struct {
		name        string
		args        SubmittedChargeInput
		expected    models.SubmittedChargeCollection
		expectedErr error
	}{
		{
			name: "It returns charge data for NEW submitted charges",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation.Uid,
				Status:         "NEW",
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeCollection{
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge21.ID),
					Driver:        common.NewDriver(int(s.submittedCharge21.DriverID), "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					StartTime:     "2022-04-08T21:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    2001,
					EnergyUsage:   20.01,
					Location: models.Location{
						ID:           int(s.locationHome2.ID),
						LocationType: "home",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "PP-10001",
					Status:      "NEW",
					ChargeID:    int(s.submittedCharge21.ChargeID),
				},
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge22.ID),
					Driver:        common.NewDriver(int(s.submittedCharge22.DriverID), "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					StartTime:     "2022-04-08T20:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    1500,
					EnergyUsage:   15,
					Location: models.Location{
						ID:           int(s.locationPublic2.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "Kent-Jake",
					Status:      "NEW",
					ChargeID:    int(s.submittedCharge22.ChargeID),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It returns charge data for NEW submitted charges",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation.Uid,
				Status:         "PROCESSED",
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeCollection{
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge23.ID),
					Driver:        common.NewDriver(int(s.submittedCharge23.DriverID), "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					StartTime:     "2022-04-08T19:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    502,
					EnergyUsage:   5.02,
					Location: models.Location{
						ID:           int(s.locationPublic2.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName:          "Kent-Jake",
					Status:               "PROCESSED",
					ProcessedByFirstName: "Fleet",
					ProcessedByLastName:  "Manager",
					ProcessedAt:          time.Date(2022, time.October, 8, 21, 34, 0, 0, time.UTC),
					ChargeID:             int(s.submittedCharge23.ChargeID),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It returns submitted charged where charger is unnamed (XDP-1144)",
			args: SubmittedChargeInput{
				OrganisationID: s.organisationWithUnnamedUnit.Uid,
				Status:         "NEW",
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeCollection{
				&models.SubmittedCharge{
					ID:            int(s.submittedChargeForUnnamedUnit.ID),
					Driver:        common.NewDriver(int(s.submittedChargeForUnnamedUnit.DriverID), "John", "Smith", "<EMAIL>", uuid.Nil),
					StartTime:     "2022-04-11T21:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					Location: models.Location{
						ID:           int(s.locationWithUnnamedUnit.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "UNKNOWN",
					Status:      "NEW",
					EnergyUsage: 10,
					ChargeCost:  6750,
					ChargeID:    int(s.submittedChargeForUnnamedUnit.ChargeID),
				},
			},
		},
		{
			name: "returns submitted charge if the submitted time was in the given time window",
			args: SubmittedChargeInput{OrganisationID: s.organisationWithUnnamedUnit.Uid, Status: "NEW", From: time.Date(2022, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location()), To: time.Date(2023, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location())},
			expected: models.SubmittedChargeCollection{
				&models.SubmittedCharge{
					ID:            int(s.submittedChargeForUnnamedUnit.ID),
					Driver:        common.NewDriver(int(s.submittedChargeForUnnamedUnit.DriverID), "John", "Smith", "<EMAIL>", uuid.Nil),
					StartTime:     "2022-04-11T21:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					Location: models.Location{
						ID:           int(s.locationWithUnnamedUnit.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "UNKNOWN",
					Status:      "NEW",
					EnergyUsage: 10,
					ChargeCost:  6750,
					ChargeID:    int(s.submittedChargeForUnnamedUnit.ChargeID),
				},
			},
		},
		{
			name:     "does not return submitted charge if the submitted time was not in the given time window",
			args:     SubmittedChargeInput{OrganisationID: s.organisationWithUnnamedUnit.Uid, Status: "NEW", From: time.Date(2023, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location()), To: time.Date(2024, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location())},
			expected: models.SubmittedChargeCollection{},
		},
	}

	for i := range tests {
		tt := tests[i]
		t.Run(tt.name, func(t *testing.T) {
			orgUID, _ := uuid.Parse(tt.args.OrganisationID)
			status := tt.args.Status

			expectedChargeUIDs := make([]uuid.UUID, len(tt.expected))
			for i, charge := range tt.expected {
				expectedChargeUIDs[i] = utils.FabricateUUIDFromNumericID(charge.ChargeID)
			}

			actual, actualChargeUIDs, err := s.underTest.GetByOrganisationAndStatus(s.T().Context(), orgUID, &status, tt.args.From, tt.args.To)

			if tt.expectedErr != nil {
				require.Equal(t, tt.expectedErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expected, actual)
				require.ElementsMatch(t, expectedChargeUIDs, actualChargeUIDs)
			}
		})
	}
}

func (s *SubmittedChargesRepositoryTestSuite) TestRetrieveSubmittedChargesByOrganisationPerDriver() {
	t := s.T()

	tests := []struct {
		name        string
		args        SubmittedChargeInput
		expected    models.SubmittedChargeByDriverCollection
		expectedErr error
	}{
		{
			name: "It returns results aggregated by drivers for given organisation id",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation.Uid,
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{ // has home and public charges
					Driver:             common.NewDriver(-102, "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					TotalCharges:       3,
					SubmittedChargeIDs: []int64{s.submittedCharge21.ID, s.submittedCharge22.ID, s.submittedCharge23.ID},
					TotalUsage:         models.NewTotalUsage(20.01, 20.02),
					TotalCost:          models.NewTotalCost(2001, 2002),
				},
				&models.SubmittedChargeDriverSummary{ // has only home charges
					Driver:             common.NewDriver(-101, "John", "Smith", "<EMAIL>", uuid.Nil),
					TotalCharges:       2,
					SubmittedChargeIDs: []int64{s.submittedCharge.ID, s.submittedCharge12.ID},
					TotalUsage:         models.NewTotalUsage(1.01, 0.0),
					TotalCost:          models.NewTotalCost(101, 0),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It returns results aggregated by drivers for given organisation in between two dates and null energy cost",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation.Uid,
				From:           april2023Start,
				To:             april2023End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{ // has home and public charges
					Driver:             common.NewDriver(-102, "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					TotalCharges:       3,
					SubmittedChargeIDs: []int64{s.submittedHomeChargeNoEnergyCost.ID, s.submittedPublicChargeNoEnergyCost1.ID, s.submittedPublicChargeNoEnergyCost2.ID},
					TotalUsage:         models.NewTotalUsage(20.01, 20.02),
					TotalCost:          models.NewTotalCost(0, 3000),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It doesn't return drivers from other organisations",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation2.Uid,
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(-104, "Tom", "Banks", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedCharge41.ID},
					TotalUsage:         models.NewTotalUsage(0.82, 0.0),
					TotalCost:          models.NewTotalCost(41, 0),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It doesn't return charges submitted to other companies",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation3.Uid,
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(-101, "John", "Smith", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedCharge13.ID},
					TotalUsage:         models.NewTotalUsage(7, 0.0),
					TotalCost:          models.NewTotalCost(77, 0),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It does include charges submitted by soft-deleted users",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation4.Uid,
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(-105, "Abi", "Hill", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedCharge51.ID},
					TotalUsage:         models.NewTotalUsage(0.82, 0.0),
					TotalCost:          models.NewTotalCost(41, 0),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It returns summary when charger is unnamed (XDP-1144)",
			args: SubmittedChargeInput{
				OrganisationID: s.organisationWithUnnamedUnit.Uid,
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(int(s.submittedChargeForUnnamedUnit.DriverID), "John", "Smith", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedChargeForUnnamedUnit.ID},
					TotalUsage:         models.NewTotalUsage(0, 10.0),
					TotalCost:          models.NewTotalCost(0, 6750),
				},
			},
		},
		{
			name: "returns summary if the submitted time was in the given time window",
			args: SubmittedChargeInput{OrganisationID: s.organisationWithUnnamedUnit.Uid, Status: "NEW", From: time.Date(2022, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location()), To: time.Date(2023, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location())},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(int(s.submittedChargeForUnnamedUnit.DriverID), "John", "Smith", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedChargeForUnnamedUnit.ID},
					TotalUsage:         models.NewTotalUsage(0, 10.0),
					TotalCost:          models.NewTotalCost(0, 6750),
				},
			},
		},
		{
			name:     "does not return summary if the submitted time was not in the given time window",
			args:     SubmittedChargeInput{OrganisationID: s.organisationWithUnnamedUnit.Uid, Status: "NEW", From: time.Date(2023, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location()), To: time.Date(2024, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location())},
			expected: models.SubmittedChargeByDriverCollection{},
		},
	}

	for i := range tests {
		tt := tests[i]
		t.Run(tt.name, func(t *testing.T) {
			orgUID, _ := uuid.Parse(tt.args.OrganisationID)
			actual, err := s.underTest.GetByOrganisationPerDriver(s.T().Context(), orgUID, tt.args.From, tt.args.To)

			if tt.expectedErr != nil {
				require.Equal(t, tt.expectedErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expected, actual)
			}
		})
	}
}

func (s *SubmittedChargesRepositoryTestSuite) TestRetrieveSubmittedChargesByOrganisationAndStatusPerDriver() {
	t := s.T()

	tests := []struct {
		name        string
		args        SubmittedChargeInput
		expected    models.SubmittedChargeByDriverCollection
		expectedErr error
	}{
		{
			name: "It returns results aggregated by drivers for given organisation id only showing processed charges",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation.Uid,
				Status:         "PROCESSED",
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(-102, "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedCharge23.ID},
					TotalUsage:         models.NewTotalUsage(0, 5.02),
					TotalCost:          models.NewTotalCost(0, 502),
				},
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(-101, "John", "Smith", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedCharge12.ID},
					TotalUsage:         models.NewTotalUsage(0.4, 0.0),
					TotalCost:          models.NewTotalCost(40, 0),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It returns results aggregated by drivers for given organisation id only showing processed charges",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation.Uid,
				Status:         "NEW",
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(-102, "Steven", "Flynn", "<EMAIL>", uuid.Nil),
					TotalCharges:       2,
					SubmittedChargeIDs: []int64{s.submittedCharge21.ID, s.submittedCharge22.ID},
					TotalUsage:         models.NewTotalUsage(20.01, 15),
					TotalCost:          models.NewTotalCost(2001, 1500),
				},
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(-101, "John", "Smith", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedCharge.ID},
					TotalUsage:         models.NewTotalUsage(0.61, 0.0),
					TotalCost:          models.NewTotalCost(61, 0),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It doesn't return drivers from other organisations",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation2.Uid,
				Status:         "NEW",
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(-104, "Tom", "Banks", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedCharge41.ID},
					TotalUsage:         models.NewTotalUsage(0.82, 0.0),
					TotalCost:          models.NewTotalCost(41, 0),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It doesn't return charges submitted to other companies",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation3.Uid,
				Status:         "NEW",
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(-101, "John", "Smith", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedCharge13.ID},
					TotalUsage:         models.NewTotalUsage(7, 0.0),
					TotalCost:          models.NewTotalCost(77, 0),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It does include charges submitted by soft-deleted users",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation4.Uid,
				Status:         "NEW",
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(-105, "Abi", "Hill", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedCharge51.ID},
					TotalUsage:         models.NewTotalUsage(0.82, 0.0),
					TotalCost:          models.NewTotalCost(41, 0),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It returns summary when charger is unnamed (XDP-1144)",
			args: SubmittedChargeInput{
				OrganisationID: s.organisationWithUnnamedUnit.Uid,
				Status:         "NEW",
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(int(s.submittedChargeForUnnamedUnit.DriverID), "John", "Smith", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedChargeForUnnamedUnit.ID},
					TotalUsage:         models.NewTotalUsage(0, 10),
					TotalCost:          models.NewTotalCost(0, 6750),
				},
			},
			expectedErr: nil,
		},
		{
			name: "returns summary if the submitted time was in the given time window",
			args: SubmittedChargeInput{OrganisationID: s.organisationWithUnnamedUnit.Uid, Status: "NEW", From: time.Date(2022, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location()), To: time.Date(2023, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location())},
			expected: models.SubmittedChargeByDriverCollection{
				&models.SubmittedChargeDriverSummary{
					Driver:             common.NewDriver(int(s.submittedChargeForUnnamedUnit.DriverID), "John", "Smith", "<EMAIL>", uuid.Nil),
					TotalCharges:       1,
					SubmittedChargeIDs: []int64{s.submittedChargeForUnnamedUnit.ID},
					TotalUsage:         models.NewTotalUsage(0, 10.0),
					TotalCost:          models.NewTotalCost(0, 6750),
				},
			},
		},
		{
			name:     "does not return summary if the submitted time was not in the given time window",
			args:     SubmittedChargeInput{OrganisationID: s.organisationWithUnnamedUnit.Uid, Status: "NEW", From: time.Date(2023, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location()), To: time.Date(2024, 1, 1, 0, 0, 0, 0, time.Now().UTC().Location())},
			expected: models.SubmittedChargeByDriverCollection{},
		},
	}

	for i := range tests {
		tt := tests[i]
		t.Run(tt.name, func(t *testing.T) {
			orgUID, _ := uuid.Parse(tt.args.OrganisationID)
			status := tt.args.Status
			actual, err := s.underTest.GetByOrganisationAndStatusPerDriver(s.T().Context(), orgUID, &status, tt.args.From, tt.args.To)

			if tt.expectedErr != nil {
				require.Equal(t, tt.expectedErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expected, actual)
			}
		})
	}
}

func (s *SubmittedChargesRepositoryTestSuite) TestRetrieveDriverByID() {
	t := s.T()

	tests := []*struct {
		name        string
		args        SubmittedChargeInput
		expected    common.Driver
		expectedErr error
	}{
		{
			name:        "It returns driver details for a given driver ID",
			args:        SubmittedChargeInput{OrganisationID: s.organisation.Uid, DriverID: s.driver2.ID},
			expected:    common.NewDriver(int(s.driver2.ID), "Steven", "Flynn", "<EMAIL>", uuid.Nil),
			expectedErr: nil,
		},
	}

	for i := range tests {
		tt := tests[i]
		t.Run(tt.name, func(t *testing.T) {
			actual, err := s.underTest.GetDriverByID(s.T().Context(), tt.args.DriverID)

			if tt.expectedErr != nil {
				require.Equal(t, tt.expectedErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expected, actual)
			}
		})
	}
}

func (s *SubmittedChargesRepositoryTestSuite) TestRetrieveSubmittedChargesSummaryByOrganisationAndDriver() {
	t := s.T()

	tests := []struct {
		name        string
		args        SubmittedChargeInput
		expected    models.SubmittedChargesSummary
		expectedErr error
	}{
		{
			name: "It returns charge data summary for a driver with no public charges",
			args: SubmittedChargeInput{OrganisationID: s.organisation.Uid, DriverID: s.driver1.ID},
			expected: models.SubmittedChargesSummary{
				TotalUsage: models.NewTotalUsage(1.01, 0),
				TotalCost:  models.NewTotalCost(101, 0),
			},
			expectedErr: nil,
		},
		{
			name: "It returns charge data summary for a driver with home and public charges",
			args: SubmittedChargeInput{OrganisationID: s.organisation.Uid, DriverID: s.driver2.ID},
			expected: models.SubmittedChargesSummary{
				TotalUsage: models.NewTotalUsage(40.02, 40.04),
				TotalCost:  models.NewTotalCost(2001, 5002),
			},
			expectedErr: nil,
		},
	}

	for i := range tests {
		tt := tests[i]
		t.Run(tt.name, func(t *testing.T) {
			orgUID, _ := uuid.Parse(tt.args.OrganisationID)
			actual, err := s.underTest.GetSummaryByOrganisationAndDriver(s.T().Context(), orgUID, tt.args.DriverID)

			if tt.expectedErr != nil {
				require.Equal(t, tt.expectedErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expected, actual)
			}
		})
	}
}

func (s *SubmittedChargesRepositoryTestSuite) TestRetrieveSubmittedChargesByOrganisationAndDriver() {
	t := s.T()

	tests := []struct {
		name        string
		args        SubmittedChargeInput
		expected    models.SubmittedChargeCollection
		expectedErr error
	}{
		{
			name: "It returns charge data for a given org and driver with new/processed home/public charges.",
			args: SubmittedChargeInput{
				OrganisationID: s.organisation.Uid,
				DriverID:       s.driver2.ID,
				From:           april2022Start,
				To:             april2022End,
			},
			expected: models.SubmittedChargeCollection{
				&models.SubmittedCharge{
					ID:            int(s.submittedHomeChargeNoEnergyCost.ID),
					StartTime:     "2023-04-08T21:34:00Z",
					EndTime:       "2023-04-11T21:34:10Z",
					SubmittedTime: "2023-04-08T21:34:00Z",
					ChargeCost:    0,
					EnergyUsage:   20.01,
					Location: models.Location{
						ID:           int(s.locationHome2.ID),
						LocationType: "home",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "PP-10001",
					Status:      "NEW",
					Duration:    23,
					ChargeID:    int(s.submittedHomeChargeNoEnergyCost.ChargeID),
				},
				&models.SubmittedCharge{
					ID:            int(s.submittedPublicChargeNoEnergyCost1.ID),
					StartTime:     "2023-04-08T20:34:00Z",
					EndTime:       "2023-04-11T21:34:10Z",
					SubmittedTime: "2023-04-08T21:34:00Z",
					ChargeCost:    1500,
					EnergyUsage:   15,
					Location: models.Location{
						ID:           int(s.locationPublic2.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "Kent-Jake",
					Status:      "NEW",
					Duration:    23,
					ChargeID:    int(s.submittedPublicChargeNoEnergyCost1.ChargeID),
				},
				&models.SubmittedCharge{
					ID:            int(s.submittedPublicChargeNoEnergyCost2.ID),
					StartTime:     "2023-04-08T19:34:00Z",
					EndTime:       "2023-04-11T21:34:10Z",
					SubmittedTime: "2023-04-08T21:34:00Z",
					ChargeCost:    1500,
					EnergyUsage:   5.02,
					Location: models.Location{
						ID:           int(s.locationPublic2.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "Kent-Jake",
					Status:      "NEW",
					Duration:    23,
					ChargeID:    int(s.submittedPublicChargeNoEnergyCost2.ChargeID),
				},
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge21.ID),
					StartTime:     "2022-04-08T21:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    2001,
					EnergyUsage:   20.01,
					Location: models.Location{
						ID:           int(s.locationHome2.ID),
						LocationType: "home",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "PP-10001",
					Status:      "NEW",
					Duration:    23,
					ChargeID:    int(s.submittedCharge21.ChargeID),
				},
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge22.ID),
					StartTime:     "2022-04-08T20:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    1500,
					EnergyUsage:   15.00,
					Location: models.Location{
						ID:           int(s.locationPublic2.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "Kent-Jake",
					Status:      "NEW",
					Duration:    23,
					ChargeID:    int(s.submittedCharge22.ChargeID),
				},
				&models.SubmittedCharge{
					ID:            int(s.submittedCharge23.ID),
					StartTime:     "2022-04-08T19:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    502,
					EnergyUsage:   5.02,
					Location: models.Location{
						ID:           int(s.locationPublic2.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName:          "Kent-Jake",
					Status:               "PROCESSED",
					Duration:             23,
					ProcessedByFirstName: "Fleet",
					ProcessedByLastName:  "Manager",
					ProcessedAt:          time.Date(2022, time.October, 8, 21, 34, 0, 0, time.UTC),
					ChargeID:             int(s.submittedCharge23.ChargeID),
				},
			},
			expectedErr: nil,
		},
		{
			name: "It returns when charger is unnamed (XDP-1144)",
			args: SubmittedChargeInput{OrganisationID: s.organisationWithUnnamedUnit.Uid, DriverID: s.driverForUnnamedUnit.ID},
			expected: models.SubmittedChargeCollection{
				&models.SubmittedCharge{
					ID:            int(s.submittedChargeForUnnamedUnit.ID),
					StartTime:     "2022-04-11T21:34:00Z",
					EndTime:       "2022-04-11T21:34:10Z",
					SubmittedTime: "2022-04-08T21:34:00Z",
					ChargeCost:    6750,
					EnergyUsage:   10.00,
					Location: models.Location{
						ID:           int(s.locationWithUnnamedUnit.ID),
						LocationType: "public",
						Address: models.Address{
							Country:  ptr.To("GB"),
							Town:     "London",
							Line1:    "234 Banner St",
							Line2:    ptr.To("Line 2"),
							Postcode: ptr.To("EC1Y 8QE"),
						},
					},
					ChargerName: "UNKNOWN",
					Status:      "NEW",
					Duration:    23,
					ChargeID:    int(s.submittedChargeForUnnamedUnit.ChargeID),
				},
			},
		},
	}

	for i := range tests {
		tt := tests[i]
		t.Run(tt.name, func(t *testing.T) {
			expectedChargeUIDs := make([]uuid.UUID, len(tt.expected))
			for i, charge := range tt.expected {
				expectedChargeUIDs[i] = utils.FabricateUUIDFromNumericID(charge.ChargeID)
			}

			orgUID, _ := uuid.Parse(tt.args.OrganisationID)
			actual, actualChargeUIDs, err := s.underTest.GetByOrganisationAndDriver(s.T().Context(), orgUID, tt.args.DriverID)

			if tt.expectedErr != nil {
				require.Equal(t, tt.expectedErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expected, actual)
				require.ElementsMatch(t, expectedChargeUIDs, actualChargeUIDs)
			}
		})
	}
}

type SubmittedChargesInput struct {
	submittedCharges chargesmodels.SubmittedCharges
	organisationID   int64
	driverID         int64
}

type SubmittedChargesOutput struct {
	chargesmodels.SubmittedCharges
}

func (s *SubmittedChargesRepositoryTestSuite) TestCreate() {
	t := s.T()

	expectedDate, err := time.Parse(time.RFC822, "25 Oct 22 12:00 UTC")
	require.NoError(t, err)

	tests := []struct {
		name        string
		args        SubmittedChargesInput
		expected    SubmittedChargesOutput
		expectedErr error
	}{
		{
			name: "Creates single submitted charge",
			args: SubmittedChargesInput{
				submittedCharges: chargesmodels.SubmittedCharges{{
					ChargeID: s.chargeCreate1.ID,
				}},
				organisationID: 123,
				driverID:       456,
			},
			expected: SubmittedChargesOutput{
				SubmittedCharges: chargesmodels.SubmittedCharges{
					{
						ID:             1,
						ChargeID:       s.chargeCreate1.ID,
						OrganisationID: 123,
						DriverID:       456,
						SubmittedAt:    expectedDate,
						CreatedBy:      "xdp-api",
						CreatedAt:      expectedDate,
						Status:         chargesmodels.NEW,
					},
				},
			},
		},
		{
			name: "Creates multiple submittedCharges",
			args: SubmittedChargesInput{
				submittedCharges: chargesmodels.SubmittedCharges{{
					ChargeID: s.chargeCreate2.ID,
				}, {
					ChargeID: s.chargeCreate3.ID,
				}},
				organisationID: 123,
				driverID:       456,
			},
			expected: SubmittedChargesOutput{
				SubmittedCharges: chargesmodels.SubmittedCharges{
					{
						ID:             2,
						ChargeID:       s.chargeCreate2.ID,
						OrganisationID: 123,
						DriverID:       456,
						SubmittedAt:    expectedDate,
						CreatedBy:      "xdp-api",
						CreatedAt:      expectedDate,
						Status:         chargesmodels.NEW,
					},
					{
						ID:             3,
						ChargeID:       s.chargeCreate3.ID,
						OrganisationID: 123,
						DriverID:       456,
						SubmittedAt:    expectedDate,
						CreatedBy:      "xdp-api",
						CreatedAt:      expectedDate,
						Status:         chargesmodels.NEW,
					},
				},
			},
		},
		{
			name: "Charge not found error when submitting charge which does not exist",
			args: SubmittedChargesInput{
				submittedCharges: chargesmodels.SubmittedCharges{{
					ChargeID: 9999,
				}},
				organisationID: 123,
				driverID:       456,
			},
			expectedErr: charges.ErrChargeNotFound,
		},
		{
			name: "Multiple claims on same charge results in error",
			args: SubmittedChargesInput{
				submittedCharges: chargesmodels.SubmittedCharges{{
					ChargeID: s.chargeCreate2.ID,
				}, {
					ChargeID: s.chargeCreate2.ID,
				}},
				organisationID: 123,
				driverID:       456,
			},
			expectedErr: charges.ErrDuplicateSubmission,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual, err := s.underTest.Create(s.T().Context(), tt.args.submittedCharges, tt.args.organisationID, tt.args.driverID)
			if tt.expectedErr != nil {
				require.Equal(t, tt.expectedErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expected.SubmittedCharges, actual)
			}
		})
	}
}

func (s *SubmittedChargesRepositoryTestSuite) TestUpdateSingleCharge() {
	defer s.clearDB()

	// arrange
	ctx := s.T().Context()
	submittedCharges, err := s.underTest.Create(ctx, chargesmodels.SubmittedCharges{{
		ChargeID: s.charge2.ID,
	}}, 123, 456)

	require.NoError(s.T(), err)

	submittedChargeIDs := make([]int64, len(submittedCharges))

	for i, submittedCharge := range submittedCharges {
		submittedChargeIDs[i] = submittedCharge.ID
	}
	// act
	processedCharges, err := s.underTest.MarkAsProcessed(ctx, submittedChargeIDs, s.user.ID)

	// assert
	require.NoError(s.T(), err)
	for _, processedCharge := range processedCharges {
		require.Equal(s.T(), processedCharge.Status, models.PROCESSED)
		require.Equal(s.T(), processedCharge.ProcessedBy, s.user.ID)
		require.Equal(s.T(), processedCharge.ProcessedAt.UnixMilli(), s.processedAt.UnixMilli())
	}
}

func (s *SubmittedChargesRepositoryTestSuite) TestUpdateMultipleCharges() {
	defer s.clearDB()

	// arrange
	ctx := s.T().Context()
	submittedCharges, err := s.underTest.Create(ctx, chargesmodels.SubmittedCharges{{
		ChargeID: s.charge3.ID,
	}, {
		ChargeID: s.charge2.ID,
	}, {
		ChargeID: s.charge1.ID,
	}}, 123, 456)
	require.NoError(s.T(), err)

	submittedChargeIDs := make([]int64, len(submittedCharges))

	for i, submittedCharge := range submittedCharges {
		submittedChargeIDs[i] = submittedCharge.ID
	}

	// act
	processedCharges, err := s.underTest.MarkAsProcessed(ctx, submittedChargeIDs, s.user.ID)

	// assert
	require.NoError(s.T(), err)
	require.Len(s.T(), processedCharges, 3, "Should update 3 charges.")
	for _, processedCharge := range processedCharges {
		require.Equal(s.T(), processedCharge.Status, models.PROCESSED)
		require.Equal(s.T(), processedCharge.ProcessedBy, s.user.ID)
		require.Equal(s.T(), processedCharge.ProcessedAt.UnixMilli(), s.processedAt.UnixMilli())
	}
}

func (s *SubmittedChargesRepositoryTestSuite) TestUpdateProcessedCharges() {
	defer s.clearDB()

	// arrange
	ctx := s.T().Context()
	submittedCharges, err := s.underTest.Create(ctx, chargesmodels.SubmittedCharges{{
		ChargeID: s.charge3.ID,
	}, {
		ChargeID: s.charge2.ID,
	}, {
		ChargeID: s.charge1.ID,
	}}, 123, 456)
	require.NoError(s.T(), err)

	// mark one as processed, then we expect it not to be part of the update
	markedAsProcessed, err := s.underTest.MarkAsProcessed(ctx, []int64{submittedCharges[1].ID}, s.user.ID)
	require.NoError(s.T(), err)

	submittedChargeIDs := make([]int64, len(submittedCharges))
	for i, submittedCharge := range submittedCharges {
		submittedChargeIDs[i] = submittedCharge.ID
	}

	// act
	processedCharges, err := s.underTest.MarkAsProcessed(ctx, submittedChargeIDs, s.user.ID)

	// assert
	require.NoError(s.T(), err)
	require.Len(s.T(), processedCharges, 2, "Should update 2 of the 3 charges")
	require.NotContains(s.T(), processedCharges, markedAsProcessed, "Should not update previously processed charge")

	for _, processedCharge := range processedCharges {
		require.Equal(s.T(), processedCharge.Status, models.PROCESSED)
		require.Equal(s.T(), processedCharge.ProcessedBy, s.user.ID)
		require.Equal(s.T(), processedCharge.ProcessedAt.UnixMilli(), s.processedAt.UnixMilli())
	}
}

func (s *SubmittedChargesRepositoryTestSuite) TestUpdateChargeWhichDoesNotExist() {
	defer s.clearDB()

	// arrange
	ctx := s.T().Context()
	var submittedChargeIDs []int64
	submittedChargeIDs = append(submittedChargeIDs, 999)

	// act
	processedCharges, err := s.underTest.MarkAsProcessed(ctx, submittedChargeIDs, s.user.ID)

	// assert
	require.NoError(s.T(), err)
	require.Len(s.T(), processedCharges, 0, "Should update no charges.")
}

func (s *SubmittedChargesRepositoryTestSuite) TestUpdateChargeForUserWhoDoesNotExist() {
	defer s.clearDB()

	// arrange
	ctx := s.T().Context()
	submittedCharges, err := s.underTest.Create(ctx, chargesmodels.SubmittedCharges{{
		ChargeID: s.charge3.ID,
	}, {
		ChargeID: s.charge2.ID,
	}, {
		ChargeID: s.charge1.ID,
	}}, 123, 456)
	require.NoError(s.T(), err)

	submittedChargeIDs := make([]int64, len(submittedCharges))
	for i, submittedCharge := range submittedCharges {
		submittedChargeIDs[i] = submittedCharge.ID
	}

	// act
	_, err = s.underTest.MarkAsProcessed(ctx, submittedChargeIDs, -9999)

	// assert
	require.ErrorIs(s.T(), err, submittedchargesdomain.ErrApproverNotFound)
}

func (s *SubmittedChargesRepositoryTestSuite) TestRetrieveUsage() {
	ctx := s.T().Context()
	t := s.T()

	tests := []struct {
		name           string
		expected       models.FleetUsage
		expectedErr    error
		organisationID string
	}{
		{
			name:           "Organisation not found error for missing organisation",
			expectedErr:    submittedchargesdomain.ErrOrganisationNotFound,
			organisationID: uuid.NewString(),
		},
		{
			name:           "Organisation with no submitted charges returns empty usage",
			expected:       emptyFleetUsage(),
			organisationID: s.organisationWithNoCharges.Uid,
		},
		{
			name: "Multiple submitted charges",
			expected: models.FleetUsage{
				TotalCharges:       4,
				TotalChargesHome:   3,
				TotalChargesPublic: 1,
				KwhUsed:            40,
				KwhUsedHome:        30,
				KwhUsedPublic:      10,
				NumberOfDrivers:    1,
			},
			organisationID: s.organisationWithMultipleCharges.Uid,
		},
		{
			name: "Multiple drivers",
			expected: models.FleetUsage{
				TotalCharges:       4,
				TotalChargesHome:   3,
				TotalChargesPublic: 1,
				KwhUsed:            40,
				KwhUsedHome:        30,
				KwhUsedPublic:      10,
				NumberOfDrivers:    2,
			},
			organisationID: s.organisationWithMultipleDrivers.Uid,
		},
		{
			name: "Does not count charges outwith date range (before this month)",
			expected: models.FleetUsage{
				TotalCharges:       0,
				TotalChargesHome:   0,
				TotalChargesPublic: 0,
				KwhUsed:            0,
				KwhUsedHome:        0,
				KwhUsedPublic:      0,
				NumberOfDrivers:    0,
			},
			organisationID: s.organisationWithOldCharges.Uid,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual, err := s.underTest.RetrieveFleetUsage(ctx, uuid.MustParse(tt.organisationID))

			if tt.expectedErr != nil {
				require.Equal(t, tt.expectedErr, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expected, *actual)
			}
		})
	}
}

func (s *SubmittedChargesRepositoryTestSuite) TestFindProjectionsByAggregateIDs() {
	chargeUUID1 := uuid.New()
	chargeUUID2 := uuid.New()

	pluggedInAt := time.Date(2024, 1, 1, 5, 0, 0, 0, time.UTC)
	startedAt := pluggedInAt.Add(time.Minute * 1).UTC()
	endedAt := startedAt.Add(time.Minute * 47).UTC()
	unpluggedAt := endedAt.Add(time.Second * 38).UTC()
	energyCost := int32(1234)

	tests := []struct {
		name        string
		inputUUIDs  []uuid.UUID
		expected    []*projectioncharges.Projection
		expectedLog string
		queries     *projectionsqlc.Queries
		seed        bool
	}{
		{
			name:        "No projections found for given aggregate",
			inputUUIDs:  []uuid.UUID{chargeUUID1},
			expected:    []*projectioncharges.Projection{},
			expectedLog: fmt.Sprintf("No projection found for charge UID: %s", chargeUUID1.String()),
			seed:        false,
		},
		{
			name:       "Single projection found",
			inputUUIDs: []uuid.UUID{chargeUUID2},
			expected: []*projectioncharges.Projection{
				{
					ChargeUUID:  chargeUUID2,
					PluggedInAt: ptr.To(pluggedInAt),
					StartedAt:   ptr.To(startedAt),
					EndedAt:     ptr.To(endedAt),
					UnpluggedAt: ptr.To(unpluggedAt),
					EnergyCost:  ptr.To(energyCost),
				},
			},
			expectedLog: "",
			seed:        true,
		},
		{
			name:       "Multiple projections found",
			inputUUIDs: []uuid.UUID{chargeUUID1, chargeUUID2},
			expected: []*projectioncharges.Projection{
				{
					ChargeUUID:  chargeUUID1,
					PluggedInAt: ptr.To(pluggedInAt),
					StartedAt:   ptr.To(startedAt),
					EndedAt:     ptr.To(endedAt),
					UnpluggedAt: ptr.To(unpluggedAt),
					EnergyCost:  ptr.To(energyCost),
				},
				{
					ChargeUUID:  chargeUUID2,
					PluggedInAt: ptr.To(pluggedInAt),
					StartedAt:   ptr.To(startedAt),
					EndedAt:     ptr.To(endedAt),
					UnpluggedAt: ptr.To(unpluggedAt),
					EnergyCost:  ptr.To(energyCost),
				},
			},
			expectedLog: "",
			seed:        true,
		},
	}
	for _, tt := range tests {
		s.Run(tt.name, func() {
			t := s.T()
			ctx := s.T().Context()

			var logBuffer bytes.Buffer
			s.logger.SetOutput(&logBuffer)

			if tt.seed {
				createProjections(ctx, t, s.projectionQueries, tt.inputUUIDs, pluggedInAt, startedAt, endedAt, unpluggedAt, &energyCost)
			}
			actual, err := s.underTest.FindProjectionsByAggregateIDs(ctx, tt.inputUUIDs)

			if tt.expectedLog != "" {
				require.Contains(t, logBuffer.String(), tt.expectedLog)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.expected, actual)
			}
			s.logger.SetOutput(os.Stderr)
		})
	}
}

func emptyFleetUsage() models.FleetUsage {
	return models.FleetUsage{
		TotalCharges:       0,
		TotalChargesHome:   0,
		TotalChargesPublic: 0,
		KwhUsed:            0,
		KwhUsedHome:        0,
		KwhUsedPublic:      0,
		NumberOfDrivers:    0,
	}
}

func clearDBTable(t *testing.T, testDB *test.Database) {
	t.Helper()
	open, err := sql.Open("postgres", testDB.ConnectionString(t))
	require.NoError(t, err)
	_, err = open.Exec("DELETE FROM commercial.submitted_charges")
	require.NoError(t, err)
}

func createProjections(ctx context.Context, t *testing.T, queries *projectionsqlc.Queries, chargeUUIDs []uuid.UUID, pluggedInAt, startedAt, endedAt, unpluggedAt time.Time, energyCost *int32) {
	t.Helper()

	for _, chargeUUID := range chargeUUIDs {
		random.CreateProjectionsCharge(ctx, t, queries, &projectionsqlc.CreateChargeProjectionParams{
			ChargeUUID:           chargeUUID,
			PluggedInAt:          db.ToNullTime(ptr.To(pluggedInAt)),
			StartedAt:            db.ToNullTime(ptr.To(startedAt)),
			EndedAt:              db.ToNullTime(ptr.To(endedAt)),
			UnpluggedAt:          db.ToNullTime(ptr.To(unpluggedAt)),
			RewardEligibleEnergy: "0.0",
			EnergyCost:           db.ToNullInt32(energyCost),
		})
	}
}
