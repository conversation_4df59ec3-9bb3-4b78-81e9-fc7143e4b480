package main

import (
	"context"
	"experience/apps/data-platform/events-queue-worker/cmd/config"
	energycost "experience/libs/data-platform/cost-calculation/energy-cost"
	revenuecalc "experience/libs/data-platform/cost-calculation/revenue"
	"experience/libs/data-platform/cost-calculation/revenue/calculators"
	commandevents "experience/libs/data-platform/event-sourcing/domain/commands/charges"
	commandhandlers "experience/libs/data-platform/event-sourcing/domain/commands/charges/handlers"
	chargeevents "experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/authorisers"
	chargersevents "experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/cost"
	eventhandlers "experience/libs/data-platform/event-sourcing/domain/events/charges/handlers"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/revenue"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/rewards"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/sites"
	dbmigrate "experience/libs/data-platform/golang-migrate"
	"experience/libs/shared/go/converters"
	"experience/libs/shared/go/db/postgres"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/event-store/handlers"
	assetserviceapi "experience/libs/shared/go/external/asset-service-api/src"
	energymetricsapiclient "experience/libs/shared/go/external/energy-metrics-client/src"
	smartchargingserviceclient "experience/libs/shared/go/external/smart-charging-service-client/src"
	tariffsapiclient "experience/libs/shared/go/external/tariffs-api-client/src"
	"experience/libs/shared/go/sqs"
	xrayutils "experience/libs/shared/go/xray"
	"fmt"
	"os/signal"
	"sync"
	"syscall"
	"time"

	_ "time/tzdata" // required to ensure timezone database is present on Alpine for location based lookup
)

func main() {
	serviceName := "data-platform-events-queue-worker"
	mainCtx, stop := signal.NotifyContext(context.Background(), syscall.SIGTERM, syscall.SIGINT)
	defer stop()
	c, logger := config.NewEventsQueueWorkerConfig(mainCtx, serviceName)
	logger.Println("application starting")
	sqsClient, err := sqs.NewSQSClient(mainCtx)
	if err != nil {
		panic(err)
	}
	db := postgres.NewReadWriteDB(&c.ServiceDatasource, c.IsLocal(), postgres.WithXray())

	chargeEventsClient := sqs.NewClient(sqsClient, c.ChargeEventsURL, sqs.WithVisibilityTimeout(c.ChargeEventsVisibilityTimeout))
	eventStore := eventstore.NewPGEventStore(db.WriteDB)
	aggregateLoader := chargeevents.NewAggregateLoader(eventStore)
	authorisersRepository := authorisers.NewRepository(db.ReadDB)
	chargersEventsRepository := chargersevents.NewRepository(db.ReadDB)

	chargeEventsMessageHandler := chargeevents.NewEventManager(logger, chargeevents.NewSnsTransformer(chargeevents.Transformer{}), []handlers.SyncEventHandler{}, time.Now)
	sitesRepository := sites.NewRepository(db.ReadDB)
	costCalcRepository := cost.NewRepository(db.ReadDB)
	revenueRepository := revenue.NewRepository(db.ReadDB)

	durationRevenueCalculator := calculators.NewDurationRangeCalculator(revenueRepository)
	revenueCalculator := revenuecalc.NewCostCalculator(revenuecalc.RangeCalculatorAdapter(calculators.FixedRevenue), revenuecalc.RangeCalculatorAdapter(calculators.EnergyRevenue), durationRevenueCalculator)

	client, err := smartchargingserviceclient.NewRewardsAPIClient(c.IsProd(), string(c.SmartChargingServiceHost))
	if err != nil {
		logger.Fatalf("unable to initialise rewardsAPIClient: %v\n", err)
	}
	rewardsService := rewards.NewService(logger, client)
	attributeRewardableEnergyService := chargeevents.NewAttributeRewardableEnergyService(logger, chargeEventsMessageHandler, chargeEventsClient, rewardsService)

	assetSummaryAPIClient, err := assetserviceapi.NewAssetSummaryAPIClient(c.IsProd(), string(c.AssetServiceAPIHost))
	if err != nil {
		logger.Fatalf("unable to initialise assetSummaryAPIClient: %v\n", err)
	}
	assetSummary := cost.NewAssetSummaryService(assetSummaryAPIClient)

	tariffAPIClient, err := tariffsapiclient.NewChargingStationsTariffsAPIClient(c.IsProd(), string(c.TariffsAPIHost))
	if err != nil {
		logger.Fatalf("unable to initialise tariffAPIClient: %v\n", err)
	}
	tariff := cost.NewTariffService(tariffAPIClient, cost.ToEnergyCostTariffs)

	energyMetricsAPIClient, err := energymetricsapiclient.NewChargeMetricsClient(c.IsProd(), string(c.EnergyMetricsAPIHost))
	if err != nil {
		logger.Fatalf("unable to initialise energyMetricsAPIClient: %v\n", err)
	}
	energyMetrics := energycost.NewEnergyMetricsService(energyMetricsAPIClient, energycost.ToChargeInterval)

	billChargeService := chargeevents.NewBillChargeService(logger, aggregateLoader, chargeEventsMessageHandler, chargeEventsClient, revenueRepository, revenueCalculator)
	linkUserEventService := chargeevents.NewLinkUserService(logger, chargeEventsClient, aggregateLoader, chargersEventsRepository, &chargeevents.UserAdder{})
	claimChargeService := chargeevents.NewClaimChargeService(logger, chargeEventsClient, aggregateLoader, chargersEventsRepository, sitesRepository, authorisersRepository, chargeEventsMessageHandler)
	confirmChargeService := chargeevents.NewConfirmChargeService(logger, chargeEventsClient, aggregateLoader, chargeEventsMessageHandler, authorisersRepository)
	costChargeService := chargeevents.NewCostChargeService(logger, chargeEventsClient, aggregateLoader, chargeEventsMessageHandler, energycost.NewEnergyCostCalculator(), costCalcRepository, converters.UUIDStringToIntConverter, assetSummary, tariff, energyMetrics)
	chargeProjectionsUpdater := eventhandlers.NewChargeProjectionsUpdater(eventhandlers.NewDataStore(db.WriteDB))

	chargeCommandsClient := sqs.NewClient(sqsClient, c.ChargeCommandsURL, sqs.WithVisibilityTimeout(30))

	expectedErrors := []error{
		// This error can happen when events for the same aggregate get processed
		// concurrently. No need to worry most of the time as SQS is retrying
		// these already automatically for us.
		eventstore.ErrEventIsOutOfDate,
	}
	chargeEventsProcessor := sqs.NewProcessor(chargeEventsClient,
		logger,
		fmt.Sprintf("%s-events", serviceName),
		sqs.WithRedrivePolicyMaxReceiveCount(3),
		sqs.WithExpectedErrorsList(expectedErrors),
	)
	chargeCommandsProcessor := sqs.NewProcessor(
		chargeCommandsClient,
		logger,
		fmt.Sprintf("%s-commands", serviceName),
		sqs.WithRedrivePolicyMaxReceiveCount(3),
		sqs.WithExpectedErrorsList(expectedErrors),
	)
	chargeCommandHandler := commandhandlers.NewClaimChargeCommandHandler(claimChargeService)
	costChargeCommandHandler := commandhandlers.NewCostChargeCommandHandler(costChargeService)
	addUserCommandHandler := commandhandlers.NewAddUserCommandHandler(linkUserEventService)
	confirmChargeCommandHandler := commandhandlers.NewConfirmChargeCommandHandler(confirmChargeService)
	recalculateEnergyCostCommandHandler := commandhandlers.NewRecalculateEnergyCostCommandHandler(costChargeService)
	billChargeCommandHandler := commandhandlers.NewBillChargeCommandHandler(billChargeService)
	attributeRewardableEnergyCommandHandler := commandhandlers.NewAttributeRewardableEnergyCommandHandler(attributeRewardableEnergyService)
	chargeCommandsMessageHandler := commandevents.NewCommandManager(commandevents.CommandTransformer{}, []handlers.CommandHandler{chargeCommandHandler, costChargeCommandHandler, addUserCommandHandler, confirmChargeCommandHandler, recalculateEnergyCostCommandHandler, billChargeCommandHandler, attributeRewardableEnergyCommandHandler})

	claimChargeHandler := eventhandlers.NewClaimChargeHandler(chargeCommandsClient)
	confirmChargeHandler := eventhandlers.NewConfirmChargeHandler(chargeCommandsClient)
	costChargeHandler := eventhandlers.NewCostChargeHandler(chargeCommandsClient)
	billChargeHandler := eventhandlers.NewBillChargeHandler(chargeCommandsClient)
	attributePointsHandler := eventhandlers.NewAttributeRewardableEnergy(chargeCommandsClient)
	chargeEventsMessageHandler.BindHandlers([]handlers.SyncEventHandler{chargeProjectionsUpdater, claimChargeHandler, costChargeHandler, confirmChargeHandler, billChargeHandler, attributePointsHandler})

	dbmigrate.MigrateUp(dbmigrate.NewMigrateConfig(c.Config, c.MigrateDatasource), logger)

	xrayutils.EnableTracing(logger, "./apps/data-platform/events-queue-worker/cmd/config/tracing/fallback_xray_sampling.json")

	var wg sync.WaitGroup
	cancellationCtx, cancel := context.WithCancel(mainCtx)
	wg.Add(1)
	go func() {
		defer wg.Done()

		// Start SQS Consumers
		chargeEventsProcessor.Start(cancellationCtx, chargeEventsMessageHandler)
		chargeCommandsProcessor.Start(cancellationCtx, chargeCommandsMessageHandler)

		// Wait until sigterm/interrupt
		<-mainCtx.Done()
		cancel() // Cancel message processor go routines
		_, shutdownCancel := context.WithTimeout(mainCtx, time.Second*30)
		defer shutdownCancel()
	}()

	wg.Wait()
	logger.Println("exited")
}
