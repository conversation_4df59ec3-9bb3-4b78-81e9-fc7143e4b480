package sqs

import (
	"context"
	"log"
	"sync"
	"time"

	"k8s.io/utils/ptr"

	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"github.com/aws/aws-xray-sdk-go/v2/xray"
)

type ClientOperations interface {
	DeleteMessage(ctx context.Context, handle ReceiptHandle) error
	ReceiveMessage(ctx context.Context) (Messages, error)
	SendMessage(ctx context.Context, message any) (MessageID, error)
	SendMessageBatch(ctx context.Context, batch []string) error
	GetQueueDepth(ctx context.Context) (int64, error)
}

func WithRedrivePolicyMaxReceiveCount(maxReceiveCount int) func(*Processor) {
	return func(p *Processor) {
		p.redrivePolicyMaxReceiveCount = maxReceiveCount
	}
}

func WithExpectedErrorsList(expectedErrors []error) func(*Processor) {
	return func(p *Processor) {
		p.expectedErrors = expectedErrors
	}
}

func NewProcessor(sqsClient ClientOperations, l *log.Logger, serviceName string, options ...func(*Processor)) Processor {
	processor := Processor{
		client:                       sqsClient,
		logger:                       l,
		NumberOfWorkers:              2,
		serviceName:                  serviceName,
		redrivePolicyMaxReceiveCount: 10,
	}

	for _, option := range options {
		option(&processor)
	}

	return processor
}

type Processor struct {
	client                       ClientOperations
	logger                       *log.Logger
	NumberOfWorkers              int
	serviceName                  string
	redrivePolicyMaxReceiveCount int
	expectedErrors               []error
}

var XrayAddError = xray.AddError

// Start will start worker go routines that listen to the queue configured on the client and process messages
// received from the queue.
//
// NumberOfWorkers is used to determine how many go routines are created.
//
// ctx - used to control the execution of the works. Calling ctx.Done() will schedule them to stop.
// handler - message handler that encapsulates any bespoke processing to happen on each message (must be thread safe)
func (p *Processor) Start(ctx context.Context, handler MessageHandler) {
	for w := 0; w < p.NumberOfWorkers; w++ {
		go p.start(ctx, handler)
	}
}

// ProcessMessages will look to receive messages from the queue and process them.
// Which queue and the number of messages processed are configured via the client.
//
// Processing involves: receiving messages -> invoke handler -> delete message.
//
// If there is an issue retrieving messages then the process will exit. If however there
// are issues either when invoking the handler or deleting the message, an error will be logged
// but processing of subsequent messages will continue.
//
// ctx - passed to the receiver, handler and delete message operations.
// handler - message handler that encapsulates any bespoke processing to happen on each message (must be thread safe)
func (p *Processor) ProcessMessages(ctx context.Context, handler MessageHandler) {
	xrayCtx, segment := xray.BeginSegment(ctx, p.serviceName)
	defer segment.Close(nil)

	messages, err := p.client.ReceiveMessage(xrayCtx)
	if err != nil {
		p.logger.Printf("error receiving message: %v", err)
		xErr := xray.AddError(xrayCtx, err)
		if xErr != nil {
			p.logger.Printf("adding error to trace: %v", xErr)
		}
		time.Sleep(time.Millisecond * 200)
		return
	}

	err = xray.AddAnnotation(xrayCtx, "number_of_processed_messages", len(messages))
	if err != nil {
		p.logger.Printf("Error adding annotation number_of_processed_messages to X-Ray trace: %v", err)
	}

	var wg sync.WaitGroup
	for _, message := range messages {
		wg.Add(1)
		subctx, s := xray.BeginSubsegment(xrayCtx, "process_message")
		go func(message types.Message) {
			defer wg.Done()
			defer s.Close(nil)
			msg := Message(message)
			procErr := handler.Process(subctx, msg)
			if procErr != nil {
				p.HandleProcessError(subctx, p.redrivePolicyMaxReceiveCount, procErr, message)
				return
			}
			deleteErr := p.client.DeleteMessage(subctx, message.ReceiptHandle)
			if deleteErr != nil {
				p.logger.Printf("error deleting message: %v, %v", ptr.Deref(message.MessageId, ""), deleteErr)
				xErr := xray.AddError(subctx, deleteErr)
				if xErr != nil {
					p.logger.Printf("adding error to trace: %v", xErr)
				}
				time.Sleep(time.Millisecond * 200)
				return
			}
		}(message)

		wg.Wait()
	}
}

func (p *Processor) start(ctx context.Context, handler MessageHandler) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			p.ProcessMessages(context.Background(), handler)
		}
	}
}
