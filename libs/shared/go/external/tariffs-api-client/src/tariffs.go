package tariffsapiclient

import (
	"bytes"
	"context"
	"errors"
	"io"
	"net/http"

	"github.com/aws/aws-xray-sdk-go/v2/xray"
	"k8s.io/utils/ptr"
)

var userAgent = "xdp-tariffs-api-client"

const StubTariffRate = 0.3

type TariffsAPIStub struct{}

func NewChargingStationsTariffsAPIClient(isProd bool, host string) (ChargingStationsTariffsAPI, error) {
	if host == "" {
		return nil, errors.New("NewChargingStationsTariffsAPIClient: host is required")
	}

	if isProd {
		cfg := NewConfiguration()
		cfg.HTTPClient = xray.Client(nil)
		cfg.UserAgent = userAgent
		cfg.Servers = ServerConfigurations{{
			URL: host,
		},
		}
		client := NewAPIClient(cfg)
		return client.ChargingStationsTariffsAPI, nil
	}

	return &TariffsAPIStub{}, nil
}

func (t TariffsAPIStub) CreateTariffForChargingStation(_ context.Context, _ string) ApiCreateTariffForChargingStationRequest {
	// TODO implement me
	panic("implement me")
}

func (t TariffsAPIStub) CreateTariffForChargingStationExecute(_ ApiCreateTariffForChargingStationRequest) (*PersistedTariffRowDto, *http.Response, error) {
	// TODO implement me
	panic("implement me")
}

func (t TariffsAPIStub) DeleteAllTariffForChargingStation(_ context.Context, _ string) ApiDeleteAllTariffForChargingStationRequest {
	// TODO implement me
	panic("implement me")
}

func (t TariffsAPIStub) DeleteAllTariffForChargingStationExecute(_ ApiDeleteAllTariffForChargingStationRequest) (*http.Response, error) {
	// TODO implement me
	panic("implement me")
}

func (t TariffsAPIStub) DeleteTariff(_ context.Context, _, _ string) ApiDeleteTariffRequest {
	// TODO implement me
	panic("implement me")
}

func (t TariffsAPIStub) DeleteTariffExecute(_ ApiDeleteTariffRequest) (*http.Response, error) {
	// TODO implement me
	panic("implement me")
}

func (t TariffsAPIStub) GetTariffsByPpid(ctx context.Context, ppid string) ApiGetTariffsByPpidRequest {
	return ApiGetTariffsByPpidRequest{
		ctx:        ctx,
		ApiService: &t,
		ppid:       ppid,
	}
}

func (t TariffsAPIStub) GetTariffsByPpidExecute(r ApiGetTariffsByPpidRequest) (*ChargingStationTariffSearchResponseDto, *http.Response, error) {
	stubResponse := http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(bytes.NewBufferString("")),
	}

	stubResponseDto := ChargingStationTariffSearchResponseDto{
		Data: []PersistedTariffRowDto{
			{
				Ppid: r.ppid,
				SupplierId: NullableString{
					value: ptr.To("3b137bf9-68ef-44f3-bd09-ab4e5783ca5a"),
					isSet: true,
				},
				TariffInfo: []TariffInfoDto{
					{
						Start: "00:00:00",
						End:   "00:00:00",
						Price: StubTariffRate,
						Days:  []string{"MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"},
					},
				},
				Timezone:          "Europe/London",
				EffectiveFrom:     "2021-01-01",
				Id:                "3fa85f64-5717-4562-b3fc-2c963f66afa6",
				CheapestUnitPrice: 0.2,
			},
		},
	}

	return &stubResponseDto, &stubResponse, nil
}

func (t TariffsAPIStub) UpdateTariff(_ context.Context, _, _ string) ApiUpdateTariffRequest {
	// TODO implement me
	panic("implement me")
}

func (t TariffsAPIStub) UpdateTariffExecute(_ ApiUpdateTariffRequest) (*PersistedTariffRowDto, *http.Response, error) {
	// TODO implement me
	panic("implement me")
}
