package transactionsapiclient

import (
	"bytes"
	"context"
	"errors"
	"io"
	"net/http"

	"github.com/aws/aws-xray-sdk-go/v2/xray"
)

var userAgent = "xdp-transactions-api-client"

type TransactionsAPIStub struct {
}

func NewTransactionsAPIClient(isProd bool, transactionsAPIHost string) (DefaultAPI, error) {
	if transactionsAPIHost == "" {
		return nil, errors.New("NewTransactionsAPIClient: host is required")
	}

	if isProd {
		cfg := NewConfiguration()
		cfg.HTTPClient = xray.Client(nil)
		cfg.UserAgent = userAgent
		cfg.Servers = ServerConfigurations{{
			URL: transactionsAPIHost,
		},
		}
		client := NewAPIClient(cfg)
		return client.DefaultAPI, nil
	}

	return &TransactionsAPIStub{}, nil
}

func (t TransactionsAPIStub) TransactionsPost(ctx context.Context) ApiTransactionsPostRequest {
	return ApiTransactionsPostRequest{
		ctx:                                  ctx,
		ApiService:                           &t,
		handlersCreateTransactionRequestBody: NewHandlersCreateTransactionRequestBodyWithDefaults(),
	}
}

func (t TransactionsAPIStub) TransactionsPostExecute(_ ApiTransactionsPostRequest) (*HandlersCreateTransactionResponseBody, *http.Response, error) {
	transactionID := int32(12345)
	responseBody := `{"id": 12345}`

	stubResponse := &http.Response{
		Body:       io.NopCloser(bytes.NewBufferString(responseBody)),
		StatusCode: http.StatusOK,
	}
	mockResponse := &HandlersCreateTransactionResponseBody{
		Id: &transactionID,
	}

	return mockResponse, stubResponse, nil
}

func (t TransactionsAPIStub) TransactionsStartPost(ctx context.Context) ApiTransactionsStartPostRequest {
	return ApiTransactionsStartPostRequest{
		ctx:                                 ctx,
		ApiService:                          &t,
		handlersStartTransactionRequestBody: NewHandlersStartTransactionRequestBodyWithDefaults(),
	}
}

func (t TransactionsAPIStub) TransactionsStartPostExecute(_ ApiTransactionsStartPostRequest) (*HandlersTransactionActionResponseBody, *http.Response, error) {
	status := "started"
	message := "Transaction started successfully"

	responseBody := `{"status": "started", "message": "Transaction started successfully"}`

	stubResponse := &http.Response{
		Body:       io.NopCloser(bytes.NewBufferString(responseBody)),
		StatusCode: http.StatusCreated,
		Header:     make(http.Header),
	}

	mockResponse := &HandlersTransactionActionResponseBody{
		Status:  &status,
		Message: &message,
	}

	return mockResponse, stubResponse, nil
}

func (t TransactionsAPIStub) TransactionsStopPost(ctx context.Context) ApiTransactionsStopPostRequest {
	return ApiTransactionsStopPostRequest{
		ctx:        ctx,
		ApiService: &t,
		handlersStopTransactionByParametersRequestBody: NewHandlersStopTransactionByParametersRequestBodyWithDefaults(),
	}
}

func (t TransactionsAPIStub) TransactionsStopPostExecute(_ ApiTransactionsStopPostRequest) (*HandlersTransactionActionResponseBody, *http.Response, error) {
	status := "stopped"
	message := "Transaction stopped successfully"

	responseBody := `{"status": "stopped", "message": "Transaction stopped successfully"}`

	stubResponse := &http.Response{
		Body:       io.NopCloser(bytes.NewBufferString(responseBody)),
		StatusCode: http.StatusOK,
		Header:     make(http.Header),
	}

	mockResponse := &HandlersTransactionActionResponseBody{
		Status:  &status,
		Message: &message,
	}

	return mockResponse, stubResponse, nil
}

//nolint:revive // linter not happy with the name
func (t TransactionsAPIStub) TransactionsTransactionIdStopPost(ctx context.Context, transactionId int32) ApiTransactionsTransactionIdStopPostRequest {
	return ApiTransactionsTransactionIdStopPostRequest{
		ctx:                                ctx,
		ApiService:                         &t,
		transactionId:                      transactionId,
		handlersStopTransactionRequestBody: NewHandlersStopTransactionRequestBodyWithDefaults(),
	}
}

//nolint:revive // linter not happy with the name
func (t TransactionsAPIStub) TransactionsTransactionIdStopPostExecute(_ ApiTransactionsTransactionIdStopPostRequest) (*HandlersTransactionActionResponseBody, *http.Response, error) {
	status := "stopped"
	message := "Transaction stopped successfully"

	responseBody := `{"status": "stopped", "message": "Transaction stopped successfully"}`

	stubResponse := &http.Response{
		Body:       io.NopCloser(bytes.NewBufferString(responseBody)),
		StatusCode: http.StatusOK,
		Header:     make(http.Header),
	}

	mockResponse := &HandlersTransactionActionResponseBody{
		Status:  &status,
		Message: &message,
	}

	return mockResponse, stubResponse, nil
}
