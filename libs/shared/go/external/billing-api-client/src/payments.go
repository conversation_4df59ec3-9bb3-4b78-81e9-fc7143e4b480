package billingapiclient

import (
	"bytes"
	"context"
	"errors"
	"io"
	"net/http"

	"github.com/aws/aws-xray-sdk-go/v2/xray"
)

var userAgent = "xdp-payments-api-client"

type PaymentsAPIStub struct {
}

func NewPaymentsAPIClient(isProd bool, billingAPIHost string) (PaymentsAPI, error) {
	if billingAPIHost == "" {
		return nil, errors.New("NewPaymentsAPIClient: host is required")
	}

	if isProd {
		cfg := NewConfiguration()
		cfg.UserAgent = userAgent
		cfg.HTTPClient = xray.Client(nil)
		cfg.Servers = ServerConfigurations{{
			URL: billingAPIHost,
		},
		}
		client := NewAPIClient(cfg)
		return client.PaymentsAPI, nil
	}

	return &PaymentsAPIStub{}, nil
}

func (p PaymentsAPIStub) PaymentControllerCaptureGuestPayment(ctx context.Context) ApiPaymentControllerCaptureGuestPaymentRequest {
	return ApiPaymentControllerCaptureGuestPaymentRequest{
		ctx:                   ctx,
		ApiService:            &p,
		capturePaymentRequest: NewCapturePaymentRequestWithDefaults(),
	}
}

func (p PaymentsAPIStub) PaymentControllerCaptureGuestPaymentExecute(_ ApiPaymentControllerCaptureGuestPaymentRequest) (*CapturePaymentIntentResponse, *http.Response, error) {
	stubResponse := http.Response{
		StatusCode: http.StatusCreated,
		Body:       io.NopCloser(bytes.NewBufferString(`{"paymentIntentStatus": "succeeded"}`)),
	}
	mockResponse := CapturePaymentIntentResponse{
		PaymentIntentStatus: "succeeded",
	}
	return &mockResponse, &stubResponse, nil
}

func (p PaymentsAPIStub) PaymentControllerCreateGuestPaymentIntent(_ context.Context) ApiPaymentControllerCreateGuestPaymentIntentRequest {
	// TODO implement me
	panic("implement me")
}

func (p PaymentsAPIStub) PaymentControllerCreateGuestPaymentIntentExecute(_ ApiPaymentControllerCreateGuestPaymentIntentRequest) (*CreateGuestPaymentResponse, *http.Response, error) {
	// TODO implement me
	panic("implement me")
}

func (p PaymentsAPIStub) PaymentControllerCreateRegisteredUserPaymentIntent(_ context.Context, _ string) ApiPaymentControllerCreateRegisteredUserPaymentIntentRequest {
	// TODO implement me
	panic("implement me")
}

func (p PaymentsAPIStub) PaymentControllerCreateRegisteredUserPaymentIntentExecute(_ ApiPaymentControllerCreateRegisteredUserPaymentIntentRequest) (*CreateRegisteredUserPaymentResponse, *http.Response, error) {
	// TODO implement me
	panic("implement me")
}

func (p PaymentsAPIStub) PaymentControllerSetupIntent(_ context.Context, _ string) ApiPaymentControllerSetupIntentRequest {
	// TODO implement me
	panic("implement me")
}

func (p PaymentsAPIStub) PaymentControllerSetupIntentExecute(_ ApiPaymentControllerSetupIntentRequest) (*CreateIntentResponse, *http.Response, error) {
	// TODO implement me
	panic("implement me")
}
