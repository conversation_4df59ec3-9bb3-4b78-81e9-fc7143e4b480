package energymetricsclient

import (
	"bytes"
	"context"
	"errors"
	"io"
	"net/http"
	"time"

	"github.com/aws/aws-xray-sdk-go/v2/xray"
	"k8s.io/utils/ptr"
)

var userAgent = "charge-data-energy-metrics-client"

type ChargeMetricsStub struct{}

func NewChargeMetricsClient(isProd bool, energyMetricsAPIHost string) (ChargeMetricsAPI, error) {
	if energyMetricsAPIHost == "" {
		return nil, errors.New("NewChargeMetricsClient: host is required")
	}
	if isProd {
		cfg := NewConfiguration()
		cfg.UserAgent = userAgent
		cfg.HTTPClient = xray.Client(nil)
		cfg.Servers = ServerConfigurations{
			{
				URL: energyMetricsAPIHost,
			},
		}
		client := NewAPIClient(cfg)
		return client.ChargeMetricsAPI, nil
	}
	return &ChargeMetricsStub{}, nil
}

func (c ChargeMetricsStub) GetChargeEnergyMetrics(ctx context.Context, ppid, evseID, chargeID string) ApiGetChargeEnergyMetricsRequest {
	return ApiGetChargeEnergyMetricsRequest{
		ctx:        ctx,
		ApiService: &c,
		ppid:       ppid,
		evseId:     evseID,
		chargeId:   chargeID,
	}
}

//nolint:gocritic // must conform to generated interface
func (c ChargeMetricsStub) GetChargeEnergyMetricsExecute(_ ApiGetChargeEnergyMetricsRequest) (*ChargeEnergyMetricResponse, *http.Response, error) {
	stubResponse := http.Response{
		StatusCode: http.StatusOK,
		Body:       io.NopCloser(bytes.NewBufferString(`{"metrics": [{"timestamp": "2025-07-22T10:29:37.574Z", "value": 2.4}]}`)),
	}
	mockResponse := ChargeEnergyMetricResponse{
		Params: Params{
			From: time.Time{},
			To:   time.Time{},
		},
		Metrics: []Metric{
			{
				Timestamp: time.Time{},
				Value: NullableFloat32{
					value: ptr.To(float32(4.2)),
					isSet: true,
				},
			},
		},
		Total: 0,
		Unit:  "",
	}
	return &mockResponse, &stubResponse, nil
}

func (c ChargeMetricsStub) GetBaselineForChargingStationCharges(_ context.Context) ApiGetBaselineForChargingStationChargesRequest {
	// TODO implement me
	panic("implement me")
}

func (c ChargeMetricsStub) GetBaselineForChargingStationChargesExecute(_ ApiGetBaselineForChargingStationChargesRequest) (*BaselineResponse, *http.Response, error) {
	// TODO implement me
	panic("implement me")
}

func (c ChargeMetricsStub) GetChargesEnergyMetrics(_ context.Context) ApiGetChargesEnergyMetricsRequest {
	// TODO implement me
	panic("implement me")
}

//nolint:gocritic // must conform to generated interface
func (c ChargeMetricsStub) GetChargesEnergyMetricsExecute(_ ApiGetChargesEnergyMetricsRequest) (*map[string][]GetChargesEnergyMetrics200ResponseValueInner, *http.Response, error) {
	// TODO implement me
	panic("implement me")
}

func (c ChargeMetricsStub) GetTotalEnergyCurrentSession(_ context.Context, _ string) ApiGetTotalEnergyCurrentSessionRequest {
	// TODO implement me
	panic("implement me")
}

func (c ChargeMetricsStub) GetTotalEnergyCurrentSessionExecute(_ ApiGetTotalEnergyCurrentSessionRequest) (*ChargeCurrentEnergySessionResponse, *http.Response, error) {
	// TODO implement me
	panic("implement me")
}
