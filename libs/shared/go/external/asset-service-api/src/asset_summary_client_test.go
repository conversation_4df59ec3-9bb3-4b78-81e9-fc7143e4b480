package assetserviceapi

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestNewAssetSummaryAPIClient(t *testing.T) {
	tests := []struct {
		name    string
		host    string
		want    *APIClient
		wantErr bool
	}{
		{
			name:    "success",
			host:    "http://localhost:8080",
			wantErr: false,
		},
		{
			name:    "host is required",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewAssetSummaryAPIClient(true, tt.host)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewAssetSummaryAPIClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestCanCallAssetSummaryAPI(t *testing.T) {
	testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
		require.Equal(t, "/charging-stations/PP-10002", req.RequestURI)

		byteBody, _ := io.ReadAll(req.Body)
		requestBody := string(byteBody)
		require.NotNil(t, requestBody)

		res.Header().Set("Content-Type", "application/json")
		res.WriteHeader(http.StatusOK)

		_, _ = res.Write([]byte(`{
  "ppid": "PP-10002",
  "rfid": {
    "serialNumber": "PCB12345678",
    "macAddress": "00:11:22:33:44:55"
  },
  "evses": [],
  "ownership": "CUSTOMER",
  "model": {
    "sku": "SOLO3-UK-7KW",
    "range": {
      "name": "Solo 3S"
    },
    "vendor": {
      "name": "Pod Point"
    },
    "regions": [{
      "name": "United Kingdom"
    }]
  },
  "router": {
    "serialNumber": "ROUTER123456",
    "macAddress": "11:22:33:44:55:66",
    "simNumber": "SIM1234567890",
    "model": "TP-Link MR600"
  },
  "location": {
    "id": "LOC-001"
  },
  "tags": [],
  "provisioningInfo": {
    "provisionedAt": "2025-06-16T11:06:42Z"
  }}`))
	}))
	defer testServer.Close()

	url := testServer.URL
	client, err := NewAssetSummaryAPIClient(true, url)
	require.NoError(t, err)

	request := client.GetChargingStation(t.Context(), "PP-10002")
	result, httpResponse, err := request.Execute() //nolint:bodyclose // Test code
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, httpResponse.StatusCode)

	require.NotNil(t, result)
	require.Equal(t, "PP-10002", result.Ppid)
	require.Equal(t, "CUSTOMER", string(*result.Ownership.Get()))
	require.Equal(t, "Solo 3S", result.Model.Range.Name)
}

func TestCanCallStubAPI(t *testing.T) {
	client, err := NewAssetSummaryAPIClient(false, "http://localhost:8080")
	require.NoError(t, err)

	request := client.GetChargingStation(context.Background(), "PP-10001")
	result, httpResponse, err := request.Execute() //nolint:bodyclose // Test code
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, httpResponse.StatusCode)

	require.NotNil(t, result)
	require.Equal(t, "PP-10001", result.Ppid)
	require.Equal(t, "CUSTOMER", string(*result.Ownership.Get()))
	require.Equal(t, "Solo 3", result.Model.Range.Name)
}
