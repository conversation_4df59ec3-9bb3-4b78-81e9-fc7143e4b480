'use client';

import Input from '../input/input';
import React, { useCallback, useMemo, useState } from 'react';
import classNames from 'classnames';

export interface CheckboxProps extends React.HTMLProps<HTMLInputElement> {
  checked?: boolean;
  className?: string;
  disabled?: boolean;
  errorMessage?: string;
  hint?: string;
  id: string;
  isFocused?: boolean;
  label: string;
  name?: string;
  srOnlyLabel?: boolean;
  value?: string;
}

export const Checkbox = ({
  checked,
  className,
  disabled,
  errorMessage,
  hint,
  id,
  isFocused,
  label,
  name,
  srOnlyLabel,
  value,
  ...props
}: CheckboxProps) => (
  <Input
    {...props}
    checked={checked}
    className={classNames('w-6 h-6 checked:bg-neutral', className)}
    disabled={disabled}
    errorMessage={errorMessage}
    hint={hint}
    icon={undefined}
    id={id}
    isFocused={isFocused}
    label={label}
    name={name}
    placeholder={undefined}
    srOnly<PERSON>abel={srOnlyLabel}
    type="checkbox"
    value={value}
    width={undefined}
  />
);

export default Checkbox;

export interface MultiCheckboxHookStore {
  get: () => number[];
  set: (selection: number[]) => void;
}

interface MultiCheckboxHookReturn {
  areAllChecked: () => boolean;
  areAllUnchecked: () => boolean;
  check: (option: number) => void;
  checkAll: () => void;
  checkMultiple: (options: number[]) => void;
  isAnyChecked: () => boolean;
  isAnyUnchecked: () => boolean;
  isChecked: (option: number) => boolean;
  reset: () => void;
  selection: number[];
  toggle: (option: number) => void;
  toggleAll: () => void;
  uncheck: (option: number) => void;
  uncheckAll: () => void;
  uncheckMultiple: (options: number[]) => void;
}

/**
 * Hook to support a multi selection of checkboxes.
 *
 * @param options All the possible identifiers for the multi selection.
 * @param state Either a list of pre-selected identifiers or an object defining how to retrieve and persist the state of the multi selection.
 */
export const useMultiCheckbox = (
  options: number[],
  state?: number[] | MultiCheckboxHookStore
): MultiCheckboxHookReturn => {
  const initialState: number[] = useMemo(
    () =>
      state && !Array.isArray(state)
        ? (state as MultiCheckboxHookStore).get()
        : ((state ?? []) as number[]),
    [state]
  );

  const [selection, setSelection] = useState<number[]>(initialState);

  /**
   * Generic accessor to resolve the state
   * from local state or provided state.
   */
  const getSelected = useCallback((): number[] => {
    if (state && !Array.isArray(state)) {
      return (state as MultiCheckboxHookStore).get();
    }

    return selection;
  }, [selection, state]);

  /**
   * Generic mutator to persist the state
   * in both local state and provided state if given.
   */
  const setSelected = useCallback(
    (options: number[]): void => {
      if (state && !Array.isArray(state)) {
        state.set(options);
      }

      setSelection(options);
    },
    [state]
  );

  const check = useCallback(
    (option: number) => setSelected([...new Set([...getSelected(), option])]),
    [getSelected, setSelected]
  );

  const uncheck = useCallback(
    (option: number) =>
      setSelected(
        getSelected().filter((selectedOption) => selectedOption !== option)
      ),
    [getSelected, setSelected]
  );

  const checkAll = useCallback(
    (): void => setSelected(options),
    [options, setSelected]
  );

  const uncheckAll = useCallback((): void => setSelected([]), [setSelected]);

  const checkMultiple = useCallback(
    (options: number[]): void => {
      setSelected(options);
    },
    [setSelected]
  );

  const uncheckMultiple = useCallback(
    (options: number[]): void => {
      setSelected(selection.filter((item) => !options.includes(item)));
    },
    [selection, setSelected]
  );

  const areAllUnchecked = useCallback(
    (): boolean => getSelected().length === 0,
    [getSelected]
  );

  const isAnyChecked = useCallback(
    (): boolean => getSelected().length > 0,
    [getSelected]
  );

  const isChecked = useCallback(
    (option: number): boolean => getSelected().includes(option),
    [getSelected]
  );

  const areAllChecked = useCallback((): boolean => {
    if (options.length > 0) {
      return getSelected().length === options.length;
    }

    return false;
  }, [getSelected, options.length]);

  const isAnyUnchecked = useCallback((): boolean => {
    if (options.length > 0) {
      return getSelected().length !== options.length;
    }

    return false;
  }, [getSelected, options.length]);

  const toggle = useCallback(
    (option: number): void =>
      isChecked(option) ? uncheck(option) : check(option),
    [check, isChecked, uncheck]
  );

  const toggleAll = useCallback(
    (): void => (areAllChecked() ? uncheckAll() : checkAll()),
    [areAllChecked, checkAll, uncheckAll]
  );

  const reset = useCallback(
    (): void => setSelected(initialState),
    [initialState, setSelected]
  );

  return {
    areAllChecked,
    areAllUnchecked,
    check,
    checkAll,
    checkMultiple,
    isAnyChecked,
    isAnyUnchecked,
    isChecked,
    reset,
    selection,
    toggle,
    toggleAll,
    uncheck,
    uncheckAll,
    uncheckMultiple,
  };
};
