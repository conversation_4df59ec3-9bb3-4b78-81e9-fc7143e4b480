// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`AddTariffScheduleMenu should match snapshot 1`] = `
<body>
  <div>
    <div
      class="relative inline-block text-left"
      data-headlessui-state=""
    >
      <button
        aria-expanded="false"
        aria-haspopup="menu"
        class="bg-black border-black border-2 border-solid rounded-sm px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed tour-tariff-add-schedule"
        data-headlessui-state=""
        id="headlessui-menu-button-:test-id-2"
        type="button"
      >
        <span>
          Add schedule
        </span>
        <svg
          class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
            />
          </g>
        </svg>
      </button>
    </div>
  </div>
</body>
`;

exports[`AddTariffScheduleMenu should match snapshot when opened 1`] = `
<body>
  <div>
    <div
      class="relative inline-block text-left"
      data-headlessui-state="open"
      data-open=""
    >
      <button
        aria-controls="headlessui-menu-items-:test-id-3"
        aria-expanded="true"
        aria-haspopup="menu"
        class="bg-black border-black border-2 border-solid rounded-sm px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed tour-tariff-add-schedule"
        data-active=""
        data-headlessui-state="open active"
        data-open=""
        id="headlessui-menu-button-:test-id-2"
        type="button"
      >
        <span>
          Add schedule
        </span>
        <svg
          class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
            />
          </g>
        </svg>
      </button>
    </div>
  </div>
  <div
    id="headlessui-portal-root"
  >
    <div
      data-headlessui-portal=""
    >
      <div
        aria-labelledby="headlessui-menu-button-:test-id-2"
        class="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-xl bg-white shadow-lg ring-1 ring-black/5 focus:outline-hidden px-2 py-1.5"
        data-anchor="bottom end"
        data-headlessui-state="open"
        data-open=""
        id="headlessui-menu-items-:test-id-3"
        role="menu"
        style="position: absolute; left: 0px; top: 0px; --button-width: 0px;"
        tabindex="0"
      >
        <button
          class="block px-4 py-2 w-full text-left cursor-pointer disabled:cursor-not-allowed disabled:bg-neutral/20 disabled:text-neutral data-[focus]:rounded-sm data-[focus]:text-neutral data-[focus]:bg-neutral/10"
          data-headlessui-state=""
          id="headlessui-menu-item-:test-id-6"
          role="menuitem"
          tabindex="-1"
          type="button"
        >
          Flat rate
        </button>
        <button
          class="block px-4 py-2 w-full text-left cursor-pointer disabled:cursor-not-allowed disabled:bg-neutral/20 disabled:text-neutral data-[focus]:rounded-sm data-[focus]:text-neutral data-[focus]:bg-neutral/10"
          data-headlessui-state=""
          id="headlessui-menu-item-:test-id-7"
          role="menuitem"
          tabindex="-1"
          type="button"
        >
          Day/night
        </button>
        <button
          class="block px-4 py-2 w-full text-left cursor-pointer disabled:cursor-not-allowed disabled:bg-neutral/20 disabled:text-neutral data-[focus]:rounded-sm data-[focus]:text-neutral data-[focus]:bg-neutral/10"
          data-headlessui-state=""
          id="headlessui-menu-item-:test-id-8"
          role="menuitem"
          tabindex="-1"
          type="button"
        >
          Week/weekend
        </button>
        <button
          class="block px-4 py-2 w-full text-left cursor-pointer disabled:cursor-not-allowed disabled:bg-neutral/20 disabled:text-neutral data-[focus]:rounded-sm data-[focus]:text-neutral data-[focus]:bg-neutral/10"
          data-headlessui-state=""
          id="headlessui-menu-item-:test-id-9"
          role="menuitem"
          tabindex="-1"
          type="button"
        >
          Custom
        </button>
      </div>
    </div>
  </div>
</body>
`;
