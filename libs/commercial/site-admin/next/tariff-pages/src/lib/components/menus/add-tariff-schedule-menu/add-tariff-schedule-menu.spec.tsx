import { GroupProvider } from '@experience/commercial/site-admin/next/shared';
import { fireEvent, render, screen } from '@testing-library/react';
import AddTariffScheduleMenu from './add-tariff-schedule-menu';
import useSWR, { SWRResponse } from 'swr';
import userEvent from '@testing-library/user-event';

const mockSetOpenCustomAddTariffSchedule = jest.fn();
const mockSetOpenFlatRateAddTariffSchedule = jest.fn();
const mockSetOpenWorkWeekTariffSchedule = jest.fn();
const mockSetOpenDayNightTariffSchedule = jest.fn();

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({ status: 'authenticated' })),
}));

jest.mock('swr');
const mockUseSwr = jest.mocked(useSWR);

describe('AddTariffScheduleMenu', () => {
  it('should render correctly', () => {
    const { baseElement } = render(
      <AddTariffScheduleMenu
        currency="GBP"
        setOpenCustomAddTariffSchedule={mockSetOpenCustomAddTariffSchedule}
        setOpenFlatRateAddTariffSchedule={mockSetOpenFlatRateAddTariffSchedule}
        setOpenWorkWeekTariffSchedule={mockSetOpenWorkWeekTariffSchedule}
        setOpenDayNightTariffSchedule={mockSetOpenDayNightTariffSchedule}
      />
    );

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <AddTariffScheduleMenu
        currency="GBP"
        setOpenCustomAddTariffSchedule={mockSetOpenCustomAddTariffSchedule}
        setOpenFlatRateAddTariffSchedule={mockSetOpenFlatRateAddTariffSchedule}
        setOpenWorkWeekTariffSchedule={mockSetOpenWorkWeekTariffSchedule}
        setOpenDayNightTariffSchedule={mockSetOpenDayNightTariffSchedule}
      />
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should match snapshot when opened', () => {
    const { baseElement } = render(
      <AddTariffScheduleMenu
        currency="GBP"
        setOpenCustomAddTariffSchedule={mockSetOpenCustomAddTariffSchedule}
        setOpenFlatRateAddTariffSchedule={mockSetOpenFlatRateAddTariffSchedule}
        setOpenWorkWeekTariffSchedule={mockSetOpenWorkWeekTariffSchedule}
        setOpenDayNightTariffSchedule={mockSetOpenDayNightTariffSchedule}
      />
    );

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));
    expect(baseElement).toMatchSnapshot();
  });

  it('should set open custom tariff schedule to true when custom option is clicked', async () => {
    render(
      <AddTariffScheduleMenu
        currency="GBP"
        setOpenCustomAddTariffSchedule={mockSetOpenCustomAddTariffSchedule}
        setOpenFlatRateAddTariffSchedule={mockSetOpenFlatRateAddTariffSchedule}
        setOpenWorkWeekTariffSchedule={mockSetOpenWorkWeekTariffSchedule}
        setOpenDayNightTariffSchedule={mockSetOpenDayNightTariffSchedule}
      />
    );

    await userEvent.click(screen.getByRole('button', { name: 'Add schedule' }));
    await userEvent.click(screen.getByRole('menuitem', { name: 'Custom' }));

    expect(mockSetOpenCustomAddTariffSchedule).toHaveBeenCalledWith(true);
    expect(mockSetOpenFlatRateAddTariffSchedule).not.toHaveBeenCalled();
    expect(mockSetOpenWorkWeekTariffSchedule).not.toHaveBeenCalled();
  });

  it('should set open flat rate tariff schedule to true when flat rate option is clicked', async () => {
    render(
      <AddTariffScheduleMenu
        currency="GBP"
        setOpenCustomAddTariffSchedule={mockSetOpenCustomAddTariffSchedule}
        setOpenFlatRateAddTariffSchedule={mockSetOpenFlatRateAddTariffSchedule}
        setOpenWorkWeekTariffSchedule={mockSetOpenWorkWeekTariffSchedule}
        setOpenDayNightTariffSchedule={mockSetOpenDayNightTariffSchedule}
      />
    );

    await userEvent.click(screen.getByRole('button', { name: 'Add schedule' }));
    await userEvent.click(screen.getByRole('menuitem', { name: 'Flat rate' }));

    expect(mockSetOpenCustomAddTariffSchedule).not.toHaveBeenCalled();
    expect(mockSetOpenFlatRateAddTariffSchedule).toHaveBeenCalledWith(true);
    expect(mockSetOpenWorkWeekTariffSchedule).not.toHaveBeenCalled();
  });

  it('should set open workweek tariff schedule to true when week/weekend option is clicked', async () => {
    render(
      <AddTariffScheduleMenu
        currency="GBP"
        setOpenCustomAddTariffSchedule={mockSetOpenCustomAddTariffSchedule}
        setOpenFlatRateAddTariffSchedule={mockSetOpenFlatRateAddTariffSchedule}
        setOpenWorkWeekTariffSchedule={mockSetOpenWorkWeekTariffSchedule}
        setOpenDayNightTariffSchedule={mockSetOpenDayNightTariffSchedule}
      />
    );

    await userEvent.click(screen.getByRole('button', { name: 'Add schedule' }));
    await userEvent.click(
      screen.getByRole('menuitem', { name: 'Week/weekend' })
    );

    expect(mockSetOpenCustomAddTariffSchedule).not.toHaveBeenCalled();
    expect(mockSetOpenFlatRateAddTariffSchedule).not.toHaveBeenCalled();
    expect(mockSetOpenWorkWeekTariffSchedule).toHaveBeenCalledWith(true);
  });

  it('should be disabled and show tooltip on hover if assigned pod supports OCPP', async () => {
    render(
      <AddTariffScheduleMenu
        currency="GBP"
        hasChargerAssignedWhichDoesNotSupportTariffs={true}
        setOpenCustomAddTariffSchedule={mockSetOpenCustomAddTariffSchedule}
        setOpenFlatRateAddTariffSchedule={mockSetOpenFlatRateAddTariffSchedule}
        setOpenWorkWeekTariffSchedule={mockSetOpenWorkWeekTariffSchedule}
        setOpenDayNightTariffSchedule={mockSetOpenDayNightTariffSchedule}
      />
    );
    expect(screen.getByRole('button', { name: 'Add schedule' })).toBeDisabled();

    await userEvent.hover(screen.getAllByRole('tooltip')[0]);
    expect(
      await screen.findByText(
        'It is not possible to edit the schedules on this tariff while it is assigned to a rapid charger'
      )
    ).toBeInTheDocument();
  });

  it('should be disabled and show tooltip on hover if currency is not GBP', async () => {
    render(
      <AddTariffScheduleMenu
        currency="NOK"
        hasChargerAssignedWhichDoesNotSupportTariffs={false}
        setOpenCustomAddTariffSchedule={mockSetOpenCustomAddTariffSchedule}
        setOpenFlatRateAddTariffSchedule={mockSetOpenFlatRateAddTariffSchedule}
        setOpenWorkWeekTariffSchedule={mockSetOpenWorkWeekTariffSchedule}
        setOpenDayNightTariffSchedule={mockSetOpenDayNightTariffSchedule}
      />
    );
    expect(screen.getByRole('button', { name: 'Add schedule' })).toBeDisabled();

    await userEvent.hover(screen.getAllByRole('tooltip')[0]);
    expect(
      await screen.findByText(
        'It is not possible to edit the schedules on this tariff as the currency is not GBP'
      )
    ).toBeInTheDocument();
  });

  it('should be disabled if the group is in read-only mode', async () => {
    mockUseSwr.mockReturnValueOnce({
      data: { readOnly: true },
    } as SWRResponse);

    render(
      <GroupProvider>
        <AddTariffScheduleMenu
          currency="GBP"
          setOpenCustomAddTariffSchedule={mockSetOpenCustomAddTariffSchedule}
          setOpenFlatRateAddTariffSchedule={
            mockSetOpenFlatRateAddTariffSchedule
          }
          setOpenWorkWeekTariffSchedule={mockSetOpenWorkWeekTariffSchedule}
          setOpenDayNightTariffSchedule={mockSetOpenDayNightTariffSchedule}
        />
      </GroupProvider>
    );

    await userEvent.click(screen.getByRole('button', { name: 'Add schedule' }));
    expect(screen.getByRole('menuitem', { name: 'Flat rate' })).toBeDisabled();
    expect(screen.getByRole('menuitem', { name: 'Day/night' })).toBeDisabled();
    expect(
      screen.getByRole('menuitem', { name: 'Week/weekend' })
    ).toBeDisabled();
    expect(screen.getByRole('menuitem', { name: 'Custom' })).toBeDisabled();
  });
});
