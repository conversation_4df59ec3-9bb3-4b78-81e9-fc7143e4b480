generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
  output          = "../../../../../../../node_modules/@prisma/clients/statements"
  binaryTargets   = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  schemas  = ["statements"]
  provider = "postgresql"
  url      = env("COMMERCIAL_DB_URL")
}

enum Status {
  NEW
  READY
  GENERATING
  GENERATED
  SENDING
  SENT
  CANCELLED

  @@schema("statements")
}

enum PayoutStatus {
  DEFERRED
  PAID_OUT
  PENDING
  TRANSFERRED
  WARNING

  @@schema("statements")
}

enum DocumentType {
  CONTRACT
  CONTRACT_SUMMARY
  FRAMEWORK_CONTRACT
  FRAMEWORK_ORDER

  @@schema("statements")
}

enum Socket {
  A
  B

  @@schema("statements")
}

model User {
  id        String     @id @default(dbgenerated("gen_random_uuid()"))
  email     String
  name      String
  workItems WorkItem[]

  @@schema("statements")
}

model GroupConfig {
  id                       String                @id @default(dbgenerated("gen_random_uuid()"))
  groupId                  String                @unique
  groupName                String
  accountRef               String
  businessName             String?
  businessEmail            String?
  addressLine1             String?
  addressLine2             String?
  town                     String?
  county                   String?
  postcode                 String?
  poNumber                 String?
  stripeCustomerId         String?
  stripeConnectedAccountId String?
  stripeSubscriptionId     String?
  stripeSubscriptionStatus String?
  transfersEnabled         Boolean               @default(false)
  vatRegistered            Boolean               @default(true)
  WorkItem                 WorkItem[]
  Statement                Statement[]
  GroupDocument            GroupDocument[]
  SubscriptionCharger      SubscriptionCharger[]

  @@schema("statements")
}

model SiteConfig {
  id            String          @id @default(dbgenerated("gen_random_uuid()"))
  groupId       String
  siteId        String          @unique
  siteName      String
  automated     Boolean         @default(false)
  emails        String
  ChargerConfig ChargerConfig[]

  @@schema("statements")
}

model ChargerConfig {
  id         String     @id @default(dbgenerated("gen_random_uuid()"))
  siteConfig SiteConfig @relation(fields: [siteId], references: [siteId], onDelete: Cascade)
  siteId     String
  ppid       String     @unique
  fee        Decimal    @db.Decimal(12, 3)
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  deletedAt  DateTime?

  @@schema("statements")
}

model WorkItem {
  id          String      @id @default(dbgenerated("gen_random_uuid()"))
  groupId     String
  groupConfig GroupConfig @relation(fields: [groupId], references: [groupId])
  groupName   String
  siteId      String
  siteName    String
  month       DateTime
  status      Status      @default(NEW)
  user        User?       @relation(fields: [userId], references: [id])
  userId      String?
  statement   Statement?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  deletedAt   DateTime?

  @@unique([siteId, month])
  @@schema("statements")
}

model Statement {
  id                     String                   @id @default(dbgenerated("gen_random_uuid()"))
  reference              String
  workItem               WorkItem                 @relation(fields: [workItemId], references: [id])
  workItemId             String                   @unique
  groupId                String
  groupConfig            GroupConfig              @relation(fields: [groupId], references: [groupId])
  emails                 String
  siteAddress            String
  numberOfCharges        Int
  energyDelivered        Decimal                  @db.Decimal(12, 2)
  claimedEnergyDelivered Decimal                  @db.Decimal(12, 2)
  paidEnergyDelivered    Decimal                  @db.Decimal(12, 2)
  feesNet                Decimal                  @db.Decimal(12, 2)
  feesGross              Decimal                  @db.Decimal(12, 2)
  feesVat                Decimal                  @db.Decimal(12, 2)
  revenueNet             Decimal                  @db.Decimal(12, 2)
  revenueGross           Decimal                  @db.Decimal(12, 2)
  revenueVat             Decimal                  @db.Decimal(12, 2)
  stripeTransferId       String?
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  deletedAt              DateTime?
  statementChargerConfig StatementChargerConfig[]
  invoice                Invoice?
  automaticPayout        Boolean                  @default(false)
  payoutStatus           PayoutStatus?

  @@schema("statements")
}

model StatementChargerConfig {
  id          String    @id @default(dbgenerated("gen_random_uuid()"))
  statement   Statement @relation(fields: [statementId], references: [id], onDelete: Cascade)
  statementId String
  ppid        String
  fee         Decimal   @db.Decimal(12, 2)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  @@schema("statements")
}

model Invoice {
  id                  String    @id @default(dbgenerated("gen_random_uuid()"))
  statement           Statement @relation(fields: [statementId], references: [id], onDelete: Cascade)
  statementId         String    @unique
  stripeInvoiceId     String?   @unique
  stripeInvoiceStatus String?
  stripeInvoiceNumber String?   @unique
  hostedInvoiceUrl    String?
  invoiceNumber       Int       @default(autoincrement())
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt
  deletedAt           DateTime?

  @@schema("statements")
}

model GroupDocument {
  id          String       @id @default(dbgenerated("gen_random_uuid()"))
  groupId     String
  groupConfig GroupConfig  @relation(fields: [groupId], references: [groupId])
  name        String
  type        DocumentType
  uploadDate  DateTime     @default(now())
  startDate   DateTime
  s3Location  String

  @@schema("statements")
}

model SubscriptionCharger {
  id          String      @id @default(dbgenerated("gen_random_uuid()"))
  groupId     String
  groupConfig GroupConfig @relation(fields: [groupId], references: [groupId])
  ppid        String
  socket      Socket
  createdAt   DateTime    @default(now())
  deletedAt   DateTime?

  @@schema("statements")
}
