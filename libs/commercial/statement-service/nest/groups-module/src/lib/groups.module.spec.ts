import { Document } from '@experience/commercial/statement-service/shared';
import {
  Statement,
  TEST_CREATE_GROUP_REQUEST,
  TEST_UPDATE_GROUP_REQUEST,
} from '@experience/commercial/statement-service/shared';
import { TEST_OIDC_USER_WITH_GROUP_DOCUMENTS_ROLE_DATA } from '@experience/shared/typescript/oidc-utils/specs';
import axios, { AxiosResponse } from 'axios';

const dateMatcher = /^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$/;
const isoDateMatcher =
  /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})$/;
const uuidMatcher =
  /^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i;

const workItemDateMatcher = (response: AxiosResponse<Statement[]>) =>
  response.data.map(() => ({
    workItem: {
      month: expect.stringMatching(dateMatcher),
    },
  }));

const documentMatcher = (response: AxiosResponse<Statement[]>) =>
  response.data.map(() => ({
    id: expect.stringMatching(uuidMatcher),
    uploadDate: expect.stringMatching(isoDateMatcher),
  }));

const oidcHeadersDocuments = {
  headers: {
    'x-amzn-oidc-data': TEST_OIDC_USER_WITH_GROUP_DOCUMENTS_ROLE_DATA,
  },
};

export const describeGroupsModule = (baseUrl: string): void => {
  describe('groups module', () => {
    describe('groups controller', () => {
      it('should find a list of groups', async () => {
        const response = await axios.get(`${baseUrl}/groups`);
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });

      it('should find a list of site admin groups', async () => {
        const response = await axios.get(`${baseUrl}/groups/site-admin`);
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });

      it('should find a group by group id', async () => {
        const response = await axios.get(
          `${baseUrl}/groups/45c9ef22-b44b-4edd-bb66-392e923950af`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });

      it('should find a group by group id including sites', async () => {
        const response = await axios.get(
          `${baseUrl}/groups/45c9ef22-b44b-4edd-bb66-392e923950af?includeSites=true`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot();
      });

      it('should update a group by group id', async () => {
        const putResponse = await axios.put(
          `${baseUrl}/groups/45c9ef22-b44b-4edd-bb66-392e923950af`,
          TEST_UPDATE_GROUP_REQUEST
        );
        expect(putResponse.status).toEqual(200);

        const getResponse = await axios.get(
          `${baseUrl}/groups/45c9ef22-b44b-4edd-bb66-392e923950af?includeSites=true`
        );
        expect(getResponse.data).toMatchSnapshot();
      });

      it('should create a group', async () => {
        const postResponse = await axios.post(
          `${baseUrl}/groups`,
          TEST_CREATE_GROUP_REQUEST
        );
        expect(postResponse.status).toEqual(201);

        const getResponse = await axios.get(
          `${baseUrl}/groups/036c0720-f908-45fc-ae7c-031a47c2e278?includeSites=true`
        );
        expect(getResponse.data).toMatchSnapshot();
      });
    });

    describe('groups statements controller', () => {
      it('should find a list of statements for a group', async () => {
        const response = await axios.get(
          `${baseUrl}/groups/c9c5d39c-23ba-4a21-ac45-75968fdc055f/statements`
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot(workItemDateMatcher(response));
      });
    });

    describe('group documents controller', () => {
      it('should find a list of documents for a group', async () => {
        const response = await axios.get(
          `${baseUrl}/groups/45c9ef22-b44b-4edd-bb66-392e923950af/documents`,
          oidcHeadersDocuments
        );
        expect(response.status).toEqual(200);
        expect(response.data).toMatchSnapshot(documentMatcher(response));
      });

      it('should upload a document for a group', async () => {
        const formData = new FormData();
        formData.append('name', 'Test Contract Document');
        formData.append('type', 'CONTRACT');
        formData.append(
          'startDate',
          new Date('2023-10-01T00:00:00Z').toISOString()
        );
        formData.append(
          'file',
          new Blob(['test content'], { type: 'application/pdf' }),
          'test-document.pdf'
        );

        const response = await axios.post(
          `${baseUrl}/groups/45c9ef22-b44b-4edd-bb66-392e923950af/documents`,
          formData,
          oidcHeadersDocuments
        );
        expect(response.status).toEqual(201);

        const documentsResponse = await axios.get(
          `${baseUrl}/groups/45c9ef22-b44b-4edd-bb66-392e923950af/documents`,
          oidcHeadersDocuments
        );
        expect(documentsResponse.status).toEqual(200);
        expect(documentsResponse.data).toMatchSnapshot(
          documentMatcher(documentsResponse)
        );
      });

      it('should download a document for a group', async () => {
        const documentsResponse: Document[] = await axios
          .get(
            `${baseUrl}/groups/45c9ef22-b44b-4edd-bb66-392e923950af/documents`,
            oidcHeadersDocuments
          )
          .then((response) => response.data);

        const testDocument = documentsResponse.find(
          (doc) => doc.name === 'Test Contract Document.pdf'
        ) as Document;

        const downloadResponse = await axios.get(
          `${baseUrl}/groups/45c9ef22-b44b-4edd-bb66-392e923950af/documents/${testDocument.id}/download`,
          oidcHeadersDocuments
        );

        expect(downloadResponse.status).toEqual(200);
        expect(downloadResponse.headers['content-type']).toEqual(
          'application/pdf'
        );
        expect(downloadResponse.data).toContain('');
      });
    });

    describe('group chargers controller', () => {
      it('should get chargers for a group', async () => {
        const response = await axios.get(
          `${baseUrl}/groups/45c9ef22-b44b-4edd-bb66-392e923950af/chargers`,
          oidcHeadersDocuments
        );

        expect(response.status).toEqual(200);
        expect(response.data[0]).toEqual({
          hasSubscription: expect.any(Boolean),
          name: expect.any(String),
          ppid: expect.any(String),
          siteAddress: expect.any(String),
          socket: expect.any(String),
        });
      });
    });
  });
};
