import { ChargersService } from './chargers.service';
import { ConfigService } from '@nestjs/config';
import {
  HealthIndicatorService,
  PrismaHealthIndicator,
} from '@nestjs/terminus';
import {
  SITE_ADMIN_API_URL,
  TEST_GROUP,
  TEST_SUBSCRIPTION_CHARGER_WITH_ADDITIONAL_INFO,
} from '@experience/commercial/statement-service/shared';
import {
  StatementsPrismaClient,
  TEST_SUBSCRIPTION_CHARGER_ENTITY,
} from '@experience/commercial/statement-service/prisma/statements/client';
import { TEST_POD_WITH_SITE } from '@experience/commercial/site-admin/typescript/domain-model';
import { Test } from '@nestjs/testing';

describe('ChargersService', () => {
  let service: ChargersService;
  let prismaClient: StatementsPrismaClient;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        ChargersService,
        ConfigService,
        HealthIndicatorService,
        PrismaHealthIndicator,
        StatementsPrismaClient,
      ],
    }).compile();

    service = module.get<ChargersService>(ChargersService);
    prismaClient = module.get<StatementsPrismaClient>(StatementsPrismaClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(prismaClient).toBeDefined();
  });

  it('should find chargers by group ID', async () => {
    const mockFetchResponse = jest
      .fn()
      .mockResolvedValueOnce([TEST_POD_WITH_SITE]);
    const mockFetch = jest.spyOn(global, 'fetch');
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: mockFetchResponse,
      status: 200,
    } as unknown as Response);
    const mockFindMany = (prismaClient.subscriptionCharger.findMany = jest
      .fn()
      .mockResolvedValueOnce([
        { ...TEST_SUBSCRIPTION_CHARGER_ENTITY, ppid: 'BAR' },
      ]));

    const result = await service.findChargersByGroupId(TEST_GROUP.groupId);

    expect(mockFetch).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/pods?groupUid=036c0720-f908-45fc-ae7c-031a47c2e278`
    );
    expect(mockFindMany).toHaveBeenCalledWith({
      where: { groupId: TEST_GROUP.groupId, deletedAt: null },
    });
    expect(result).toEqual([TEST_SUBSCRIPTION_CHARGER_WITH_ADDITIONAL_INFO]);
  });

  it('should return empty array when no chargers are found', async () => {
    const mockFetchResponse = jest.fn().mockResolvedValueOnce([]);
    const mockFetch = jest.spyOn(global, 'fetch');
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: mockFetchResponse,
      status: 200,
    } as unknown as Response);
    const mockFindMany = (prismaClient.subscriptionCharger.findMany = jest
      .fn()
      .mockResolvedValueOnce([]));

    const result = await service.findChargersByGroupId(TEST_GROUP.groupId);

    expect(mockFetch).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/pods?groupUid=036c0720-f908-45fc-ae7c-031a47c2e278`
    );
    expect(mockFindMany).toHaveBeenCalledWith({
      where: { groupId: TEST_GROUP.groupId, deletedAt: null },
    });
    expect(result).toEqual([]);
  });
});
