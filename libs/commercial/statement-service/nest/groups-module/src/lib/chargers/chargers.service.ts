import { Pod as Charger } from '@experience/commercial/site-admin/typescript/domain-model';
import { Injectable, Logger } from '@nestjs/common';
import {
  SITE_ADMIN_API_URL,
  Socket,
  SubscriptionChargerWithAdditionalInfo,
} from '@experience/commercial/statement-service/shared';
import {
  StatementsPrismaClient,
  SubscriptionChargerEntity,
} from '@experience/commercial/statement-service/prisma/statements/client';

@Injectable()
export class ChargersService {
  private readonly logger = new Logger(ChargersService.name);

  constructor(private readonly database: StatementsPrismaClient) {}

  async findChargersByGroupId(
    groupId: string
  ): Promise<SubscriptionChargerWithAdditionalInfo[]> {
    this.logger.log('finding chargers by group id');
    const response = await fetch(
      `${SITE_ADMIN_API_URL}/pods?groupUid=${groupId}`
    );

    const chargers = (await response.json()) as Charger[];

    const subscriptionChargers: SubscriptionChargerEntity[] =
      await this.database.subscriptionCharger.findMany({
        where: {
          groupId,
          deletedAt: null,
        },
      });

    return chargers.flatMap((charger) =>
      this.mapGroupLevelCharger(charger, subscriptionChargers)
    );
  }

  private mapGroupLevelCharger = (
    charger: Charger,
    subscriptionChargers: SubscriptionChargerEntity[]
  ): SubscriptionChargerWithAdditionalInfo[] =>
    charger.sockets.map((socket) => {
      const hasSubscription = subscriptionChargers.some(
        (subscriptionCharger) =>
          subscriptionCharger.ppid === charger.ppid &&
          socket.door === subscriptionCharger.socket
      );

      return {
        name: charger?.name,
        ppid: charger.ppid,
        siteAddress: charger.site?.address.name,
        socket: socket.door as Socket,
        hasSubscription: hasSubscription,
      };
    });
}
