import { ChargersController } from './chargers.controller';
import { ChargersService } from './chargers.service';
import { INestApplication } from '@nestjs/common';
import {
  TEST_GROUP,
  TEST_SUBSCRIPTION_CHARGER_WITH_ADDITIONAL_INFO,
} from '@experience/commercial/statement-service/shared';
import { Test } from '@nestjs/testing';
import request from 'supertest';

jest.mock('./chargers.service');

describe('ChargersController', () => {
  let app: INestApplication;
  let controller: ChargersController;
  let service: ChargersService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      controllers: [ChargersController],
      providers: [ChargersService],
    }).compile();

    controller = module.get(ChargersController);
    service = module.get(ChargersService);

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should be defined', () => {
    expect(app).toBeDefined();
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should find chargers chargers for a group', async () => {
    const mockFindChargersChargersByGroupId = jest
      .spyOn(service, 'findChargersByGroupId')
      .mockResolvedValueOnce([TEST_SUBSCRIPTION_CHARGER_WITH_ADDITIONAL_INFO]);

    const response = await request(app.getHttpServer())
      .get(`/groups/${TEST_GROUP.groupId}/chargers`)
      .expect(200);

    expect(response.body).toEqual([
      TEST_SUBSCRIPTION_CHARGER_WITH_ADDITIONAL_INFO,
    ]);
    expect(mockFindChargersChargersByGroupId).toHaveBeenCalledWith(
      TEST_GROUP.groupId
    );
  });

  it('should return a 400 error when groupId is not a valid UUID', async () => {
    const response = await request(app.getHttpServer())
      .get(`/groups/not-a-uuid/chargers`)
      .expect(400);

    expect(response.body).toEqual({
      statusCode: 400,
      message: 'Validation failed (uuid is expected)',
      error: 'Bad Request',
    });
  });

  it('should return an empty array when no chargers found', async () => {
    jest.spyOn(service, 'findChargersByGroupId').mockResolvedValueOnce([]);

    const response = await request(app.getHttpServer())
      .get(`/groups/${TEST_GROUP.groupId}/chargers`)
      .expect(200);

    expect(response.body).toEqual([]);
  });
});
