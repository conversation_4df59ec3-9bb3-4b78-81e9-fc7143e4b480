import { ChargersService } from './chargers.service';
import { Controller, Get, Param, ParseUUIDPipe } from '@nestjs/common';
import { SubscriptionChargerWithAdditionalInfo } from '@experience/commercial/statement-service/shared';

@Controller('groups/:groupId/chargers')
export class ChargersController {
  constructor(private readonly chargersService: ChargersService) {}

  @Get()
  async findChargersByGroupId(
    @Param('groupId', ParseUUIDPipe) groupId: string
  ): Promise<SubscriptionChargerWithAdditionalInfo[]> {
    return this.chargersService.findChargersByGroupId(groupId);
  }
}
