import { ChargersController } from './chargers/chargers.controller';
import { ChargersService } from './chargers/chargers.service';
import { ConfigModule } from '@nestjs/config';
import { DocumentsController } from './documents/documents.controller';
import { DocumentsService } from './documents/documents.service';
import { GroupsController } from './groups.controller';
import { GroupsService } from './groups.service';
import { GroupsStatementsController } from './statements/statements.controller';
import { GroupsStatementsService } from './statements/statements.service';
import { Module } from '@nestjs/common';
import { S3Module } from '@experience/shared/nest/aws/s3-module';
import { STRIPE_CONFIG, StripeModule } from '@experience/shared/nest/stripe';
import { SitesModule } from '@experience/commercial/statement-service/nest/sites-module';
import { StatementsPrismaClientModule } from '@experience/commercial/statement-service/prisma/statements/client';
import { SubscriptionController } from './subscriptions/subscription.controller';
import { SubscriptionService } from './subscriptions/subscription.service';
import { UpdateGroupAndSitesCommand } from './commands/update-groups-and-sites.command';

@Module({
  imports: [
    ConfigModule,
    StatementsPrismaClientModule,
    S3Module,
    SitesModule,
    StripeModule.register(process.env.STRIPE_API_KEY as string, STRIPE_CONFIG),
  ],
  controllers: [
    GroupsController,
    DocumentsController,
    GroupsStatementsController,
    SubscriptionController,
    ChargersController,
  ],
  providers: [
    GroupsService,
    DocumentsService,
    GroupsStatementsService,
    UpdateGroupAndSitesCommand,
    SubscriptionService,
    ChargersService,
  ],
  exports: [GroupsService],
})
export class GroupsModule {}
