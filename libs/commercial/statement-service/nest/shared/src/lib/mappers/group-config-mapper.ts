import { Group } from '@experience/commercial/statement-service/shared';
import {
  GroupConfigEntity,
  GroupConfigEntityShallow,
} from '@experience/commercial/statement-service/prisma/statements/client';

export const mapGroupConfigToDto = (
  entity: GroupConfigEntityShallow | GroupConfigEntity
): Group => ({
  accountRef: entity.accountRef ?? undefined,
  addressLine1: entity.addressLine1 ?? undefined,
  addressLine2: entity.addressLine2 ?? undefined,
  businessEmail: entity.businessEmail ?? undefined,
  businessName: entity.businessName ?? undefined,
  county: entity.county ?? undefined,
  groupId: entity.groupId,
  groupName: entity.groupName,
  hasContractDocument:
    ('GroupDocument' in entity && entity['GroupDocument'].length > 0) ?? false,
  poNumber: entity.poNumber ?? undefined,
  postcode: entity.postcode ?? undefined,
  stripeConnectedAccountId: entity.stripeConnectedAccountId ?? undefined,
  stripeCustomerId: entity.stripeCustomerId ?? undefined,
  stripeSubscriptionId: entity.stripeSubscriptionId ?? undefined,
  stripeSubscriptionStatus: entity.stripeSubscriptionStatus ?? undefined,
  town: entity.town ?? undefined,
  transfersEnabled: entity.transfersEnabled,
  vatRegistered: entity.vatRegistered,
});
