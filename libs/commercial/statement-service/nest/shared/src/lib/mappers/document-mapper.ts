import {
  Document,
  DocumentType,
} from '@experience/commercial/statement-service/shared';
import { DocumentEntity } from '@experience/commercial/statement-service/prisma/statements/client';

export const mapDocumentEntityToDto = (entity: DocumentEntity): Document => ({
  id: entity.id,
  name: entity.name,
  startDate: entity.startDate.toISOString(),
  type: mapDocumentType(entity.type),
  uploadDate: entity.uploadDate.toISOString(),
  url: entity.s3Location,
});

export const mapDocumentType = (
  entityType: DocumentEntity['type']
): DocumentType => {
  const enumMap: Record<DocumentEntity['type'], DocumentType> = {
    ['CONTRACT']: DocumentType.CONTRACT,
    ['CONTRACT_SUMMARY']: DocumentType.CONTRACT_SUMMARY,
    ['FRAMEWORK_CONTRACT']: DocumentType.FRAMEWORK_CONTRACT,
    ['FRAMEWORK_ORDER']: DocumentType.FRAMEWORK_ORDER,
  };
  return enumMap[entityType];
};
