import {
  TEST_GROUP,
  TEST_GROUP_WITH_STRIPE_SUBSCRIPTION,
} from '@experience/commercial/statement-service/shared';
import {
  TEST_GROUP_CONFIG_ENTITY,
  TEST_GROUP_CONFIG_ENTITY_WITH_ADDRESS,
  TEST_GROUP_CONFIG_ENTITY_WITH_DOCUMENT,
} from '@experience/commercial/statement-service/prisma/statements/client';
import { mapGroupConfigToDto } from './group-config-mapper';
import { setIn } from 'immutable';

describe('Group config mapper', () => {
  it('should map group config entity to group dto', () => {
    expect(mapGroupConfigToDto(TEST_GROUP_CONFIG_ENTITY)).toEqual(TEST_GROUP);
  });

  it('should map a group config entity with stripe ids to a group dto', () => {
    expect(
      mapGroupConfigToDto({
        ...TEST_GROUP_CONFIG_ENTITY_WITH_ADDRESS,
        stripeConnectedAccountId: 'acct_123456789',
        stripeCustomerId: 'cus_123456789',
        stripeSubscriptionId: 'sub_1MowQVLkdIwHu7ixeRlqHVzs',
        stripeSubscriptionStatus: 'active',
        transfersEnabled: true,
        vatRegistered: true,
      })
    ).toEqual(TEST_GROUP_WITH_STRIPE_SUBSCRIPTION);
  });

  it.each([
    [
      'true when group documents exist',
      TEST_GROUP_CONFIG_ENTITY_WITH_DOCUMENT,
      true,
    ],
    [
      'false when there are no group documents',
      setIn(TEST_GROUP_CONFIG_ENTITY_WITH_DOCUMENT, ['GroupDocument'], []),
      false,
    ],
    [
      'false when group documents do not exist on the group',
      TEST_GROUP_CONFIG_ENTITY,
      false,
    ],
  ])('should set contract document flag to %s', (_, entity, flag) => {
    expect(mapGroupConfigToDto(entity).hasContractDocument).toEqual(flag);
  });
});
