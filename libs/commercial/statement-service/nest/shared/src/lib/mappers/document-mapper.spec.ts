import {
  DocumentEntity,
  TEST_DOCUMENT_ENTITY,
} from '@experience/commercial/statement-service/prisma/statements/client';
import {
  DocumentType,
  TEST_DOCUMENT,
} from '@experience/commercial/statement-service/shared';
import { mapDocumentEntityToDto, mapDocumentType } from './document-mapper';

describe('Document Mapper', () => {
  it('should map a document entity to a document DTO', () => {
    expect(mapDocumentEntityToDto(TEST_DOCUMENT_ENTITY)).toEqual(TEST_DOCUMENT);
  });

  it.each([
    ['CONTRACT', DocumentType.CONTRACT],
    ['CONTRACT_SUMMARY', DocumentType.CONTRACT_SUMMARY],
    ['FRAMEWORK_CONTRACT', DocumentType.FRAMEWORK_CONTRACT],
    ['FRAMEWORK_ORDER', DocumentType.FRAMEWORK_ORDER],
  ])('should map document type - %s', (entityType, dtoEnum) => {
    expect(mapDocumentType(entityType as DocumentEntity['type'])).toEqual(
      dtoEnum
    );
  });
});
