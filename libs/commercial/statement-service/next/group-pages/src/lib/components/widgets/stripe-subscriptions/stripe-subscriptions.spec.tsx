import { FlagsProvider } from 'flagged';
import { StripeSubscriptions } from './stripe-subscriptions';
import {
  TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
  TEST_GROUP_WITH_STRIPE_CUSTOMER,
  TEST_GROUP_WITH_STRIPE_SUBSCRIPTION,
} from '@experience/commercial/statement-service/shared';
import { TEST_SUBSCRIPTION } from '@experience/shared/nest/stripe';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

const mockWindowOpen = jest.fn();
const mockRouter = { push: jest.fn() };

jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
}));

describe('StripeSubscriptions', () => {
  it('should render successfully', () => {
    const { baseElement } = render(
      <StripeSubscriptions group={TEST_GROUP_WITH_STRIPE_SUBSCRIPTION} />
    );
    expect(baseElement).toBeTruthy();
  });

  it.each([
    ['with a stripe subscription', TEST_GROUP_WITH_STRIPE_SUBSCRIPTION],
    [
      'with no stripe subscription',
      { ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT },
    ],
  ])('should match snapshot %s', (_, group) => {
    const { baseElement } = render(
      <StripeSubscriptions group={group} subscription={TEST_SUBSCRIPTION} />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should open the stripe product page when the subscription id link is clicked', async () => {
    render(<StripeSubscriptions group={TEST_GROUP_WITH_STRIPE_SUBSCRIPTION} />);
    jest.spyOn(window, 'open').mockImplementation(mockWindowOpen);

    await userEvent.click(
      screen.getByRole('button', {
        name: TEST_GROUP_WITH_STRIPE_SUBSCRIPTION.stripeSubscriptionId,
      })
    );

    expect(mockWindowOpen).toHaveBeenCalledWith(
      `https://dashboard.stripe.com/subscriptions/${TEST_GROUP_WITH_STRIPE_SUBSCRIPTION.stripeSubscriptionId}`,
      '_blank'
    );
  });

  it('should open the stripe subscription modal when the add a subscription button is clicked', async () => {
    render(<StripeSubscriptions group={TEST_GROUP_WITH_STRIPE_CUSTOMER} />);
    await userEvent.click(
      screen.getByRole('button', { name: '2. Add a subscription' })
    );

    expect(screen.getByRole('dialog')).toHaveTextContent(
      'Create Stripe subscription'
    );
  });

  it.each([
    [
      'when there is no stripe customer for the group',
      { ...TEST_GROUP_WITH_STRIPE_CUSTOMER, stripeCustomerId: undefined },
    ],
    [
      'when there are no customer details for the group',
      {
        ...TEST_GROUP_WITH_STRIPE_CUSTOMER,
        accountRef: undefined,
        businessName: undefined,
        businessEmail: undefined,
      },
    ],
  ])('should disable the add stripe subscription button %s', (_, group) => {
    render(<StripeSubscriptions group={group} />);

    expect(
      screen.getByRole('button', { name: '2. Add a subscription' })
    ).toBeDisabled();
  });

  it.each([
    ['with feature flag on', true],
    ['with feature flag off', false],
  ])(
    'should handle edit subscription click correctly %s',
    async (_, featureEnabled) => {
      render(
        <FlagsProvider features={{ chargersOnSubscription: featureEnabled }}>
          <StripeSubscriptions
            group={TEST_GROUP_WITH_STRIPE_SUBSCRIPTION}
            subscription={TEST_SUBSCRIPTION}
          />
        </FlagsProvider>
      );

      await userEvent.click(
        screen.getByRole('button', { name: /Edit subscription/ })
      );

      if (featureEnabled) {
        expect(mockRouter.push).toHaveBeenCalledWith(
          `/groups/${TEST_GROUP_WITH_STRIPE_SUBSCRIPTION.groupId}/chargers`
        );
      } else {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      }
    }
  );
});
