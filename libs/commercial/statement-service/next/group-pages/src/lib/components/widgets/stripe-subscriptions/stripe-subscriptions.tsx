import {
  Button,
  ButtonPadding,
  ButtonTypes,
  Card,
  Heading,
  HeadingSizes,
  Paragraph,
  PencilIcon,
} from '@experience/shared/react/design-system';
import { Group } from '@experience/commercial/statement-service/shared';
import { RetrieveSubscriptionResponse } from '@experience/shared/nest/stripe';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import { convertSnakeCaseToSentenceCase } from '@experience/shared/typescript/utils';
import { useFeature } from 'flagged';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import CreateStripeSubscriptionModal from '../../modals/create-stripe-subscription-modal/create-stripe-subscription-modal';
import EditStripeSubscriptionModal from '../../modals/edit-stripe-subscription-modal/edit-stripe-subscription-modal';

export interface StripeDetailsProps {
  group: Group;
  subscription?: RetrieveSubscriptionResponse;
}

export const StripeSubscriptions = ({
  group,
  subscription,
}: StripeDetailsProps) => {
  const [openEditSubscriptionModal, setOpenEditSubscriptionModal] =
    useState(false);
  const [openStripeSubscription, setOpenStripeSubscription] = useState(false);
  const chargersOnSubscriptionFeatureEnabled = useFeature(
    'chargersOnSubscription'
  );
  const router = useRouter();

  const hasCustomerDetails =
    !!group.accountRef && !!group.businessName && !!group.businessEmail;
  const createStripeSubscriptionDisabled =
    !group.stripeCustomerId ||
    !hasCustomerDetails ||
    !!group.stripeSubscriptionId;

  const openStripeSubscriptionPage = () => {
    window.open(
      `https://dashboard.stripe.com/subscriptions/${group.stripeSubscriptionId}`,
      '_blank'
    );
  };

  return (
    <>
      <Card>
        {group.stripeSubscriptionId ? (
          <>
            <div className="flex justify-between items-center">
              <Card.Header className="pb-1">Subscription ID</Card.Header>
              <Button
                aria-label={`Edit subscription ${group.stripeSubscriptionId}`}
                buttonType={ButtonTypes.LINK}
                id={`edit-energy-cost-${group.stripeSubscriptionId}`}
                name={`edit-energy--cost-${group.stripeSubscriptionId}`}
                onClick={() =>
                  chargersOnSubscriptionFeatureEnabled
                    ? router.push(`/groups/${group.groupId}/chargers`)
                    : setOpenEditSubscriptionModal(true)
                }
                padding={ButtonPadding.BOX}
              >
                <PencilIcon.LIGHT />
              </Button>
            </div>
            <div>
              <Button
                buttonType={ButtonTypes.LINK}
                onClick={openStripeSubscriptionPage}
              >
                {group.stripeSubscriptionId}
              </Button>
            </div>
            <Heading.H3
              fontSize={HeadingSizes.XS}
              className="font-bold pb-2 pt-2"
            >
              Status
            </Heading.H3>
            <Paragraph>
              {convertSnakeCaseToSentenceCase(subscription?.status ?? '-')}
            </Paragraph>
            <EditStripeSubscriptionModal
              group={group}
              open={openEditSubscriptionModal}
              setOpen={setOpenEditSubscriptionModal}
              subscription={subscription}
            />
          </>
        ) : (
          <div className="h-full flex flex-col">
            <Paragraph className="h-full text-center">
              Once the Stripe customer is created, add a subscription for the
              number of sockets
            </Paragraph>
            <VerticalSpacer />
            <Button
              className="mx-auto"
              buttonType={ButtonTypes.PRIMARY}
              id="create-subscription"
              onClick={() => setOpenStripeSubscription(true)}
              disabled={createStripeSubscriptionDisabled}
            >
              2. Add a subscription
            </Button>
          </div>
        )}
      </Card>
      <CreateStripeSubscriptionModal
        group={group}
        open={openStripeSubscription}
        setOpen={setOpenStripeSubscription}
      />
    </>
  );
};
