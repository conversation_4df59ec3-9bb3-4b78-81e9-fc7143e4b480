import {
  ActionItem,
  DownloadIcon,
  InfiniteTable,
} from '@experience/shared/react/design-system';
import { ColumnDef } from '@tanstack/react-table';
import { Document } from '@experience/commercial/statement-service/shared';
import { convertSnakeCaseToSentenceCase } from '@experience/shared/typescript/utils';
import dayjs from 'dayjs';

export interface DocumentsTableProps {
  documents: Document[];
  groupId: string;
}

export const DocumentsTable = ({ documents, groupId }: DocumentsTableProps) => {
  const columns: ColumnDef<Document>[] = [
    {
      accessorKey: 'name',
      header: () => 'Name',
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'type',
      cell: ({ row: { original: document } }) =>
        convertSnakeCaseToSentenceCase(document.type),
      header: () => 'Type',
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'uploadDate',
      header: () => 'Upload Date',
      cell: ({ row: { original: document } }) =>
        dayjs(document.uploadDate).format('DD/MM/YYYY'),
      sortingFn: 'datetime',
    },
    {
      accessorKey: 'startDate',
      header: () => 'Start Date',
      cell: ({ row: { original: document } }) =>
        dayjs(document.startDate).format('DD/MM/YYYY'),
      sortingFn: 'datetime',
    },
    {
      id: 'actions',
      header: () => 'Actions',
      cell: ({ row: { original: document } }) => (
        <ActionItem
          key={`download-${document.id}`}
          aria-label={`Download ${document.name}`}
          onClick={() =>
            window.open(
              `/api/groups/${groupId}/documents/${document.id}/download`,
              '_blank'
            )
          }
          icon={<DownloadIcon.LIGHT />}
          text="Download"
        />
      ),
    },
  ];

  return (
    <InfiniteTable
      id="documents-table"
      caption="Table of documents"
      columns={columns}
      data={documents}
      showSearchField
    />
  );
};
