import { Site } from './site.dto';

export class Group {
  accountRef?: string;
  addressLine1?: string;
  addressLine2?: string;
  businessEmail?: string;
  businessName?: string;
  county?: string;
  groupId: string;
  groupName: string;
  hasContractDocument?: boolean;
  poNumber?: string;
  postcode?: string;
  sites?: Site[];
  stripeConnectedAccountId?: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  stripeSubscriptionStatus?: string;
  town?: string;
  transfersEnabled: boolean;
  vatRegistered: boolean;
}
