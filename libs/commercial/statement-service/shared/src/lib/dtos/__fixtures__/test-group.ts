import { Group } from '../group.dto';
import { TEST_SITE } from './test-site';

export const TEST_GROUP: Group = {
  accountRef: '*********',
  businessEmail: '<EMAIL>',
  businessName: 'Test Group Inc Invoice office',
  groupId: '036c0720-f908-45fc-ae7c-031a47c2e278',
  groupName: 'Test Group Inc.',
  hasContractDocument: false,
  poNumber: 'UK10010001',
  transfersEnabled: false,
  vatRegistered: true,
} as Group;

export const TEST_GROUP_WITH_ADDRESS: Group = {
  accountRef: '*********',
  addressLine1: 'Flat 1',
  addressLine2: '1 Test Street',
  businessEmail: '<EMAIL>',
  businessName: 'Test Group Inc Invoice office',
  county: 'Test County',
  groupId: '036c0720-f908-45fc-ae7c-031a47c2e278',
  groupName: 'Test Group Inc.',
  hasContractDocument: false,
  poNumber: 'UK10010001',
  postcode: 'TE1 1ST',
  town: 'Test Town',
  transfersEnabled: false,
  vatRegistered: true,
};

export const TEST_GROUP_WITH_SITE: Group = {
  ...TEST_GROUP_WITH_ADDRESS,
  sites: [TEST_SITE],
};

export const TEST_GROUP_WITH_STRIPE_CUSTOMER: Group = {
  ...TEST_GROUP_WITH_ADDRESS,
  stripeCustomerId: 'cus_*********',
};

export const TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT: Group = {
  ...TEST_GROUP_WITH_STRIPE_CUSTOMER,
  stripeConnectedAccountId: 'acct_*********',
  transfersEnabled: true,
};

export const TEST_GROUP_WITH_STRIPE_SUBSCRIPTION: Group = {
  ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
  stripeSubscriptionId: 'sub_1MowQVLkdIwHu7ixeRlqHVzs',
  stripeSubscriptionStatus: 'active',
};
