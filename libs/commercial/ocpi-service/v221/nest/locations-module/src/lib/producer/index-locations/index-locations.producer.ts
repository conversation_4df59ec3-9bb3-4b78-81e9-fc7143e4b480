import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';
import {
  batchMessagesForQueueing,
  mapToBatchMessage,
  sendBatchMessages,
} from '@experience/shared/nest/aws/sqs-module';

export interface MessageBody {
  ppid: string;
}

@Injectable()
export class IndexLocationsProducer {
  private readonly logger = new Logger(IndexLocationsProducer.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly database: OCPIPrismaClient,
    private readonly sqsClientService: SqsClientService
  ) {}

  async queueIndexLocationRequests(): Promise<void> {
    this.logger.log('queueing index location requests');

    const ppids = await this.database.location
      .findMany({
        include: { evses: true },
        where: {
          owner: {
            in: [
              'One Stop',
              'Tesco 75kW Trial',
              'Tesco Bank',
              'Tesco Campus',
              'Tesco Esso Alliance',
              'Tesco Stores Ltd - Distribution Centers',
              'Tesco Stores Ltd - Dot Com',
              'Tesco Stores Ltd - Not Asset 1',
            ],
          },
        },
      })
      .then((locations) => [
        ...new Set(
          locations
            .flatMap((location) => location.evses.map((evse) => evse.ppid))
            .filter((ppid) => ppid !== null)
        ),
      ]);

    const messages = batchMessagesForQueueing(
      ppids.map((ppid) => mapToBatchMessage<MessageBody>({ ppid }))
    );

    await sendBatchMessages(
      this.configService.get('INDEX_LOCATIONS_QUEUE_URL') as string,
      messages,
      this.sqsClientService
    );
  }
}
