openapi: 3.0.0
paths:
  /health:
    get:
      operationId: HealthController_check
      parameters: []
      responses:
        '200':
          description: The Health Check is successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  info:
                    type: object
                    example: &a1
                      database: &a2
                        status: up
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example: {}
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example: *a1
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
        '503':
          description: The Health Check is not successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  info:
                    type: object
                    example: *a1
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example:
                      redis: &a3
                        status: down
                        message: Could not connect
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example:
                      database: *a2
                      redis: *a3
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
      summary: Get Subscriptions API health
      tags:
        - Healthcheck
  /subscriptions:
    get:
      description: List subscriptions by user ID or PPID
      operationId: SubscriptionsController_search
      parameters:
        - name: userId
          required: false
          in: query
          description: The Firebase user ID to filter subscriptions
          schema:
            type: string
        - name: ppid
          required: false
          in: query
          description: The Pod Point ID (ppid) to filter subscriptions
          schema:
            type: string
      responses:
        '200':
          description: Returned when subscriptions are successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListSubscriptionsDTO'
        '500':
          description: Returned when an internal error occurs
      summary: List subscriptions
      tags: &a4
        - Subscriptions
  /subscriptions/{subscriptionId}:
    get:
      description: Get a subscription by id
      operationId: SubscriptionsController_getBySubscriptionId
      parameters:
        - name: subscriptionId
          required: true
          in: path
          description: The ID of the subscription
          schema:
            format: uuid
            type: string
      responses:
        '200':
          description: Returned when subscription is successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PersistedSubscriptionDTO'
        '404':
          description: Returned when subscription could not be found
        '500':
          description: Returned when an internal error occurs
      summary: Get a subscription
      tags: *a4
    delete:
      description:
        Cancels and deletes a given subscription and it's corresponding
        actions and plan.
      operationId: SubscriptionsController_delete
      parameters:
        - name: mode
          required: true
          in: query
          description: here be dragons
          schema:
            enum:
              - debug
            type: string
        - name: subscriptionId
          required: true
          in: path
          description: The ID of the subscription
          schema:
            format: uuid
            type: string
      responses:
        '200':
          description: Returned when successfully cancelled and deleted
        '403':
          description: here be dragons, you have been warned
        '404':
          description: Returned when no matching subscription could be found
        '500':
          description: Returned when something goes wrong
      summary: Delete a subscription
      tags: *a4
  /subscriptions/{subscriptionId}/actions/{actionId}:
    get:
      description: Get an action by action ID within a subscription
      operationId: SubscriptionsController_getByActionId
      parameters:
        - name: subscriptionId
          required: true
          in: path
          description: The ID of the subscription
          schema:
            format: uuid
            type: string
        - name: actionId
          required: true
          in: path
          description: The ID of the action
          schema:
            format: uuid
            type: string
      responses:
        '200':
          description: Returned when action is successfully retrieved
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/SetupDirectDebitActionDTO'
                  - $ref: '#/components/schemas/CheckAffordabilityActionDTO'
                  - $ref: '#/components/schemas/InstallChargingStationActionDTO'
                  - $ref: '#/components/schemas/HomeSurveyActionDTO'
                  - $ref: '#/components/schemas/SignDocumentsActionDTO'
                  - $ref: '#/components/schemas/PayUpfrontFeeActionDTO'
                  - $ref: '#/components/schemas/SignRewardsTOSActionDTO'
                  - $ref: '#/components/schemas/LinkExistingChargerActionDTO'
        '404':
          description: Returned when action could not be found
        '500':
          description: Returned when an internal error occurs
      summary: Get an action
      tags: *a4
    patch:
      description: Resolve an action contextually based on the type of action
      operationId: SubscriptionsController_updateActionById
      parameters:
        - name: subscriptionId
          required: true
          in: path
          description: The ID of the subscription
          schema:
            format: uuid
            type: string
        - name: actionId
          required: true
          in: path
          description: The ID of the action
          schema:
            format: uuid
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/UpdateAffordabilityActionDTO'
                - $ref: '#/components/schemas/UpdateDirectDebitActionDTO'
                - $ref: '#/components/schemas/UpdateHomeSurveyActionDTO'
                - $ref: '#/components/schemas/UpdateSignDocumentsActionDTO'
                - $ref: '#/components/schemas/UpdateSignRewardsTOSActionDTO'
                - $ref: '#/components/schemas/UpdateLinkExistingChargerActionDTO'
      responses:
        '200':
          description: The body of the resolved action
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/SetupDirectDebitActionDTO'
                  - $ref: '#/components/schemas/CheckAffordabilityActionDTO'
                  - $ref: '#/components/schemas/HomeSurveyActionDTO'
                  - $ref: '#/components/schemas/SignDocumentsActionDTO'
                  - $ref: '#/components/schemas/SignRewardsTOSActionDTO'
                  - $ref: '#/components/schemas/LinkExistingChargerActionDTO'
        '400':
          description: Returned when the input to the endpoint was invalid
        '404':
          description: Returned when action could not be found
        '500':
          description: Returned when an internal error occurs
      summary: Resolve an action
      tags: *a4
  /subscriptions/{subscriptionId}/direct-debit:
    get:
      description: Get subscription direct debit details
      operationId: SubscriptionsController_getSubscriptionDirectDebit
      parameters:
        - name: subscriptionId
          required: true
          in: path
          description: The ID of the subscription
          schema:
            format: uuid
            type: string
      responses:
        '200':
          description: Direct debit details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionDirectDebitDTO'
        '404':
          description: Returned when subscription could not be found
        '500':
          description: Returned when an internal error occurs
      summary: Get subscription direct debit details
      tags: *a4
  /subscriptions/{subscriptionId}/documents:
    get:
      description: Get documents related to a given subscription
      operationId: SubscriptionsController_getSubscriptionDocuments
      parameters:
        - name: subscriptionId
          required: true
          in: path
          description: The ID of the subscription
          schema:
            format: uuid
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionDocumentsDTO'
        '404':
          description: Returned when subscription could not be found
        '500':
          description: Returned when an internal error occurs
      summary: Get subscription documents
      tags: *a4
  /subscriptions/{subscriptionId}/documents/{documentId}:
    get:
      description: Downloads the PDF file of a given document
      operationId: SubscriptionsController_getSubscriptionDocument
      parameters:
        - name: subscriptionId
          required: true
          in: path
          description: The ID of the subscription
          schema:
            format: uuid
            type: string
        - name: documentId
          required: true
          in: path
          description: The ID of the document
          schema:
            type: string
      responses:
        '404':
          description: Returned when subscription could not be found
        '500':
          description: Returned when an internal error occurs
      summary: Get subscription document
      tags: *a4
info:
  title: Subscription API
  description: Subscription API Service
  version: 1.0.0
  contact: {}
tags: []
servers: []
components:
  schemas:
    ActionOwner:
      type: string
      enum:
        - USER
        - SYSTEM
    ActionStatus:
      type: string
      enum:
        - PENDING
        - SUCCESS
        - FAILURE
    SetupDirectDebitActionDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
          description: The type of action
          enum:
            - SETUP_DIRECT_DEBIT_V1
        subscriptionId:
          type: string
          format: uuid
        owner:
          allOf:
            - $ref: '#/components/schemas/ActionOwner'
        status:
          allOf:
            - $ref: '#/components/schemas/ActionStatus'
        dependsOn:
          type: array
          items:
            type: string
            format: uuid
        data:
          type: object
      required:
        - id
        - type
        - subscriptionId
        - owner
        - status
        - dependsOn
        - data
    CheckAffordabilityDataDTO:
      type: object
      properties:
        applicationId:
          type: number
          description: The application id of the check affordability check
          nullable: true
        loanId:
          type: number
          description: The loan id from the check affordability check
          nullable: true
      required:
        - applicationId
        - loanId
    CheckAffordabilityActionDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
          description: The type of action
          enum:
            - CHECK_AFFORDABILITY_V1
        subscriptionId:
          type: string
          format: uuid
        owner:
          allOf:
            - $ref: '#/components/schemas/ActionOwner'
        status:
          allOf:
            - $ref: '#/components/schemas/ActionStatus'
        dependsOn:
          type: array
          items:
            type: string
            format: uuid
        data:
          $ref: '#/components/schemas/CheckAffordabilityDataDTO'
      required:
        - id
        - type
        - subscriptionId
        - owner
        - status
        - dependsOn
        - data
    InstallChargingStationDataDTO:
      type: object
      properties:
        ppid:
          type: string
          description: The PPID of the charging station. Set once installed.
          nullable: true
      required:
        - ppid
    InstallChargingStationActionDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
          description: The type of action
          enum:
            - INSTALL_CHARGING_STATION_V1
        subscriptionId:
          type: string
          format: uuid
        owner:
          allOf:
            - $ref: '#/components/schemas/ActionOwner'
        status:
          allOf:
            - $ref: '#/components/schemas/ActionStatus'
        dependsOn:
          type: array
          items:
            type: string
            format: uuid
        data:
          $ref: '#/components/schemas/InstallChargingStationDataDTO'
      required:
        - id
        - type
        - subscriptionId
        - owner
        - status
        - dependsOn
        - data
    HomeSurveyActionDataDTO:
      type: object
      properties:
        surveyUrl:
          type: string
          description: URL of the survey
      required:
        - surveyUrl
    HomeSurveyActionDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
          description: The type of action
          enum:
            - COMPLETE_HOME_SURVEY_V1
        subscriptionId:
          type: string
          format: uuid
        owner:
          allOf:
            - $ref: '#/components/schemas/ActionOwner'
        status:
          allOf:
            - $ref: '#/components/schemas/ActionStatus'
        dependsOn:
          type: array
          items:
            type: string
            format: uuid
        data:
          $ref: '#/components/schemas/HomeSurveyActionDataDTO'
      required:
        - id
        - type
        - subscriptionId
        - owner
        - status
        - dependsOn
        - data
    PayUpfrontFeeActionDataDTO:
      type: object
      properties: {}
    PayUpfrontFeeActionDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
          description: The type of action
          enum:
            - PAY_UPFRONT_FEE_V1
        subscriptionId:
          type: string
          format: uuid
        owner:
          allOf:
            - $ref: '#/components/schemas/ActionOwner'
        status:
          allOf:
            - $ref: '#/components/schemas/ActionStatus'
        dependsOn:
          type: array
          items:
            type: string
            format: uuid
        data:
          $ref: '#/components/schemas/PayUpfrontFeeActionDataDTO'
      required:
        - id
        - type
        - subscriptionId
        - owner
        - status
        - dependsOn
        - data
    SignDocumentDTO:
      type: object
      properties:
        code:
          type: string
          description: The document type
          enum:
            - rca
            - ha
        signingUrl:
          type: string
          description: The URL for the user to sign the loan agreement.
          nullable: true
        signed:
          type: boolean
          description: Is the document signed.
      required:
        - code
        - signingUrl
        - signed
    SignDocumentsDataDTO:
      type: object
      properties:
        documents:
          description: A list of documents to be signed.
          type: array
          items:
            $ref: '#/components/schemas/SignDocumentDTO'
      required:
        - documents
    SignDocumentsActionDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
          description: The type of action
          enum:
            - SIGN_DOCUMENTS_V1
        subscriptionId:
          type: string
          format: uuid
        owner:
          allOf:
            - $ref: '#/components/schemas/ActionOwner'
        status:
          allOf:
            - $ref: '#/components/schemas/ActionStatus'
        dependsOn:
          type: array
          items:
            type: string
            format: uuid
        data:
          $ref: '#/components/schemas/SignDocumentsDataDTO'
      required:
        - id
        - type
        - subscriptionId
        - owner
        - status
        - dependsOn
        - data
    SignRewardsTOSActionDataDTO:
      type: object
      properties:
        revision:
          type: string
          description: The revision of the T&Cs which have been signed
          nullable: true
      required:
        - revision
    SignRewardsTOSActionDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
          description: The type of action
          enum:
            - SIGN_REWARDS_TOS_V1
        subscriptionId:
          type: string
          format: uuid
        owner:
          allOf:
            - $ref: '#/components/schemas/ActionOwner'
        status:
          allOf:
            - $ref: '#/components/schemas/ActionStatus'
        dependsOn:
          type: array
          items:
            type: string
            format: uuid
        data:
          $ref: '#/components/schemas/SignRewardsTOSActionDataDTO'
      required:
        - id
        - type
        - subscriptionId
        - owner
        - status
        - dependsOn
        - data
    LinkExistingChargerActionDataDTO:
      type: object
      properties:
        ppid:
          type: string
          description: The PPID of the charger which has been linked
          nullable: true
      required:
        - ppid
    LinkExistingChargerActionDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        type:
          type: string
          description: The type of action
          enum:
            - LINK_EXISTING_CHARGER_V1
        subscriptionId:
          type: string
          format: uuid
        owner:
          allOf:
            - $ref: '#/components/schemas/ActionOwner'
        status:
          allOf:
            - $ref: '#/components/schemas/ActionStatus'
        dependsOn:
          type: array
          items:
            type: string
            format: uuid
        data:
          $ref: '#/components/schemas/LinkExistingChargerActionDataDTO'
      required:
        - id
        - type
        - subscriptionId
        - owner
        - status
        - dependsOn
        - data
    OrderAddressDTO:
      type: object
      properties:
        line1:
          type: string
          description: Line 1 of the address
        line2:
          type: string
          description: Line 1 of the address
          nullable: true
        line3:
          type: string
          description: Line 1 of the address
          nullable: true
        postcode:
          type: string
          description: Postcode of the address
      required:
        - line1
        - line2
        - line3
        - postcode
    SalesforceOrderDTO:
      type: object
      properties:
        id:
          type: string
          description: id of the order
          format: uuid
        origin:
          type: string
          description: origin of the order
          enum: &a5
            - SALESFORCE
            - SALESFORCE_TEST
            - POD_DRIVE_REWARDS_SELF_SERVE
        orderedAt:
          type: string
          description: date order was placed
          format: date-time
          example: 2025-01-01T00:00:00.000Z
        address:
          $ref: '#/components/schemas/OrderAddressDTO'
        mpan:
          type: string
          description: The person who placed the order's MPAN
        firstName:
          type: string
          description: The first name of the person who placed the order
        lastName:
          type: string
          description: The last name of the person who placed the order
        email:
          type: string
          description: The email address of the person who placed the order
        phoneNumber:
          type: string
          description: The phone number of the person who placed the order
        eCommerceId:
          type: string
          description: The eCommerceId associated with the order
      required:
        - id
        - origin
        - orderedAt
        - address
        - mpan
        - firstName
        - lastName
        - email
        - phoneNumber
        - eCommerceId
    SelfServiceOrderDTO:
      type: object
      properties:
        id:
          type: string
          description: id of the order
          format: uuid
        origin:
          type: string
          description: origin of the order
          enum: *a5
        orderedAt:
          type: string
          description: date order was placed
          format: date-time
          example: 2025-01-01T00:00:00.000Z
        ppid:
          type: string
          description: The PPID of the charger the subscription is for
      required:
        - id
        - origin
        - orderedAt
        - ppid
    PodDrivePlanDTO:
      type: object
      properties:
        id:
          type: string
          description: The ID of plan
          format: uuid
        type:
          type: string
          description: The type of plan
          enum:
            - POD_DRIVE
        productCode:
          type: string
          description: The product code
        allowanceMiles:
          type: number
          description: The number of miles allowed to be claimed
          example: 10000
        allowancePeriod:
          type: string
          description: How often the allowance resets
          enum:
            - ANNUAL
        upfrontFeePounds:
          type: number
          description: How much the plan costs upfront in GBP
          example: 99
        discountedUpfrontFeePounds:
          type: number
          description: The discounted upfront fee in GBP
          example: 45
        monthlyFeePounds:
          type: number
          description: How much the plan costs per month in GBP
          example: 35
        contractDurationMonths:
          type: number
          description: How many months the contract is for
          example: 18
        ratePencePerMile:
          type: number
          description: The conversion rate between pence and miles earned
          example: 2.28
        rateMilesPerKwh:
          type: number
          description: The conversion rate between miles and Kwh
          example: 3.5
        milesRenewalDate:
          type: string
          description: When the miles renew
          format: date-time
          nullable: true
      required:
        - id
        - type
        - productCode
        - allowanceMiles
        - allowancePeriod
        - upfrontFeePounds
        - monthlyFeePounds
        - contractDurationMonths
        - ratePencePerMile
        - rateMilesPerKwh
        - milesRenewalDate
    PersistedSubscriptionDTO:
      type: object
      properties:
        id:
          type: string
          description: subscription id
          format: uuid
        userId:
          type: string
          description: firebase user id
          format: uuid
        status:
          type: string
          description: subscription status
          enum:
            - PENDING
            - ACTIVE
            - CANCELLED
            - SUSPENDED
            - ENDED
            - REJECTED
        order:
          description: order associated with the subscription
          oneOf:
            - $ref: '#/components/schemas/SalesforceOrderDTO'
            - $ref: '#/components/schemas/SelfServiceOrderDTO'
        actions:
          description: actions associated with the subscription
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/HomeSurveyActionDTO'
              - $ref: '#/components/schemas/CheckAffordabilityActionDTO'
              - $ref: '#/components/schemas/InstallChargingStationActionDTO'
              - $ref: '#/components/schemas/SetupDirectDebitActionDTO'
              - $ref: '#/components/schemas/PayUpfrontFeeActionDTO'
              - $ref: '#/components/schemas/SignDocumentsActionDTO'
              - $ref: '#/components/schemas/SignRewardsTOSActionDTO'
              - $ref: '#/components/schemas/LinkExistingChargerActionDTO'
        activatedAt:
          type: string
          description: subscription activation date
          format: date-time
          example: 2025-01-01T00:00:00.000Z
          nullable: true
        createdAt:
          type: string
          description: subscription creation date
          format: date-time
          example: 2025-01-01T00:00:00.000Z
        updatedAt:
          type: string
          description: subscription last updated date
          format: date-time
          example: 2025-01-01T00:00:00.000Z
        deletedAt:
          type: string
          description: subscription deleted date
          format: date-time
          example: 2025-01-01T00:00:00.000Z
          nullable: true
        plan:
          description:
            The plan information for the given subscription - currently only
            PodDrive
          allOf:
            - $ref: '#/components/schemas/PodDrivePlanDTO'
      required:
        - id
        - userId
        - status
        - order
        - actions
        - activatedAt
        - createdAt
        - updatedAt
        - deletedAt
        - plan
    ListSubscriptionsDTO:
      type: object
      properties:
        subscriptions:
          description: list of subscriptions
          type: array
          items:
            $ref: '#/components/schemas/PersistedSubscriptionDTO'
      required:
        - subscriptions
    SubscriptionDirectDebitDTO:
      type: object
      properties:
        accountNumberLastDigits:
          type: string
          description: The last 4 digits of the account number
          example: '1234'
        sortCodeLastDigits:
          type: string
          description: The last 2 digits of the sort code
          example: '23'
        nameOnAccount:
          type: string
          description: The name associated with the direct debit
          example: Mr Tom Wallace
        monthlyPaymentDay:
          type: number
          description: The day of the month in which payment is due
          example: 13
      required:
        - accountNumberLastDigits
        - sortCodeLastDigits
        - nameOnAccount
        - monthlyPaymentDay
    SubscriptionDocumentDTO:
      type: object
      properties:
        issued:
          type: string
          description: When the subscription was last updated
          format: date-time
          example: 2025-05-28T18:28:21.959Z
        link:
          type: string
          description: URL to the document
          example: /subscriptions/55af3ae5-972d-479e-b114-793516b583e3/documents/LMS-48
        format:
          type: string
          description: Format of the document
          example: PDF
        active:
          type: boolean
          description: If this document is active
          example: true
        type:
          type: string
          description: The type of document
          enum:
            - rca
            - ha
      required:
        - issued
        - link
        - format
        - active
        - type
    SubscriptionDocumentsDTO:
      type: object
      properties:
        documents:
          description: An array of documents associated with the subscription
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionDocumentDTO'
      required:
        - documents
    UpdateCheckAffordabilityBillingAddress:
      type: object
      properties:
        flat:
          type: string
          maxLength: 30
        number:
          type: string
          maxLength: 30
        name:
          type: string
          maxLength: 50
        street:
          type: string
          maxLength: 100
        town:
          type: string
          maxLength: 50
        county:
          type: string
          maxLength: 50
        postcode:
          type: string
      required:
        - street
        - town
    UpdateCheckAffordabilityDataDTO:
      type: object
      properties:
        title:
          type: string
          description: Title of the customer
          enum:
            - MR
            - MRS
            - MISS
            - MS
            - MX
            - DR
            - OTHER
        firstName:
          type: string
          description: First name of the customer
          example: John
        middleNames:
          type: string
          description: Middle name(s) of the customer
        lastName:
          type: string
          description: Last name of the customer
          example: Smith
        email:
          type: string
          description: Email address of the customer
          example: <EMAIL>
        phoneNumber:
          type: string
          description: Telephone number of the customer
          example: '+447234567890'
        dateOfBirth:
          type: string
          description: Date of birth of the customer
          format: date
          example: 2025-01-01
        maritalStatus:
          type: string
          enum:
            - SINGLE
            - LIVING_TOGETHER
            - COMMON_LAW
            - MARRIED
            - SEPERATED
            - DIVORCED
            - WIDOWED
            - OTHER
        residentialStatus:
          type: string
          enum:
            - LIVING_WITH_PARENTS
            - COUNCIL_TENANT
            - HOMEOWNER_WITH_MORTGAGE
            - HOMEOWNER_WITHOUT_MORTGAGE
            - PRIVATE_TENANT
            - HOUSING_ASSOCIATION
            - OTHER
        dependencies:
          type: string
          enum:
            - '0'
            - '1'
            - '2'
            - 3+
        billingAddress:
          description:
            At least one of billingAddress' flat, number or name fields must be
            provided
          allOf:
            - $ref: '#/components/schemas/UpdateCheckAffordabilityBillingAddress'
        employmentStatus:
          type: string
          enum:
            - SELF_EMPLOYED
            - PART_TIME
            - FULL_TIME
            - CONTRACT
            - RETIRED
            - ARMED_FORCES
            - HOME_MAKER
            - SINGLE_PARENT
            - DISABLED
            - UNEMPLOYED
        monthlyTakeHomePay:
          type: number
          example: 1000
        monthlyHousePayments:
          type: number
          example: 500
        monthlyTravelAndLivingExpenses:
          type: number
          example: 200
        monthlyHouseholdExpenses:
          type: number
          example: 300
        monthlyCreditPayments:
          type: number
          example: 100
        circumstancesRequireSupport:
          type: string
          enum:
            - SUPPORT_REQUESTED
            - NO_SUPPORT_REQUESTED
      required:
        - title
        - firstName
        - lastName
        - email
        - phoneNumber
        - dateOfBirth
        - maritalStatus
        - residentialStatus
        - dependencies
        - billingAddress
        - employmentStatus
        - monthlyTakeHomePay
        - monthlyHousePayments
        - monthlyTravelAndLivingExpenses
        - monthlyHouseholdExpenses
        - monthlyCreditPayments
        - circumstancesRequireSupport
    UpdateAffordabilityActionDTO:
      type: object
      properties:
        type:
          type: string
          enum:
            - CHECK_AFFORDABILITY_V1
        data:
          $ref: '#/components/schemas/UpdateCheckAffordabilityDataDTO'
      required:
        - type
        - data
    UpdateSetupDirectDebitDataDTO:
      type: object
      properties:
        accountNumber:
          type: string
          description: Bank account number of the customer
          example: '********'
        sortCode:
          type: string
          description: Sort code of the customer’s bank
          example: '123456'
        accountName:
          type: string
          description: Name of account holder
          example: Joe Bloggs
        requiresMoreThanOneSignatory:
          type: boolean
          description: Request is from either one or more authorised signatory
        understandsDirectDebitGuarantee:
          type: boolean
          description: The account holder understands the direct debit guarantee
      required:
        - accountNumber
        - sortCode
        - accountName
        - requiresMoreThanOneSignatory
        - understandsDirectDebitGuarantee
    UpdateDirectDebitActionDTO:
      type: object
      properties:
        type:
          type: string
          enum:
            - SETUP_DIRECT_DEBIT_V1
        data:
          $ref: '#/components/schemas/UpdateSetupDirectDebitDataDTO'
      required:
        - type
        - data
    UpdateHomeSurveyActionDTO:
      type: object
      properties:
        type:
          type: string
          enum:
            - COMPLETE_HOME_SURVEY_V1
        data:
          type: object
      required:
        - type
    UpdateSignDocumentsActionDTO:
      type: object
      properties:
        type:
          type: string
          enum:
            - SIGN_DOCUMENTS_V1
        data:
          type: object
      required:
        - type
    UpdateSignRewardsTOSActionDataDTO:
      type: object
      properties:
        revision:
          type: string
          description: The revision of the T&C signed
      required:
        - revision
    UpdateSignRewardsTOSActionDTO:
      type: object
      properties:
        type:
          type: string
          enum:
            - SIGN_REWARDS_TOS_V1
        data:
          $ref: '#/components/schemas/UpdateSignRewardsTOSActionDataDTO'
      required:
        - type
        - data
    UpdateLinkExistingChargerActionDTO:
      type: object
      properties:
        type:
          type: string
          enum:
            - LINK_EXISTING_CHARGER_V1
      required:
        - type
