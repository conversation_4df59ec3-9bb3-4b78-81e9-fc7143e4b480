package random

import (
	chargeevents "experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	"time"

	"github.com/brianvoe/gofakeit/v7"

	"github.com/google/uuid"
	"k8s.io/utils/ptr"
)

func ChargeCompletedEvent(params *ChargeCompletedEventParams) *chargeevents.Completed {
	plugIn := NewUTCTime()
	if params.PluggedInAt != nil {
		plugIn = *params.PluggedInAt
	}

	authoriserType := chargeevents.App
	if params.AuthoriserType != nil {
		authoriserType = *params.AuthoriserType
	}

	startAt := plugIn.Add(time.Second * time.Duration(gofakeit.IntRange(0, 100)))
	if params.StartedAt != nil {
		startAt = *params.StartedAt
	}
	duration := gofakeit.IntRange(0, 1000)
	if params.Duration != nil {
		duration = *params.Duration
	}
	endAt := startAt.Add(time.Second * time.Duration(duration))
	if params.EndedAt != nil {
		endAt = *params.EndedAt
	}
	unplugAt := endAt.Add(time.Second * time.Duration(gofakeit.IntRange(0, 1000)))
	if params.UnpluggedAt != nil {
		unplugAt = *params.UnpluggedAt
	}

	energyCost := int32(gofakeit.IntRange(0, 5000))
	energyTotal := EnergyTotalFloat64(100, 200, 2)
	if params.Energy != nil {
		energyCost = int32(*params.Energy.EnergyCost)
		energyTotal = *params.Energy.EnergyTotal
	}

	settlementAmount := gofakeit.IntRange(0, 5000)
	if params.SettlementAmount != nil {
		settlementAmount = *params.SettlementAmount
	}
	settlementCurrency := gofakeit.CurrencyShort()
	if params.SettlementCurrency != nil {
		settlementCurrency = *params.SettlementCurrency
	}

	claimChargeID := ptr.To(gofakeit.IntRange(1000, 10000))
	if params.UnconfirmedCharge {
		claimChargeID = nil
		settlementAmount = 0
	}

	event := &chargeevents.Completed{
		Base: chargeevents.Base{
			AggregateID: params.AggregateID,
			Event:       chargeevents.TypeCompleted,
			EventID:     uuid.NewString(),
			PublishedAt: time.Time{},
		},
		Payload: chargeevents.CompletedPayload{
			Charge: chargeevents.Payload{
				ID:                    params.ChargeID,
				EnergyTotal:           ptr.To(energyTotal),
				GenerationEnergyTotal: ptr.To(energyTotal),
				GridEnergyTotal:       ptr.To(energyTotal),
				ChargeDurationTotal:   ptr.To(int32(duration)),
				StartedAt:             &startAt,
				EndedAt:               &endAt,
				PluggedInAt:           &plugIn,
				UnpluggedAt:           &unplugAt,
			},
			ChargingStation: chargeevents.ChargingStation{
				ID:     params.UnitPPID,
				DoorID: Door(),
			},
			Location: chargeevents.Location{ID: params.LocationID},
			Authorisation: chargeevents.Authorisation{
				Ref:             nil,
				Type:            ptr.To(string(authoriserType)),
				ClaimedChargeID: claimChargeID,
				AuthoriserID:    &params.AuthoriserPK,
			},
			Energy: &chargeevents.Energy{
				EnergyCost:         ptr.To(energyCost),
				EnergyCostCurrency: "GBP",
			},
		},
	}

	if !params.RemoveBillingPayload {
		event.Payload.Billing = &chargeevents.Billing{
			SettlementAmount:   settlementAmount,
			SettlementCurrency: settlementCurrency,
		}
	}

	return event
}

type EnergyParams struct {
	EnergyCost  *int
	EnergyTotal *float64
}

type ChargeCompletedEventParams struct {
	ChargeID             int
	AggregateID          uuid.UUID
	AuthoriserType       *chargeevents.AuthoriserType
	AuthoriserPK         int
	UnitPPID             chargers.StationID
	LocationID           int
	PluggedInAt          *time.Time
	UnpluggedAt          *time.Time
	StartedAt            *time.Time
	EndedAt              *time.Time
	Duration             *int
	Energy               *EnergyParams
	SettlementAmount     *int
	UnconfirmedCharge    bool
	UserID               *int
	SettlementCurrency   *string
	RemoveBillingPayload bool
}
