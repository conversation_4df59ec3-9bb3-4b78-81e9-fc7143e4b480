package random

import (
	"database/sql"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/shared/go/db"
	"fmt"
	"math/rand/v2"
	"slices"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"

	"github.com/google/uuid"

	"k8s.io/utils/ptr"
)

var (
	chargerTypes          = []sqlc.Access{sqlc.AccessHome, sqlc.AccessPublic, sqlc.AccessPrivate}
	commercialChargerType = []sqlc.Access{sqlc.AccessPublic, sqlc.AccessPrivate}
	groupDataValues       = []GroupAndSiteData{
		{
			Group: NameID{
				ID:   uuid.MustParse(uuid.NewString()),
				Name: "UK Electrical Installations",
			},
			siteDataValues: []NameID{
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "Staff charging t",
				},
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "Visitors charging t",
				},
			},
		},
		{
			Group: NameID{
				ID:   uuid.MustParse(uuid.NewString()),
				Name: "Adept Graphics",
			},
			siteDataValues: []NameID{
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "Falcon Street t",
				},
			},
		},
		{
			Group: NameID{
				ID:   uuid.MustParse(uuid.NewString()),
				Name: "Bournemouth International Airport",
			},
			siteDataValues: []NameID{
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "Terminal 1 t",
				},
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "International Arrivals t",
				},
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "Domestic Departures t",
				},
			},
		},
		{
			Group: NameID{
				ID:   uuid.MustParse(uuid.NewString()),
				Name: "The Lighthouse Hotel",
			},
			siteDataValues: []NameID{
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "VIP Charging stations t",
				},
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "Guest Charging stations t",
				},
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "Valet Parking t",
				},
			},
		},
		{
			Group: NameID{
				ID:   uuid.MustParse(uuid.NewString()),
				Name: "Taylor Wimpey - Bridge Road East",
			},
			siteDataValues: []NameID{
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "West charging area t",
				},
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "North charging area t",
				},
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "South charging area t",
				},
			},
		},
		{
			Group: NameID{
				ID:   uuid.MustParse(uuid.NewString()),
				Name: "Croydon Health Services",
			},
			siteDataValues: []NameID{
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "Outpatients car park t",
				},
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "Staff car park t",
				},
				{
					ID:   uuid.MustParse(uuid.NewString()),
					Name: "Ambulance and paramedics car park t",
				},
			},
		},
	}
)

func ChargerID() string {
	prefix := gofakeit.RandomString([]string{"PSL-", "PG-", "veefil-"})
	suffix := gofakeit.IntRange(100000, 10000000)
	return fmt.Sprintf("%s%d", prefix, suffix)
}

// GeneratePodPointPPID returns a unique PPID with the "PODPOINT-" prefix.
// When passed to the AssetSummaryAPIStub, this prefix will trigger the stub to assign ownership as POD_POINT.
func GeneratePodPointPPID() string {
	prefix := "PODPOINT-"
	suffix := gofakeit.IntRange(100000, 10000000)
	return fmt.Sprintf("%s%d", prefix, suffix)
}

func ChargeDurationTotal() int32 {
	//nolint:gosec // Since this is only for tests, it is okay to use a 'weak' random package
	return rand.Int32N(900)
}

func EnergyCost() int32 {
	//nolint:gosec // Since this is only for tests, it is okay to use a 'weak' random package
	return rand.Int32N(200)
}

func CommercialChargerID() chargers.StationID {
	stationIDs := []string{"PG-", "veefil-"}
	return chargers.StationID(fmt.Sprintf("%s%d", stationIDs[gofakeit.IntRange(0, len(stationIDs)-1)], gofakeit.IntRange(100000, 10000000)))
}

func HomeChargerID() chargers.StationID {
	return chargers.StationID(fmt.Sprintf("PSL-%d", gofakeit.IntRange(100000, 10000000)))
}

func ChargerName() string {
	return gofakeit.RandomString([]string{"Gabe-Mark", "Lucy-Jack", "Bria-Alex", "Hank-Anne", "Abby-Bart", "Otis-Dawn", "Chet-Jeff"})
}

func ChargerNameNullable() sql.NullString {
	var value *string
	if !gofakeit.Bool() {
		value = ptr.To(ChargerName())
	}
	return db.ToNullString(value)
}

func ChargerTypeNullable() sqlc.NullAccess {
	return sqlc.NullAccess{
		Access: chargerTypes[gofakeit.IntRange(0, 2)],
		Valid:  true,
	}
}

func CommercialChargerTypeNullable() sqlc.NullAccess {
	return sqlc.NullAccess{
		Access: commercialChargerType[gofakeit.IntRange(0, 1)],
		Valid:  true,
	}
}

func Door() string {
	return gofakeit.RandomString([]string{"A", "B", "C"})
}

func GroupData() GroupAndSiteData {
	return groupDataValues[gofakeit.IntRange(0, len(groupDataValues)-1)]
}

func SiteName(t *testing.T) string {
	t.Helper()
	randomString := gofakeit.RandomString([]string{
		"Endeavour House",
		"Drum Brae Library",
		"Tesco Dot Com - Erith",
		"Motorline Crawley",
		"Saren Engineering Birmingham",
		"The Falcon Hotel",
		"AT&T",
		"Redrow Homes - Waterlode",
		"Boston Ambulance Station",
	})
	return fmt.Sprintf("%s - %s", randomString, t.Name())
}

func GroupName(t *testing.T) string {
	t.Helper()
	randomString := gofakeit.RandomString([]string{
		"Tesco",
		"Lidl",
		"Aldi",
		"Sainsbury's",
		"Morissons",
		"Waitrose",
		"Iceland",
		"Co-Op",
		"Spar",
	})
	return fmt.Sprintf("%s - %s", randomString, t.Name())
}

func Location() (*time.Location, error) {
	name := TimezoneString()
	location, err := time.LoadLocation(name)
	if err != nil {
		return nil, fmt.Errorf("failed to load timezone %s: %w", name, err)
	}

	return location, nil
}

func Timezone() *string {
	if gofakeit.IntRange(0, 9) == 0 {
		return nil
	}
	return ptr.To(gofakeit.TimeZoneRegion())
}

func TimezoneString() string {
	// See XDP-1310: we encountered some issues with the below timezone in our
	// e2e tests. We are not entirely sure why this specific region causes
	// issues, but we assume it is because this timezone doesn't follow Daylight
	// Savings Time BUT still reports a "Latest clock change". (For 29
	// Februrary 2024 of all dates, which only is possible in leap years.) See
	// Asia/Almaty: https://www.zeitverschiebung.net/en/timezone/asia--almaty
	// Other timezones that don't follow Daylight Savings Time, i.e. Asia/Aden,
	// are fine for us though, so the leap year theory seems likely.
	// Until then, let's hope we won't expand to Asia/Almaty.
	blacklist := []string{
		"Asia/Almaty",
	}

	var randomTimezone string

	for randomTimezone == "" || slices.Contains(blacklist, randomTimezone) {
		randomTimezone = gofakeit.TimeZoneRegion()
	}

	return randomTimezone
}

func NewChargerData(chargerType *sqlc.Access) ChargerData {
	var access sqlc.Access
	if chargerType == nil {
		access = ChargerTypeNullable().Access
	} else {
		access = *chargerType
	}
	var id, door, name, siteName, groupName sql.NullString
	var siteID, groupID uuid.NullUUID

	timezone := db.ToNullString(Timezone())

	switch access {
	case sqlc.AccessHome:
		id = db.ToNullString(ptr.To(string(HomeChargerID())))
		door = db.ToNullString(ptr.To("A"))
		name = db.ToNullString(nil)
		siteID = db.ToNullUUID(nil)
		siteName = db.ToNullString(nil)
		groupID = db.ToNullUUID(nil)
		groupName = db.ToNullString(nil)
	case sqlc.AccessPrivate, sqlc.AccessPublic:
		id = db.ToNullString(ptr.To(string(CommercialChargerID())))
		door = db.ToNullString(ptr.To(Door()))
		name = db.ToNullString(ptr.To(ChargerName()))
		groupData := GroupData()
		siteData := groupData.SiteData()
		siteID = db.ToNullUUID(ptr.To(siteData.ID))
		siteName = db.ToNullString(ptr.To(siteData.Name))
		groupID = db.ToNullUUID(ptr.To(groupData.Group.ID))
		groupName = db.ToNullString(ptr.To(groupData.Group.Name))
	}

	return ChargerData{
		Type: sqlc.NullAccess{
			Access: access,
			Valid:  true,
		},
		ID:        id,
		Door:      door,
		Name:      name,
		Timezone:  timezone,
		SiteID:    siteID,
		SiteName:  siteName,
		GroupID:   groupID,
		GroupName: groupName,
	}
}

type ChargerData struct {
	Type      sqlc.NullAccess
	ID        sql.NullString
	Door      sql.NullString
	Name      sql.NullString
	Timezone  sql.NullString
	SiteID    uuid.NullUUID
	SiteName  sql.NullString
	GroupID   uuid.NullUUID
	GroupName sql.NullString
}

type NameID struct {
	Name string
	ID   uuid.UUID
}

type GroupAndSiteData struct {
	Group          NameID
	siteDataValues []NameID
}

func (gsd GroupAndSiteData) SiteData() NameID {
	return gsd.siteDataValues[gofakeit.IntRange(0, len(gsd.siteDataValues)-1)]
}
