package random

import (
	"experience/libs/data-platform/event-sourcing/projection/drivers"
	"experience/libs/shared/go/service/domain"
	"time"

	"github.com/brianvoe/gofakeit/v7"

	"k8s.io/utils/ptr"
)

func CreateAPIStatsSummary() drivers.StatsSummary {
	statsSummary := drivers.StatsSummary{
		Energy: drivers.EnergySummary{
			Home: drivers.EnergyBreakdown{
				Grid:       ptr.To(gofakeit.Float64Range(0.0, 999.99)),
				Generation: ptr.To(gofakeit.Float64Range(0.0, 999.99)),
				Total:      gofakeit.Float64Range(0.0, 999.99),
			},
			Private: drivers.EnergyBreakdown{
				Total: gofakeit.Float64Range(0.0, 999.99),
			},
			Public: drivers.EnergyBreakdown{
				Total: gofakeit.Float64Range(0.0, 999.99),
			},
		},
		Cost: drivers.CostSummary{
			Home:    []domain.Money64{{Amount: gofakeit.Int64(), Currency: "GBP"}},
			Private: []domain.Money64{{Amount: gofakeit.Int64(), Currency: "GBP"}},
			Public:  []domain.Money64{{Amount: gofakeit.Int64(), Currency: "GBP"}},
		},
		Duration: drivers.DurationSummary{
			Home:    int(gofakeit.Int32()),
			Private: int(gofakeit.Int32()),
			Public:  int(gofakeit.Int32()),
		},
	}

	return statsSummary
}

func CreateIntervals(from, to time.Time, summary *drivers.StatsSummary) []*drivers.IntervalStats {
	intervals := make([]*drivers.IntervalStats, 1)
	intervals[0] = ptr.To(drivers.IntervalStats{
		From:  from,
		To:    to,
		Stats: *summary,
	})
	return intervals
}
