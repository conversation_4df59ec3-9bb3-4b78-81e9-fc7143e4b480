package random

import (
	"time"

	"github.com/brianvoe/gofakeit/v7"

	"github.com/google/uuid"
	"k8s.io/utils/ptr"
)

func OneTo(maximum uint) uint {
	return gofakeit.UintRange(1, maximum)
}

func PopulateUUID(id *uuid.UUID) *uuid.UUID {
	if id == nil {
		return ptr.To(uuid.New())
	}
	return id
}

func UUIDsIncluding(id *uuid.UUID) []uuid.UUID {
	ids := make([]uuid.UUID, 0)
	if id == nil {
		id = PopulateUUID(id)
	}
	ids = append(ids, *id)

	upper := int(TwoTo(10))
	for i := 0; i < upper; i++ {
		ids = append(ids, uuid.New())
	}

	return ids
}

func TwoTo(maximum uint) uint {
	return gofakeit.UintRange(2, maximum)
}

func NewUTCTime() time.Time {
	return gofakeit.Date().UTC().Round(time.Second)
}
