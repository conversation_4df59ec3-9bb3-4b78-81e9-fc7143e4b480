package random

import (
	"experience/libs/data-platform/api/contract/gen/drivers"
	charge "experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	"experience/libs/data-platform/test/fixtures"
	eventstore "experience/libs/shared/go/event-store"
	httphelpers "experience/libs/shared/go/http"
	"fmt"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
)

const (
	from = "2023-10-09"
	to   = "2023-10-10"
)

func TimesChargeWithEvents(t *testing.T, times uint, driver Driver, seed *ChargeParamsSeed) (drivers.DriversChargesResponse, []eventstore.Event, []*Charger) {
	t.Helper()
	charges := make([]*drivers.Charge, 0)
	events := make([]eventstore.Event, 0)
	randomChargers := make([]*Charger, 0)

	for i := uint(0); i < times; i++ {
		locationID := fixtures.Sequence.Get()
		chargeWithEvent, event, randomCharger := ChargeWithEvents(t, &driver, seed, locationID)
		randomChargers = append(randomChargers, &randomCharger)
		fmt.Printf("creating charger with id %s at location %d with timezone %s\n", randomCharger.ID, locationID, ptr.Deref(randomCharger.Timezone, "Not Set"))
		charges = append(charges, &chargeWithEvent)
		events = append(events, event...)
	}

	return drivers.DriversChargesResponse{
		Data: &drivers.Charges{
			Count:   len(charges),
			Charges: charges,
		},
		Meta: (*drivers.Meta)(httphelpers.PopulateMetaParams(drivers.RetrieveChargesPayload{
			DriverID: driver.DriverID.String(),
			From:     from,
			To:       to,
		})),
	}, events, randomChargers
}

func ChargeWithEvents(t *testing.T, driver *Driver, seed *ChargeParamsSeed, locationID int64) (drivers.Charge, []eventstore.Event, Charger) {
	t.Helper()
	chargePK, chargeID := ChargeIDs(fixtures.IDRangeLowerBound, fixtures.IDRangeUpperBound)
	apiCharge := CreateAPICharge(driver.DriverID, &chargeID, seed)

	events := make([]eventstore.Event, 0)
	if apiCharge.Charger.PluggedInAt != nil {
		events = append(events, ToChargeCompleted(&apiCharge, chargePK, driver, locationID))
	}

	if apiCharge.ExpensedTo != nil {
		events = append(events, ToChargeExpensed(&apiCharge))
	}

	charger := Charger{
		ID:       apiCharge.Charger.ID,
		Name:     apiCharge.Charger.Name,
		Timezone: seed.ChargerTimezone,
		SiteName: apiCharge.Charger.SiteName,
		Location: locationID,
		Access:   &apiCharge.Charger.Type,
		ChargePK: chargePK,
	}

	return apiCharge, events, charger
}

func ToChargeCompleted(dc *drivers.Charge, chargePK int, driver *Driver, locationID int64) eventstore.Event {
	plugAt, _ := time.Parse(time.RFC3339, *dc.Charger.PluggedInAt)
	unplugAt, _ := time.Parse(time.RFC3339, *dc.Charger.UnpluggedAt)
	chargePayload := charge.Payload{
		ID:                    chargePK,
		EnergyTotal:           dc.EnergyTotal,
		GenerationEnergyTotal: dc.GenerationEnergyTotal,
		GridEnergyTotal:       dc.GridEnergyTotal,
		PluggedInAt:           &plugAt,
		UnpluggedAt:           &unplugAt,
	}

	if dc.StartedAt != nil {
		startAt, _ := time.Parse(time.RFC3339, *dc.StartedAt)
		endAt, _ := time.Parse(time.RFC3339, *dc.EndedAt)

		chargePayload.StartedAt = &startAt
		chargePayload.EndedAt = &endAt
		chargePayload.ChargeDurationTotal = ptr.To(int32(*dc.Duration))
	}

	authoriserID := driver.AuthoriserID
	return &charge.Completed{
		Base: charge.Base{
			AggregateID: uuid.MustParse(dc.ID),
			Event:       charge.TypeCompleted,
			EventID:     uuid.NewString(),
			PublishedAt: charge.DefaultPublishedAt(),
		},
		Payload: charge.CompletedPayload{
			Charge: chargePayload,
			ChargingStation: charge.ChargingStation{
				ID:     chargers.StationID(dc.Charger.ID),
				DoorID: dc.Charger.Door,
			},
			Authorisation: charge.Authorisation{
				Type:         ptr.To("user"),
				AuthoriserID: ptr.To(int(authoriserID)),
			},
			Location: charge.Location{ID: int(locationID)},
		},
	}
}

func ToChargeCosted(dc *drivers.Charge) eventstore.Event {
	payload := charge.CostedPayload{
		EnergyCost:         ptr.To(int32(dc.Cost.Amount)),
		EnergyCostCurrency: dc.Cost.Currency,
	}
	return &charge.Costed{
		Base: charge.Base{
			AggregateID: uuid.MustParse(dc.ID),
			Event:       charge.TypeCosted,
			EventID:     uuid.NewString(),
			PublishedAt: charge.DefaultPublishedAt(),
		},
		Payload: payload,
	}
}

func ToChargeExpensed(dc *drivers.Charge) eventstore.Event {
	return &charge.Expensed{
		Base: charge.Base{
			AggregateID: uuid.MustParse(dc.ID),
			Event:       charge.TypeExpensed,
			EventID:     uuid.NewString(),
			PublishedAt: charge.DefaultPublishedAt(),
		},
		Payload: charge.ExpensedPayload{
			GroupID:   uuid.MustParse(dc.ExpensedTo.ID),
			GroupName: dc.ExpensedTo.Name,
		},
	}
}
