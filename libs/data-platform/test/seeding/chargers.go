package seeding

import (
	"context"
	"database/sql"
	sqlccharges "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/data-platform/test/random"
	"strconv"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/google/uuid"
)

type Options = func(p *sqlccharges.CreateChargeProjectionParams)

func ChargeProjectionsByCharger(ctx context.Context, t *testing.T, queries *sqlccharges.Queries, chargerID string, from time.Time, options ...Options) (projections []*sqlccharges.ProjectionsCharge) {
	t.Helper()

	numberOfProjections := gofakeit.IntRange(10, 60)
	projections = make([]*sqlccharges.ProjectionsCharge, numberOfProjections)

	for i := 0; i < numberOfProjections; i++ {
		energyCost := int32(gofakeit.IntRange(100, 5000))
		settlementAmount := int32(gofakeit.IntRange(100, 5000))
		params := &sqlccharges.CreateChargeProjectionParams{
			ChargeUUID:           uuid.New(),
			ChargerID:            sql.NullString{String: chargerID, Valid: chargerID != ""},
			UnpluggedAt:          sql.NullTime{Time: from.AddDate(0, 0, i).Add(time.Duration(gofakeit.IntRange(1, 59)) * time.Minute), Valid: true},
			EnergyTotal:          sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			EnergyCost:           sql.NullInt32{Int32: energyCost, Valid: true},
			SettlementAmount:     sql.NullInt32{Int32: settlementAmount, Valid: true},
			Confirmed:            sql.NullBool{Bool: gofakeit.Bool(), Valid: true},
			RewardEligibleEnergy: "0.0",
		}

		for _, option := range options {
			option(params)
		}

		projection := random.CreateProjectionsCharge(ctx, t, queries, params)
		projections[i] = projection
	}
	return projections
}
