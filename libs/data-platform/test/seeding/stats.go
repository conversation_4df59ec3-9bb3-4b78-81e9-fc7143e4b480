package seeding

import (
	"context"
	"database/sql"
	sqlccharges "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/sqlc"
	"testing"
	"time"

	"github.com/google/uuid"

	"github.com/stretchr/testify/assert"
)

func ChargesForDriver(ctx context.Context, t *testing.T, queries *sqlccharges.Queries, driverID uuid.UUID) []*sqlccharges.ProjectionsCharge {
	t.Helper()

	// Charge should be outside the from/to parameters - tests for double counting at midnight
	homeChargeOutOfRange := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID:            uuid.New(),
			GroupID:               uuid.NullUUID{UUID: uuid.New(), Valid: true},
			UserIds:               []uuid.UUID{driverID},
			SiteID:                uuid.NullUUID{UUID: uuid.New(), Valid: true},
			PluggedInAt:           sql.NullTime{Time: time.Date(2023, 12, 1, 0, 0, 0, 0, time.UTC), Valid: true},
			UnpluggedAt:           sql.NullTime{Time: time.Date(2023, 12, 1, 2, 15, 16, 0, time.UTC), Valid: true},
			EnergyTotal:           sql.NullString{String: "3.7", Valid: true},
			GridEnergyTotal:       sql.NullString{String: "1.3", Valid: true},
			GenerationEnergyTotal: sql.NullString{String: "2.6", Valid: true},
			SettlementAmount:      sql.NullInt32{Int32: 0, Valid: true},
			ChargerType:           sqlccharges.NullAccess{Access: sqlccharges.AccessHome, Valid: true},
			ChargeDurationTotal:   sql.NullInt32{Int32: 146, Valid: true},
			EnergyCost:            sql.NullInt32{Int32: 278, Valid: true},
			RewardEligibleEnergy:  "0.0",
		})

	homeCharge := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID:            uuid.New(),
			GroupID:               uuid.NullUUID{UUID: uuid.New(), Valid: true},
			UserIds:               []uuid.UUID{driverID},
			SiteID:                uuid.NullUUID{UUID: uuid.New(), Valid: true},
			PluggedInAt:           sql.NullTime{Time: time.Date(2023, 11, 30, 23, 59, 59, 0, time.UTC), Valid: true},
			UnpluggedAt:           sql.NullTime{Time: time.Date(2023, 12, 1, 2, 15, 16, 0, time.UTC), Valid: true},
			EnergyTotal:           sql.NullString{String: "5.5", Valid: true},
			GridEnergyTotal:       sql.NullString{String: "4.4", Valid: true},
			GenerationEnergyTotal: sql.NullString{String: "1.1", Valid: true},
			SettlementAmount:      sql.NullInt32{Int32: 0, Valid: true},
			ChargerType:           sqlccharges.NullAccess{Access: sqlccharges.AccessHome, Valid: true},
			ChargeDurationTotal:   sql.NullInt32{Int32: 133, Valid: true},
			EnergyCost:            sql.NullInt32{Int32: 215, Valid: true},
			RewardEligibleEnergy:  "0.0",
		})

	privateCharge := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID:           uuid.New(),
			GroupID:              uuid.NullUUID{UUID: uuid.New(), Valid: true},
			UserIds:              []uuid.UUID{driverID},
			SiteID:               uuid.NullUUID{UUID: uuid.New(), Valid: true},
			PluggedInAt:          sql.NullTime{Time: time.Date(2023, 11, 14, 11, 15, 16, 0, time.UTC), Valid: true},
			UnpluggedAt:          sql.NullTime{Time: time.Date(2023, 11, 14, 12, 15, 16, 0, time.UTC), Valid: true},
			EnergyTotal:          sql.NullString{String: "6.6", Valid: true},
			SettlementAmount:     sql.NullInt32{Int32: 200, Valid: true},
			ChargerType:          sqlccharges.NullAccess{Access: sqlccharges.AccessPrivate, Valid: true},
			ChargeDurationTotal:  sql.NullInt32{Int32: 180, Valid: true},
			EnergyCost:           sql.NullInt32{Int32: 981, Valid: true},
			RewardEligibleEnergy: "0.0",
		})

	publicCharge := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID:           uuid.New(),
			GroupID:              uuid.NullUUID{UUID: uuid.New(), Valid: true},
			UserIds:              []uuid.UUID{driverID},
			SiteID:               uuid.NullUUID{UUID: uuid.New(), Valid: true},
			PluggedInAt:          sql.NullTime{Time: time.Date(2023, 10, 2, 1, 15, 16, 0, time.UTC), Valid: true},
			UnpluggedAt:          sql.NullTime{Time: time.Date(2023, 10, 2, 4, 15, 16, 0, time.UTC), Valid: true},
			EnergyTotal:          sql.NullString{String: "7.7", Valid: true},
			SettlementAmount:     sql.NullInt32{Int32: 300, Valid: true},
			ChargerType:          sqlccharges.NullAccess{Access: sqlccharges.AccessPublic, Valid: true},
			ChargeDurationTotal:  sql.NullInt32{Int32: 267, Valid: true},
			EnergyCost:           sql.NullInt32{Int32: 415, Valid: true},
			RewardEligibleEnergy: "0.0",
		})

	return []*sqlccharges.ProjectionsCharge{homeChargeOutOfRange, homeCharge, privateCharge, publicCharge}
}

func ChargesForSite(t *testing.T, queries *sqlc.Queries, siteID int64, currentTime time.Time) (func() error, error) {
	t.Helper()
	ctx := context.Background()

	unit, err := queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	assert.NoError(t, err)

	address, err := queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(siteID))
	assert.NoError(t, err)

	location, err := queries.CreateLocation(ctx, fixtures.CreatePodPointLocationParams(&address, &unit, true, false))
	assert.NoError(t, err)

	billingEvent, err := queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750, 1))
	assert.NoError(t, err)

	billingEvent2, err := queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6220, 2))
	assert.NoError(t, err)

	startsAt := currentTime
	endsAt := currentTime
	_, err = queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, location, unit, billingEvent, 16, "10", &startsAt, &endsAt, 0, 1, 1, 0))
	assert.NoError(t, err)

	_, err = queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, location, unit, billingEvent2, 23, "17", &startsAt, &endsAt, 999135, 1, 1, 1))
	assert.NoError(t, err)

	return func() error {
		t.Helper()
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)
		return nil
	}, nil
}

func GroupStatisticsData(t *testing.T, queries *sqlc.Queries, groupUID string, fromDate, toDate time.Time, locationID ...int64) (func() error, error) {
	t.Helper()
	ctx := context.Background()

	group, err := queries.CreateOrganisation(ctx, fixtures.CreateOrganisationParams(0, groupUID, "Test Organisation"))
	assert.NoError(t, err)

	unit1, err := queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	assert.NoError(t, err)

	unit2, err := queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	assert.NoError(t, err)

	unit3, err := queries.CreateUnit(ctx, fixtures.CreateUnitParams(0, "PP-10001"))
	assert.NoError(t, err)

	address1, err := queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(0, group.ID))
	assert.NoError(t, err)

	address2, err := queries.CreateAddress(ctx, fixtures.CreatePodPointAddressParams(0, group.ID))
	assert.NoError(t, err)

	location1, err := queries.CreateLocation(ctx, fixtures.CreatePodPointLocationParams(&address1, &unit1, false, false, locationID...))
	assert.NoError(t, err)

	location2, err := queries.CreateLocation(ctx, fixtures.CreatePodPointLocationParams(&address1, &unit2, false, false))
	assert.NoError(t, err)

	location3, err := queries.CreateLocation(ctx, fixtures.CreatePodPointLocationParams(&address2, &unit3, false, false))
	assert.NoError(t, err)

	billingEvent1, err := queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750, 1))
	assert.NoError(t, err)

	billingEvent2, err := queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750, 1))
	assert.NoError(t, err)

	billingEvent3, err := queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750, 2))
	assert.NoError(t, err)

	billingEvent4, err := queries.CreateBillingEvent(ctx, fixtures.CreateBillingEventParams(-6750, 3))
	assert.NoError(t, err)

	startsAt := fromDate.Add(time.Hour * 1)
	charge1, err := queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, location1, unit1, billingEvent1, 16, "10", &startsAt, &toDate, 0, group.ID, 0, 1))
	assert.NoError(t, err)
	// Updates the charges lookup table with a charge
	err = queries.CloseCharge(ctx, charge1.ID)
	assert.NoError(t, err)

	charge2, err := queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, location2, unit2, billingEvent2, 16, "10", &startsAt, &toDate, 0, group.ID, 0, 1))
	assert.NoError(t, err)
	err = queries.CloseCharge(ctx, charge2.ID)
	assert.NoError(t, err)

	charge3, err := queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, location1, unit1, billingEvent3, 16, "10", &startsAt, &toDate, 0, group.ID, 0, 2))
	assert.NoError(t, err)
	err = queries.CloseCharge(ctx, charge3.ID)
	assert.NoError(t, err)

	charge4, err := queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, location3, unit3, billingEvent4, 16, "10", &startsAt, &toDate, 0, group.ID, 0, 3))
	assert.NoError(t, err)
	err = queries.CloseCharge(ctx, charge4.ID)
	assert.NoError(t, err)

	return func() error {
		t.Helper()
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)
		return nil
	}, nil
}
