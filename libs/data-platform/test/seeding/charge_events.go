package seeding

import (
	"context"
	"database/sql"
	chargeevents "experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/sqlc"
	sharedtest "experience/libs/shared/go/test"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"

	"github.com/stretchr/testify/assert"
)

const FixedRate = 11

// ClaimChargeData Seeds the data that is required to complete the ClaimCharge command
func ClaimChargeData(t *testing.T, apiInfraQueries *sqlc.Queries, params ClaimChargeDataParams) *CreatedClaimChargeData {
	t.Helper()
	ctx := context.Background()
	populateNilClaimDataParams(&params)

	ppid := random.ChargerID()
	if params.PPID != "" {
		ppid = params.PPID
	}

	authoriser, err := apiInfraQueries.CreateAuthoriser(ctx, sqlc.CreateAuthoriserParams{
		ID:   *params.AuthPK,
		Uid:  params.UserUUID.String(),
		Type: string(chargeevents.User),
	})
	assert.NoError(t, err)

	model, err := apiInfraQueries.CreatePodModel(ctx, fixtures.CreatePodModelParams(0))
	assert.NoError(t, err)

	unit, err := apiInfraQueries.CreateUnit(ctx, sqlc.CreateUnitParams{
		ID:      fixtures.Sequence.Get(),
		Ppid:    ppid,
		ModelID: model.ID,
		Name:    random.ChargerNameNullable(),
	})
	assert.NoError(t, err)

	var (
		group          sqlc.PodpointGroup
		revenueProfile sqlc.PodpointRevenueProfile
		businessName   string
	)
	if !params.IsHome {
		businessName = random.SiteName(t)
		groupID := uuid.New().String()
		if params.GroupID != "" {
			groupID = params.GroupID
		}
		group, err = apiInfraQueries.CreateOrganisation(ctx, fixtures.CreateOrganisationParams(0, groupID, random.GroupName(t)))
		assert.NoError(t, err)

		tierTimes := fixtures.TierTimes{
			BeginTime: sharedtest.MustParse(time.TimeOnly, "00:00:00"),
			EndTime:   sharedtest.MustParse(time.TimeOnly, "00:00:00"),
			BeginDay:  2,
			EndDay:    2,
		}
		revenueProfile, err = apiInfraQueries.CreateRevenueProfile(ctx, fixtures.CreateRevenueProfileParams(group.ID, "GBP", nil))
		assert.NoError(t, err)
		_, err = apiInfraQueries.CreateRevenueProfileTier(ctx, fixtures.CreateRevenueProfileTierParams(revenueProfile.ID, sqlc.UserTypePublic, FixedRate*10000, 0, sqlc.TermFixed, &tierTimes, nil))
		assert.NoError(t, err)
	}

	siteID := fixtures.Sequence.Get()
	if params.SiteID != nil {
		siteID = *params.SiteID
	}

	address, err := apiInfraQueries.CreateAddress(
		ctx,
		sqlc.CreateAddressParams{
			ID:           siteID,
			BusinessName: businessName,
			Line1:        "234 Banner St",
			Line2:        "Line 2",
			PostalTown:   "London",
			Postcode:     "EC1Y 8QE",
			Country:      "GB",
			Description:  t.Name(),
			GroupID: sql.NullInt64{
				Int64: group.ID,
				Valid: !params.IsHome,
			},
		})
	assert.NoError(t, err)

	createLocationParameters := fixtures.CreatePodPointLocationParams(&address, &unit, params.IsHome, true)
	createLocationParameters.RevenueProfileID = sql.NullInt64{
		Int64: revenueProfile.ID,
		Valid: !params.IsHome,
	}
	location, err := apiInfraQueries.CreateLocation(ctx, createLocationParameters)
	assert.NoError(t, err)

	return &CreatedClaimChargeData{
		Authoriser: authoriser,
		Unit:       unit,
		Address:    address,
		Location:   location,
		Model:      model,
		Group:      group,
	}
}

type ClaimChargeDataParams struct {
	AuthPK   *int64
	UserUUID *uuid.UUID
	PPID     string
	GroupID  string
	SiteID   *int64
	IsHome   bool
}

type CreatedClaimChargeData struct {
	Authoriser sqlc.PodpointAuthoriser
	Unit       sqlc.PodpointPodUnit
	Address    sqlc.PodpointPodAddress
	Location   sqlc.PodpointPodLocation
	Model      sqlc.PodpointPodModel
	Group      sqlc.PodpointGroup
}

func populateNilClaimDataParams(params *ClaimChargeDataParams) {
	if params.AuthPK == nil {
		params.AuthPK = ptr.To(fixtures.Sequence.Get())
	}

	if params.UserUUID == nil {
		params.UserUUID = random.PopulateUUID(params.UserUUID)
	}
}
