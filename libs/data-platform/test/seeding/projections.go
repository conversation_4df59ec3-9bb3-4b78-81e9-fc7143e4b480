package seeding

import (
	"context"
	"database/sql"
	sqlccharges "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/data-platform/test/random"
	"strconv"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/google/uuid"
)

func ChargeProjections(ctx context.Context, t *testing.T, queries *sqlccharges.Queries, groupID, userID uuid.UUID, siteID uuid.NullUUID, chargerID sql.NullString, from time.Time) []*sqlccharges.ProjectionsCharge {
	t.Helper()

	numberOfProjections := gofakeit.IntRange(10, 60)
	projections := make([]*sqlccharges.ProjectionsCharge, numberOfProjections)

	for i := 0; i < numberOfProjections; i++ {
		chargeDuration := int32(gofakeit.IntRange(5000, 500000))
		energyCost := int32(gofakeit.IntRange(5000, 500000))
		settlementAmount := int32(gofakeit.IntRange(5000, 500000))
		projection := random.CreateProjectionsCharge(ctx, t, queries,
			&sqlccharges.CreateChargeProjectionParams{
				ChargerName:          sql.NullString{String: gofakeit.Name(), Valid: true},
				SiteName:             sql.NullString{String: gofakeit.Name(), Valid: true},
				ChargeUUID:           uuid.New(),
				GroupID:              uuid.NullUUID{UUID: groupID, Valid: true},
				SiteID:               siteID,
				PluggedInAt:          sql.NullTime{Time: from.Add(time.Duration(gofakeit.IntRange(-24, -1)) * time.Hour), Valid: true},
				UnpluggedAt:          sql.NullTime{Time: from.AddDate(0, 0, i).Add(time.Duration(gofakeit.IntRange(1, 59)) * time.Minute), Valid: true},
				ChargeDurationTotal:  sql.NullInt32{Int32: chargeDuration, Valid: true},
				EnergyTotal:          sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
				EnergyCost:           sql.NullInt32{Int32: energyCost, Valid: true},
				SettlementAmount:     sql.NullInt32{Int32: settlementAmount, Valid: true},
				Confirmed:            sql.NullBool{Bool: gofakeit.Bool(), Valid: true},
				ChargerID:            chargerID,
				UserIds:              []uuid.UUID{userID},
				RewardEligibleEnergy: "0.0",
			})
		projections[i] = projection
	}
	return projections
}
