package seeding

import (
	"context"
	"database/sql"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/sqlc"
	"experience/libs/shared/go/db"
	"testing"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Drivers seeds test data and returns the cleanup function.
// Typically used within a test to seed required data and defer the invocation of the returned cleanup function.
func Drivers(t *testing.T, queries *sqlc.Queries, driver *random.Driver, chargers []*random.Charger, user *uuid.UUID) func() {
	t.Helper()
	ctx := context.Background()

	_, err := queries.CreateAuthoriser(ctx, sqlc.CreateAuthoriserParams{
		ID:   driver.AuthoriserID,
		Uid:  driver.DriverID.String(),
		Type: "user",
	})
	assert.NoError(t, err)

	for _, charger := range chargers {
		var chargerName sql.NullString
		var businessName string
		var group *int64
		if *charger.Access != "home" {
			createOrganisation, err := queries.CreateOrganisation(ctx, fixtures.CreateOrganisationParams(fixtures.Sequence.Get(), uuid.NewString(), *charger.SiteName))
			assert.NoError(t, err)
			group = &createOrganisation.ID
			chargerName = sql.NullString{
				String: *charger.Name,
				Valid:  true,
			}
			businessName = *charger.SiteName
		}

		tariff, err := queries.CreateTariff(ctx, sqlc.CreateTariffParams{
			ID:       fixtures.Sequence.Get(),
			Currency: "GBP",
			Name:     t.Name(),
		})
		assert.NoError(t, err)

		_, err = queries.CreateTariffTier(ctx, sqlc.CreateTariffTierParams{
			ID:       fixtures.Sequence.Get(),
			TariffID: tariff.ID,
			Rate:     charger.Rate,
		})
		assert.NoError(t, err)

		address, err := queries.CreateAddress(ctx, sqlc.CreateAddressParams{
			ID:           fixtures.Sequence.Get(),
			BusinessName: businessName,
			GroupID:      nullableGroupID(group),
			TariffID:     sql.NullInt64{Int64: tariff.ID, Valid: true},
		})
		assert.NoError(t, err)

		unit, err := queries.CreateUnit(ctx, sqlc.CreateUnitParams{
			ID:   fixtures.Sequence.Get(),
			Ppid: charger.ID,
			Name: chargerName,
		})
		assert.NoError(t, err)

		isPublic := *charger.Access == "public"
		isHome := *charger.Access == "home"

		location, err := queries.CreateLocation(ctx, sqlc.CreateLocationParams{
			ID:        charger.Location,
			Uuid:      uuid.NewString(),
			AddressID: address.ID,
			IsPublic:  boolToInt(isPublic),
			IsHome:    boolToInt(isHome),
			UnitID: sql.NullInt64{
				Int64: unit.ID,
				Valid: true,
			},
			Timezone: sql.NullString{
				String: *charger.Timezone,
				Valid:  true,
			},
			Longitude:   "-0.02560169",
			Latitude:    "51.70536849",
			Geohash:     sql.NullInt64{Int64: 114719790754337000, Valid: true},
			PaygEnabled: 0,
		})
		assert.NoError(t, err)

		_, err = queries.CreateCharge(ctx, sqlc.CreateChargeParams{
			ID:              int64(charger.ChargePK),
			LocationID:      sql.NullInt64{Int64: location.ID, Valid: true},
			UnitID:          unit.ID,
			Door:            0,
			EnergyCost:      sql.NullInt32{},
			BillingEventID:  sql.NullInt64{},
			KwhUsed:         "20.01",
			Duration:        sql.NullInt64{},
			IsClosed:        0,
			GroupID:         sql.NullInt64{},
			ClaimedChargeID: sql.NullInt64{},
		},
		)
		require.NoError(t, err)
	}

	if user != nil {
		_, err := queries.CreateUser(ctx, sqlc.CreateUserParams{
			ID:         fixtures.Sequence.Get(),
			AuthID:     user.String(),
			GroupID:    sql.NullInt64{},
			RoleID:     0,
			Email:      "",
			Salutation: db.ToNullString(ptr.To("")),
			FirstName:  "",
			LastName:   "",
			Password:   db.ToNullString(ptr.To("")),
		})
		require.NoError(t, err)
	}

	return func() {
		t.Helper()
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)
	}
}

func nullableGroupID(group *int64) sql.NullInt64 {
	if group == nil {
		return sql.NullInt64{
			Valid: false,
		}
	}
	return sql.NullInt64{
		Valid: true,
		Int64: *group,
	}
}

func boolToInt(flag bool) int16 {
	if flag {
		return 1
	}
	return 0
}
