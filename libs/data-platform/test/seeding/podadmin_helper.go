//nolint:thelper // if the seeding fails, we want to know which seeding failed
package seeding

import (
	"context"
	"database/sql"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/sqlc"
	sqlcpodadmin "experience/libs/data-platform/test/sqlc-podadmin"
	"experience/libs/shared/go/numbers"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"k8s.io/utils/ptr"
)

type PodadminHelper struct {
	podadminQueries *sqlcpodadmin.Queries
	podadminDBState *fixtures.DBState
	isLocal         bool
	config          *PodadminHelperConfig
	seededIDs       PodadminHelperSeededIDs
	queries         *sqlc.Queries
}

func NewPodadminHelper(podadminQueries *sqlcpodadmin.Queries, podadminDBState *fixtures.DBState, isLocal bool, config *PodadminHelperConfig, queries *sqlc.Queries) *PodadminHelper {
	return &PodadminHelper{
		podadminQueries: podadminQueries,
		podadminDBState: podadminDBState,
		isLocal:         isLocal,
		config:          config,
		queries:         queries,
	}
}

type PodadminHelperSeededIDs struct {
	ChargeID         *int64
	ClaimedChargeID  *int64
	BillingAccountID *int64
	UserID           *int64
	RoleID           *int64
	AuthoriserID     *int64
	AuthoriserUID    *string
	BillingEventID   *int64
	UnitID           *int64
	LocationID       *int64
	PPID             string
}

type PodadminHelperConfig struct {
	BillingAccountBalance  *int32
	BillingAccountCurrency *string
	ClaimedCharge          bool
	AuthoriserUID          *string
	AuthoriserType         *string
	ReplicationSeeding     bool
	IsPublicLocation       bool
	ChargeStartsAt         *time.Time
	ChargeEndsAt           *time.Time
	AccountlessBilling     bool
	SeedBillingEvent       bool
}

func (h *PodadminHelper) ReturnSeededIDs() PodadminHelperSeededIDs {
	return h.seededIDs
}

func (h *PodadminHelper) CreateCharge(ctx context.Context, t *testing.T) int64 {
	var podadminBillingAccountID sql.NullInt64
	if !h.config.AccountlessBilling {
		podadminBillingAccountID = sql.NullInt64{Int64: h.createBillingAccount(ctx, t), Valid: true}
	}

	claimedChargeID := sql.NullInt32{Valid: false}
	if h.config.ClaimedCharge {
		authoriserID, _ := h.CreateAuthoriser(ctx, t)
		claimedCharge := int32(h.createClaimedCharge(ctx, t, podadminBillingAccountID, authoriserID))
		claimedChargeID = sql.NullInt32{Int32: claimedCharge, Valid: true}
	}

	var podadminBillingEvent sql.NullInt32
	if h.config.SeedBillingEvent {
		podadminBillingEvent = sql.NullInt32{Int32: int32(h.CreateBillingEvent(ctx, t)), Valid: true}
	}

	var billingAccountID sql.NullInt32
	if podadminBillingAccountID.Valid {
		billingAccountID = sql.NullInt32{Int32: int32(podadminBillingAccountID.Int64), Valid: true}
	}
	unitID := uint32(h.createUnit(ctx, t))
	chargeParams := &sqlcpodadmin.CreatePodadminChargeDefaultParams{
		UnitID:           unitID,
		BillingAccountID: billingAccountID,
		ClaimedChargeID:  claimedChargeID,
		BillingEventID:   podadminBillingEvent,
	}
	podadminChargeID, err := h.podadminQueries.InsertPodadminChargeReturning(ctx, chargeParams)
	assert.NoError(t, err)
	h.seededIDs.ChargeID = ptr.To(podadminChargeID)
	h.podadminDBState.AppendID(fixtures.Charges, podadminChargeID)

	if h.isLocal && h.config.ReplicationSeeding {
		h.createChargeReplication(ctx, t, podadminChargeID, unitID, podadminBillingAccountID)
	}

	return podadminChargeID
}

func (h *PodadminHelper) createChargeReplication(ctx context.Context, t *testing.T, chargeID int64, unitID uint32, billingAccountID sql.NullInt64) {
	convertedUnitID, err := numbers.Convert[uint32, int64](unitID)
	assert.NoError(t, err)

	var startsAt, endsAt sql.NullTime
	if h.config.ChargeStartsAt != nil {
		startsAt = sql.NullTime{Time: *h.config.ChargeStartsAt, Valid: true}
	}
	if h.config.ChargeEndsAt != nil {
		endsAt = sql.NullTime{Time: *h.config.ChargeEndsAt, Valid: true}
	}

	chargeParams := sqlc.CreateChargeParams{
		ID:               chargeID,
		UnitID:           convertedUnitID,
		EnergyCost:       sql.NullInt32{Int32: 0, Valid: true},
		StartsAt:         startsAt,
		EndsAt:           endsAt,
		KwhUsed:          "0",
		IsClosed:         1,
		BillingAccountID: billingAccountID,
	}

	_, err = h.queries.CreateCharge(ctx, chargeParams)
	assert.NoError(t, err)
}

func (h *PodadminHelper) createBillingAccount(ctx context.Context, t *testing.T) int64 {
	if h.seededIDs.BillingAccountID != nil {
		return *h.seededIDs.BillingAccountID
	}
	podadminUserID := int32(h.createUser(ctx, t))

	balance := int32(100)
	if h.config.BillingAccountBalance != nil {
		balance = *h.config.BillingAccountBalance
	}

	currency := "GBP"
	if h.config.BillingAccountCurrency != nil {
		currency = *h.config.BillingAccountCurrency
	}

	podadminBillingAccount, err := h.podadminQueries.InsertPodadminBillingAccountReturning(ctx, &sqlcpodadmin.CreatePodadminBillingAccountDefaultParams{
		UserID:   sql.NullInt32{Int32: podadminUserID, Valid: true},
		Uid:      uuid.NewString(),
		Balance:  balance,
		Currency: currency,
	})
	assert.NoError(t, err)
	h.seededIDs.BillingAccountID = ptr.To(podadminBillingAccount)
	h.podadminDBState.AppendID(fixtures.BillingAccounts, podadminBillingAccount)

	if h.isLocal && h.config.ReplicationSeeding {
		h.createBillingAccountReplication(ctx, t, podadminBillingAccount, podadminUserID, balance)
	}

	return podadminBillingAccount
}

func (h *PodadminHelper) createBillingAccountReplication(ctx context.Context, t *testing.T, billingAccountID int64, userID, balance int32) {
	convertedUserID, err := numbers.Convert[int32, int64](userID)
	assert.NoError(t, err)

	_, err = h.queries.CreateBillingAccount(ctx, sqlc.CreateBillingAccountParams{
		ID:       billingAccountID,
		UserID:   sql.NullInt64{Int64: convertedUserID, Valid: true},
		Balance:  balance,
		Currency: "GBP",
	})
	assert.NoError(t, err)
}

func (h *PodadminHelper) createUser(ctx context.Context, t *testing.T) int64 {
	if h.seededIDs.UserID != nil {
		return *h.seededIDs.UserID
	}
	roleID := uint32(1)
	if h.isLocal {
		_ = h.podadminQueries.CreatePodadminRole(ctx, roleID)
		h.podadminDBState.AppendID(fixtures.Roles, int64(roleID))
	}
	h.seededIDs.RoleID = ptr.To(int64(roleID))

	authoriserUID := h.seededIDs.AuthoriserUID
	if h.seededIDs.AuthoriserUID == nil {
		_, authUID := h.CreateAuthoriser(ctx, t)
		authoriserUID = &authUID
	}

	podadminUserParams := fixtures.CreatePodadminUserDefaultParams(
		*authoriserUID,
		nil,
		"<EMAIL>",
		"test",
		"user",
		roleID,
	)
	podadminUserID, err := h.podadminQueries.InsertPodadminUserReturning(ctx, &podadminUserParams)
	assert.NoError(t, err)
	h.seededIDs.UserID = ptr.To(podadminUserID)
	h.podadminDBState.AppendID(fixtures.Users, podadminUserID)

	if h.isLocal && h.config.ReplicationSeeding {
		h.createUserReplication(ctx, t, podadminUserID, authoriserUID, &podadminUserParams)
	}

	return podadminUserID
}

func (h *PodadminHelper) createUserReplication(ctx context.Context, t *testing.T, userID int64, authoriser *string, userParams *sqlcpodadmin.CreatePodadminUserDefaultParams) {
	var authoriserUID string
	if authoriser != nil {
		authoriserUID = *authoriser
	}

	convertedGroupID, err := numbers.Convert[int32, int64](userParams.GroupID.Int32)
	assert.NoError(t, err)

	_, err = h.queries.CreateUser(ctx,
		fixtures.CreateUserParams(
			userID,
			authoriserUID,
			sqlc.PodpointGroup{ID: convertedGroupID},
			userParams.Email,
			userParams.FirstName,
			userParams.LastName,
		),
	)
	assert.NoError(t, err)
}

func (h *PodadminHelper) CreateAuthoriser(ctx context.Context, t *testing.T) (authoriserID int64, authoriserUID string) {
	if h.seededIDs.AuthoriserID != nil && h.seededIDs.AuthoriserUID != nil {
		return *h.seededIDs.AuthoriserID, *h.seededIDs.AuthoriserUID
	}
	authoriserUID = uuid.NewString()
	if h.config.AuthoriserUID != nil {
		authoriserUID = *h.config.AuthoriserUID
	}

	authoriserType := "user"
	if h.config.AuthoriserType != nil {
		authoriserType = *h.config.AuthoriserType
	}

	authoriserID, err := h.podadminQueries.InsertPodadminAuthoriserReturning(ctx, fixtures.CreatePodadminAuthoriserDefaultParams(
		authoriserUID,
		authoriserType,
	))
	assert.NoError(t, err)
	h.seededIDs.AuthoriserID = ptr.To(authoriserID)
	h.seededIDs.AuthoriserUID = ptr.To(authoriserUID)
	h.podadminDBState.AppendID(fixtures.Authorisers, authoriserID)

	if h.isLocal && h.config.ReplicationSeeding {
		h.createAuthoriserReplication(ctx, t, authoriserID, authoriserUID, authoriserType)
	}

	return authoriserID, authoriserUID
}

func (h *PodadminHelper) createAuthoriserReplication(ctx context.Context, t *testing.T, authoriserID int64, authoriserUID, authoriserType string) {
	_, err := h.queries.CreateAuthoriser(ctx, fixtures.CreateAuthoriserParams(
		authoriserID,
		authoriserUID,
		authoriserType,
		nil,
	))
	assert.NoError(t, err)
}

func (h *PodadminHelper) createClaimedCharge(ctx context.Context, t *testing.T, billingAccountID sql.NullInt64, authoriserID int64) int64 {
	if h.seededIDs.ClaimedChargeID != nil {
		return *h.seededIDs.ClaimedChargeID
	}
	podadminDoorID := h.CreateDoor(ctx, t)

	var billingAccount sql.NullInt32
	if billingAccountID.Valid {
		billingAccount = sql.NullInt32{Int32: int32(billingAccountID.Int64), Valid: true}
	}

	podadminLocationID := uint32(h.CreateLocation(ctx, t))
	podadminClaimedChargeID, err := h.podadminQueries.InsertPodadminClaimedChargeReturning(ctx, &sqlcpodadmin.CreatePodadminClaimedChargeDefaultParams{
		PodDoorID:        podadminDoorID,
		BillingAccountID: billingAccount,
		PodLocationID:    podadminLocationID,
		AuthoriserID:     int32(authoriserID),
	})
	assert.NoError(t, err)
	h.seededIDs.ClaimedChargeID = ptr.To(podadminClaimedChargeID)

	return podadminClaimedChargeID
}

func (h *PodadminHelper) CreateDoor(ctx context.Context, t *testing.T) uint32 {
	podadminDoorID := uint32(1)
	if h.isLocal {
		err := h.podadminQueries.CreatePodadminDoor(ctx, fixtures.CreatePodadminDoorParams(podadminDoorID, "test_door"))
		h.podadminDBState.AppendID(fixtures.Doors, int64(podadminDoorID))
		assert.NoError(t, err)
	}

	return podadminDoorID
}

func (h *PodadminHelper) CreateLocation(ctx context.Context, t *testing.T) int64 {
	if h.seededIDs.LocationID != nil {
		return *h.seededIDs.LocationID
	}
	podadminAddressID := uint32(h.createAddress(ctx, t))
	podadminUnitID, err := numbers.Convert[int64, int32](h.createUnit(ctx, t))
	assert.NoError(t, err)

	params := &sqlcpodadmin.CreatePodadminLocationDefaultParams{
		AddressID: podadminAddressID,
		UnitID:    sql.NullInt32{Int32: podadminUnitID, Valid: true},
	}

	if h.config.IsPublicLocation {
		params.IsPublic = true
	}

	podadminLocationID, err := h.podadminQueries.InsertPodadminLocationReturning(ctx, params)
	assert.NoError(t, err)
	h.podadminDBState.AppendID(fixtures.Locations, podadminLocationID)
	h.seededIDs.LocationID = ptr.To(podadminLocationID)

	if h.isLocal && h.config.ReplicationSeeding {
		h.CreateLocationReplication(ctx, t, podadminLocationID, podadminAddressID, podadminUnitID)
	}

	return podadminLocationID
}

func (h *PodadminHelper) CreateLocationReplication(ctx context.Context, t *testing.T, podadminLocationID int64, podadminAddressID uint32, podadminUnitID int32) {
	convertedPodadminAddressID, err := numbers.Convert[uint32, int64](podadminAddressID)
	assert.NoError(t, err)

	convertedPodadminUnitID, err := numbers.Convert[int32, int64](podadminUnitID)
	assert.NoError(t, err)

	isHome := !h.config.IsPublicLocation

	_, err = h.queries.CreateLocation(
		ctx,
		fixtures.CreatePodPointLocationParams(
			&sqlc.PodpointPodAddress{ID: convertedPodadminAddressID},
			&sqlc.PodpointPodUnit{ID: convertedPodadminUnitID},
			isHome,
			false,
			podadminLocationID,
		),
	)
	assert.NoError(t, err)
}

func (h *PodadminHelper) createUnit(ctx context.Context, t *testing.T) int64 {
	if h.seededIDs.UnitID != nil {
		return *h.seededIDs.UnitID
	}
	chargerID := random.ChargerID()
	podadminUnitID, err := h.podadminQueries.InsertPodadminUnitReturning(ctx, chargerID)
	assert.NoError(t, err)
	h.podadminDBState.AppendID(fixtures.Units, podadminUnitID)
	h.seededIDs.UnitID = ptr.To(podadminUnitID)
	h.seededIDs.PPID = chargerID

	if h.isLocal && h.config.ReplicationSeeding {
		h.createUnitReplication(ctx, t, podadminUnitID, chargerID)
	}

	return podadminUnitID
}

func (h *PodadminHelper) createUnitReplication(ctx context.Context, t *testing.T, podadminUnitID int64, chargerID string) {
	unitParams := sqlc.CreateUnitParams{
		ID:      podadminUnitID,
		Ppid:    chargerID,
		ModelID: 1,
	}
	_, err := h.queries.CreateUnit(ctx, unitParams)
	assert.NoError(t, err)
}

func (h *PodadminHelper) createAddress(ctx context.Context, t *testing.T) int64 {
	podadminAddressType := uint32(1)
	if h.isLocal {
		_ = h.podadminQueries.CreatePodadminAddressType(ctx, podadminAddressType)
		h.podadminDBState.AppendID(fixtures.AddressTypes, int64(podadminAddressType))
	}

	podadminAddressID, err := h.podadminQueries.InsertPodadminAddressReturning(ctx, &sqlcpodadmin.CreatePodadminAddressDefaultParams{
		TypeID: podadminAddressType,
	})
	assert.NoError(t, err)
	h.podadminDBState.AppendID(fixtures.Addresses, podadminAddressID)

	return podadminAddressID
}

func (h *PodadminHelper) CreateBillingEvent(ctx context.Context, t *testing.T) int64 {
	if h.seededIDs.BillingEventID != nil {
		return *h.seededIDs.BillingEventID
	}
	billingEventID, err := h.podadminQueries.InsertPodadminBillingEventReturning(ctx, &sqlcpodadmin.CreatePodadminBillingEventDefaultParams{})
	assert.NoError(t, err)
	h.podadminDBState.AppendID(fixtures.BillingEvents, billingEventID)
	h.seededIDs.BillingEventID = ptr.To(billingEventID)

	if h.isLocal && h.config.ReplicationSeeding {
		h.createBillingEventReplication(ctx, t, billingEventID)
	}

	return billingEventID
}

func (h *PodadminHelper) createBillingEventReplication(ctx context.Context, t *testing.T, billingEventID int64) {
	_, err := h.queries.CreateBillingEvent(ctx, sqlc.CreateBillingEventParams{
		ID: billingEventID,
	})
	assert.NoError(t, err)
}
