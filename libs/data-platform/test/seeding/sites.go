package seeding

import (
	"context"
	"database/sql"
	sqlccharges "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/sqlc"
	"experience/libs/shared/go/numbers"
	sharedtest "experience/libs/shared/go/test"
	"strconv"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func Sites(ctx context.Context, t *testing.T, queries *sqlc.Queries, groupID uuid.UUID, siteIDs []int, drivers []*random.Driver, chargers []*random.Charger, authorisers []*random.Authoriser) func() {
	t.Helper()

	for _, driver := range drivers {
		_, err := queries.CreateAuthoriser(ctx, sqlc.CreateAuthoriserParams{
			ID:   driver.AuthoriserID,
			Uid:  driver.DriverID.String(),
			Type: "user",
		})
		assert.NoError(t, err)
	}

	for _, authoriser := range authorisers {
		_, err := queries.CreateAuthoriser(ctx, sqlc.CreateAuthoriserParams{
			ID:       authoriser.ID,
			Uid:      authoriser.UID,
			Type:     authoriser.Type,
			GroupUid: sql.NullString{String: authoriser.GroupUID, Valid: true},
		})
		assert.NoError(t, err)
	}

	createOrganisation, err := queries.CreateOrganisation(ctx, fixtures.CreateOrganisationParams(fixtures.Sequence.Get(), groupID.String(), "GroupName"))
	assert.NoError(t, err)

	for i, siteID := range siteIDs {
		address, err := queries.CreateAddress(ctx, sqlc.CreateAddressParams{
			ID:           int64(siteID),
			BusinessName: *chargers[i].SiteName,
			GroupID:      nullableGroupID(&createOrganisation.ID),
		})
		assert.NoError(t, err)

		unit, err := queries.CreateUnit(ctx, sqlc.CreateUnitParams{
			ID:   fixtures.Sequence.Get(),
			Ppid: chargers[i].ID,
			Name: sql.NullString{
				String: *chargers[i].Name,
				Valid:  true,
			},
		})
		assert.NoError(t, err)

		tierTimes := fixtures.TierTimes{
			BeginTime: sharedtest.MustParse(time.TimeOnly, "00:00:00"),
			EndTime:   sharedtest.MustParse(time.TimeOnly, "00:00:00"),
			BeginDay:  2,
			EndDay:    2,
		}
		revenueProfile, err := queries.CreateRevenueProfile(ctx, fixtures.CreateRevenueProfileParams(address.GroupID.Int64, "GBP", nil))
		assert.NoError(t, err)
		_, err = queries.CreateRevenueProfileTier(ctx, fixtures.CreateRevenueProfileTierParams(revenueProfile.ID, sqlc.UserTypePublic, FixedRate*10000, 0, sqlc.TermFixed, &tierTimes, nil))
		assert.NoError(t, err)

		_, err = queries.CreateLocation(ctx, sqlc.CreateLocationParams{
			ID:        chargers[i].Location,
			Uuid:      uuid.NewString(),
			AddressID: address.ID,
			IsPublic:  boolToInt(gofakeit.Bool()),
			IsHome:    0,
			UnitID: sql.NullInt64{
				Int64: unit.ID,
				Valid: true,
			},
			Timezone: sql.NullString{
				String: *chargers[i].Timezone,
				Valid:  true,
			},
			Longitude:   "-0.02560169",
			Latitude:    "51.70536849",
			Geohash:     sql.NullInt64{Int64: 114719790754337000, Valid: true},
			PaygEnabled: 0,
			RevenueProfileID: sql.NullInt64{
				Int64: revenueProfile.ID,
				Valid: true,
			},
		})
		assert.NoError(t, err)
	}

	return func() {
		t.Helper()
		fixtures.CleanupAllTestData(ctx, t, queries, nil, nil, nil)
	}
}

func ChargesToRetrieveBySite(ctx context.Context, t *testing.T, queries *sqlccharges.Queries, groupID, siteID uuid.UUID, from time.Time, siteName, groupName string, chargerType sqlccharges.NullAccess, settlementAmount *int) ([]sqlccharges.ProjectionsCharge, uuid.UUID, uuid.NullUUID) {
	t.Helper()

	energyTotal := sql.NullString{String: "1.1", Valid: true}
	settlementAmountInt32 := sql.NullInt32{Valid: false}
	if settlementAmount != nil {
		convertedSettlementAmount, err := numbers.Convert[int, int32](*settlementAmount)
		assert.NoError(t, err)
		settlementAmountInt32 = sql.NullInt32{Int32: convertedSettlementAmount, Valid: true}
		energyTotal = sql.NullString{String: "0.0", Valid: gofakeit.Bool()}
	}

	chargeInAnotherSite := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID: uuid.New(),
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			GroupID:              uuid.NullUUID{UUID: groupID, Valid: true},
			SiteID:               uuid.NullUUID{UUID: uuid.New(), Valid: true},
			UnpluggedAt:          sql.NullTime{Time: from.Add(4 * time.Hour), Valid: true},
			EnergyTotal:          energyTotal,
			SettlementAmount:     settlementAmountInt32,
			ChargerType:          chargerType,
			RewardEligibleEnergy: "0.0",
		})

	return chargeProjectionsWithEnergyAndInTimeRange(ctx, t, queries, groupID, siteID, from, siteName, groupName, chargerType), chargeInAnotherSite.ChargeUUID, chargeInAnotherSite.SiteID
}

func ChargesToRetrieveBySiteForChargeStatistics(ctx context.Context, t *testing.T, queries *sqlccharges.Queries, groupID, siteID uuid.UUID, from, to time.Time, siteName, groupName string, chargerType sqlccharges.NullAccess, settlementAmount *int) ([]sqlccharges.ProjectionsCharge, uuid.UUID, uuid.NullUUID) {
	t.Helper()

	energyTotal := sql.NullString{String: "1.1", Valid: true}
	settlementAmountInt32 := sql.NullInt32{Valid: false}
	if settlementAmount != nil {
		convertedSettlementAmount, err := numbers.Convert[int, int32](*settlementAmount)
		assert.NoError(t, err)
		settlementAmountInt32 = sql.NullInt32{Int32: convertedSettlementAmount, Valid: true}
		energyTotal = sql.NullString{String: "0.0", Valid: gofakeit.Bool()}
	}

	chargeInAnotherSite := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID: uuid.New(),
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			GroupID:              uuid.NullUUID{UUID: groupID, Valid: true},
			SiteID:               uuid.NullUUID{UUID: uuid.New(), Valid: true},
			UnpluggedAt:          sql.NullTime{Time: from.Add(4 * time.Hour), Valid: true},
			EnergyTotal:          energyTotal,
			SettlementAmount:     settlementAmountInt32,
			ChargerType:          chargerType,
			RewardEligibleEnergy: "0.0",
		})

	return chargeProjectionsWithEnergyAndInTimeRangeForChargeStatistics(ctx, t, queries, groupID, siteID, from, to, siteName, groupName, chargerType), chargeInAnotherSite.ChargeUUID, chargeInAnotherSite.SiteID
}

func ChargesToRetrieveBySiteWithNullValues(ctx context.Context, t *testing.T, queries *sqlccharges.Queries, groupID, siteID uuid.UUID, from time.Time, siteName, groupName string, chargerType sqlccharges.NullAccess, settlementAmount *int) ([]sqlccharges.ProjectionsCharge, uuid.UUID, uuid.NullUUID) {
	t.Helper()

	energyTotal := sql.NullString{String: "1.1", Valid: true}
	settlementAmountInt32 := sql.NullInt32{Valid: false}
	if settlementAmount != nil {
		convertedSettlementAmount, err := numbers.Convert[int, int32](*settlementAmount)
		assert.NoError(t, err)
		settlementAmountInt32 = sql.NullInt32{Int32: convertedSettlementAmount, Valid: true}
		energyTotal = sql.NullString{String: "0.0", Valid: gofakeit.Bool()}
	}

	chargeInAnotherSite := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID: uuid.New(),
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			GroupID:              uuid.NullUUID{UUID: groupID, Valid: true},
			SiteID:               uuid.NullUUID{UUID: uuid.New(), Valid: true},
			UnpluggedAt:          sql.NullTime{Time: from.Add(4 * time.Hour), Valid: true},
			EnergyTotal:          energyTotal,
			SettlementAmount:     settlementAmountInt32,
			ChargerType:          chargerType,
			RewardEligibleEnergy: "0.0",
		})

	recurringUser := uuid.New()

	chargeInTimeRangeWithNulls := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			Confirmed:        sql.NullBool{Bool: true, Valid: true},
			ChargeUUID:       uuid.New(),
			GroupID:          uuid.NullUUID{UUID: groupID, Valid: true},
			GroupName:        sql.NullString{String: groupName, Valid: true},
			SiteID:           uuid.NullUUID{UUID: siteID, Valid: true},
			SiteName:         sql.NullString{String: siteName, Valid: true},
			UnpluggedAt:      sql.NullTime{Time: time.Time{}, Valid: false},
			EnergyTotal:      sql.NullString{String: "", Valid: false},
			SettlementAmount: sql.NullInt32{Int32: 0, Valid: false},
			ChargerType:      chargerType,
			ChargerID: sql.NullString{
				String: "",
				Valid:  false,
			},
			UserIds:              []uuid.UUID{recurringUser},
			ChargeDurationTotal:  sql.NullInt32{Int32: 0, Valid: false},
			EnergyCost:           sql.NullInt32{Int32: 0, Valid: false},
			RewardEligibleEnergy: "0.0",
		})

	return []sqlccharges.ProjectionsCharge{
		*chargeInTimeRangeWithNulls,
	}, chargeInAnotherSite.ChargeUUID, chargeInAnotherSite.SiteID
}

func ChargesToRetrieveBySiteInTimeRange(ctx context.Context, t *testing.T, queries *sqlccharges.Queries, groupID, siteID uuid.UUID, from time.Time, siteName, groupName string, chargerType sqlccharges.NullAccess) []sqlccharges.ProjectionsCharge {
	t.Helper()

	random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID:           uuid.New(),
			GroupID:              uuid.NullUUID{UUID: groupID, Valid: true},
			SiteID:               uuid.NullUUID{UUID: siteID, Valid: true},
			UnpluggedAt:          sql.NullTime{Time: from.Add(-1 * time.Hour), Valid: true},
			EnergyTotal:          sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount:     sql.NullInt32{Int32: 10, Valid: true},
			ChargerType:          chargerType,
			RewardEligibleEnergy: "0.0",
		})

	random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID:           uuid.New(),
			GroupID:              uuid.NullUUID{UUID: groupID, Valid: true},
			SiteID:               uuid.NullUUID{UUID: siteID, Valid: true},
			UnpluggedAt:          sql.NullTime{Time: from.Add(25 * time.Hour), Valid: true},
			EnergyTotal:          sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount:     sql.NullInt32{Int32: 10, Valid: true},
			ChargerType:          chargerType,
			RewardEligibleEnergy: "0.0",
		})

	return chargeProjectionsWithEnergyAndInTimeRange(ctx, t, queries, groupID, siteID, from, siteName, groupName, chargerType)
}

func chargeProjectionsWithEnergyAndInTimeRange(ctx context.Context, t *testing.T, queries *sqlccharges.Queries, groupID, siteID uuid.UUID, from time.Time, siteName, groupName string, chargerType sqlccharges.NullAccess) []sqlccharges.ProjectionsCharge {
	t.Helper()

	recurringUser := uuid.New()

	chargeInTimeRange1 := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID:       uuid.New(),
			GroupID:          uuid.NullUUID{UUID: groupID, Valid: true},
			GroupName:        sql.NullString{String: groupName, Valid: true},
			SiteID:           uuid.NullUUID{UUID: siteID, Valid: true},
			SiteName:         sql.NullString{String: siteName, Valid: true},
			UnpluggedAt:      sql.NullTime{Time: from.Add(3 * time.Hour), Valid: true},
			EnergyTotal:      sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount: sql.NullInt32{Int32: 20, Valid: true},
			ChargerType:      chargerType,
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			UserIds:              []uuid.UUID{recurringUser},
			ChargeDurationTotal:  sql.NullInt32{Int32: random.ChargeDurationTotal(), Valid: true},
			EnergyCost:           sql.NullInt32{Int32: random.EnergyCost(), Valid: true},
			RewardEligibleEnergy: "0.0",
		})
	chargeInTimeRange2 := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID:       uuid.New(),
			GroupID:          uuid.NullUUID{UUID: groupID, Valid: true},
			GroupName:        sql.NullString{String: groupName, Valid: true},
			SiteID:           uuid.NullUUID{UUID: siteID, Valid: true},
			SiteName:         sql.NullString{String: siteName, Valid: true},
			UnpluggedAt:      sql.NullTime{Time: from.Add(4 * time.Hour), Valid: true},
			EnergyTotal:      sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount: sql.NullInt32{Int32: 19, Valid: true},
			ChargerType:      chargerType,
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			UserIds:              []uuid.UUID{recurringUser},
			ChargeDurationTotal:  sql.NullInt32{Int32: random.ChargeDurationTotal(), Valid: true},
			EnergyCost:           sql.NullInt32{Int32: random.EnergyCost(), Valid: true},
			RewardEligibleEnergy: "0.0",
		})
	chargeInTimeRangeNotRevenueGenerating := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			Confirmed:        sql.NullBool{Bool: true, Valid: true},
			ChargeUUID:       uuid.New(),
			GroupID:          uuid.NullUUID{UUID: groupID, Valid: true},
			GroupName:        sql.NullString{String: groupName, Valid: true},
			SiteID:           uuid.NullUUID{UUID: siteID, Valid: true},
			SiteName:         sql.NullString{String: siteName, Valid: true},
			UnpluggedAt:      sql.NullTime{Time: from.Add(4 * time.Hour), Valid: true},
			EnergyTotal:      sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount: sql.NullInt32{Int32: 0, Valid: true},
			ChargerType:      chargerType,
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			UserIds:              []uuid.UUID{uuid.New(), uuid.New()},
			ChargeDurationTotal:  sql.NullInt32{Int32: random.ChargeDurationTotal(), Valid: true},
			EnergyCost:           sql.NullInt32{Int32: random.EnergyCost(), Valid: true},
			RewardEligibleEnergy: "0.0",
		})
	chargeInTimeRangeNotConfirmed := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			Confirmed:        sql.NullBool{Bool: false, Valid: true},
			ChargeUUID:       uuid.New(),
			GroupID:          uuid.NullUUID{UUID: groupID, Valid: true},
			GroupName:        sql.NullString{String: groupName, Valid: true},
			SiteID:           uuid.NullUUID{UUID: siteID, Valid: true},
			SiteName:         sql.NullString{String: siteName, Valid: true},
			UnpluggedAt:      sql.NullTime{Time: from.Add(4 * time.Hour), Valid: true},
			EnergyTotal:      sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount: sql.NullInt32{Int32: 42, Valid: true},
			ChargerType:      chargerType,
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			UserIds:              []uuid.UUID{},
			ChargeDurationTotal:  sql.NullInt32{Int32: random.ChargeDurationTotal(), Valid: true},
			EnergyCost:           sql.NullInt32{Int32: random.EnergyCost(), Valid: true},
			RewardEligibleEnergy: "0.0",
		})
	chargeInTimeRangeNullConfirmed := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			Confirmed:        sql.NullBool{Bool: false, Valid: false},
			ChargeUUID:       uuid.New(),
			GroupID:          uuid.NullUUID{UUID: groupID, Valid: true},
			GroupName:        sql.NullString{String: groupName, Valid: true},
			SiteID:           uuid.NullUUID{UUID: siteID, Valid: true},
			SiteName:         sql.NullString{String: siteName, Valid: true},
			UnpluggedAt:      sql.NullTime{Time: from.Add(4 * time.Hour), Valid: true},
			EnergyTotal:      sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount: sql.NullInt32{Int32: 42, Valid: true},
			ChargerType:      chargerType,
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			UserIds:              []uuid.UUID{},
			ChargeDurationTotal:  sql.NullInt32{Int32: random.ChargeDurationTotal(), Valid: true},
			EnergyCost:           sql.NullInt32{Int32: random.EnergyCost(), Valid: true},
			RewardEligibleEnergy: "0.0",
		})

	return []sqlccharges.ProjectionsCharge{
		*chargeInTimeRange1,
		*chargeInTimeRange2,
		*chargeInTimeRangeNotRevenueGenerating,
		*chargeInTimeRangeNotConfirmed,
		*chargeInTimeRangeNullConfirmed,
	}
}

func chargeProjectionsWithEnergyAndInTimeRangeForChargeStatistics(ctx context.Context, t *testing.T, queries *sqlccharges.Queries, groupID, siteID uuid.UUID, from, to time.Time, siteName, groupName string, chargerType sqlccharges.NullAccess) []sqlccharges.ProjectionsCharge {
	t.Helper()

	recurringUser := uuid.New()

	chargeInTimeRange := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID:       uuid.New(),
			GroupID:          uuid.NullUUID{UUID: groupID, Valid: true},
			GroupName:        sql.NullString{String: groupName, Valid: true},
			SiteID:           uuid.NullUUID{UUID: siteID, Valid: true},
			SiteName:         sql.NullString{String: siteName, Valid: true},
			UnpluggedAt:      sql.NullTime{Time: from.Add(3 * time.Hour), Valid: true},
			EnergyTotal:      sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount: sql.NullInt32{Int32: 20, Valid: true},
			ChargerType:      chargerType,
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			UserIds:              []uuid.UUID{recurringUser},
			ChargeDurationTotal:  sql.NullInt32{Int32: random.ChargeDurationTotal(), Valid: true},
			EnergyCost:           sql.NullInt32{Int32: random.EnergyCost(), Valid: true},
			RewardEligibleEnergy: "0.0",
		})
	chargeAtToBoundary := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			ChargeUUID:       uuid.New(),
			GroupID:          uuid.NullUUID{UUID: groupID, Valid: true},
			GroupName:        sql.NullString{String: groupName, Valid: true},
			SiteID:           uuid.NullUUID{UUID: siteID, Valid: true},
			SiteName:         sql.NullString{String: siteName, Valid: true},
			UnpluggedAt:      sql.NullTime{Time: to.Add(1 * time.Hour), Valid: true},
			EnergyTotal:      sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount: sql.NullInt32{Int32: 19, Valid: true},
			ChargerType:      chargerType,
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			UserIds:              []uuid.UUID{recurringUser},
			ChargeDurationTotal:  sql.NullInt32{Int32: random.ChargeDurationTotal(), Valid: true},
			EnergyCost:           sql.NullInt32{Int32: random.EnergyCost(), Valid: true},
			RewardEligibleEnergy: "0.0",
		})
	chargeInTimeRangeNotRevenueGenerating := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			Confirmed:        sql.NullBool{Bool: true, Valid: true},
			ChargeUUID:       uuid.New(),
			GroupID:          uuid.NullUUID{UUID: groupID, Valid: true},
			GroupName:        sql.NullString{String: groupName, Valid: true},
			SiteID:           uuid.NullUUID{UUID: siteID, Valid: true},
			SiteName:         sql.NullString{String: siteName, Valid: true},
			UnpluggedAt:      sql.NullTime{Time: from.Add(4 * time.Hour), Valid: true},
			EnergyTotal:      sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount: sql.NullInt32{Int32: 0, Valid: true},
			ChargerType:      chargerType,
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			UserIds:              []uuid.UUID{uuid.New(), uuid.New()},
			ChargeDurationTotal:  sql.NullInt32{Int32: random.ChargeDurationTotal(), Valid: true},
			EnergyCost:           sql.NullInt32{Int32: random.EnergyCost(), Valid: true},
			RewardEligibleEnergy: "0.0",
		})
	chargeInTimeRangeNotConfirmed := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			Confirmed:        sql.NullBool{Bool: false, Valid: true},
			ChargeUUID:       uuid.New(),
			GroupID:          uuid.NullUUID{UUID: groupID, Valid: true},
			GroupName:        sql.NullString{String: groupName, Valid: true},
			SiteID:           uuid.NullUUID{UUID: siteID, Valid: true},
			SiteName:         sql.NullString{String: siteName, Valid: true},
			UnpluggedAt:      sql.NullTime{Time: from.Add(4 * time.Hour), Valid: true},
			EnergyTotal:      sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount: sql.NullInt32{Int32: 42, Valid: true},
			ChargerType:      chargerType,
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			UserIds:              []uuid.UUID{},
			ChargeDurationTotal:  sql.NullInt32{Int32: random.ChargeDurationTotal(), Valid: true},
			EnergyCost:           sql.NullInt32{Int32: random.EnergyCost(), Valid: true},
			RewardEligibleEnergy: "0.0",
		})
	chargeInTimeRangeNullConfirmed := random.CreateProjectionsCharge(ctx, t, queries,
		&sqlccharges.CreateChargeProjectionParams{
			Confirmed:        sql.NullBool{Bool: false, Valid: false},
			ChargeUUID:       uuid.New(),
			GroupID:          uuid.NullUUID{UUID: groupID, Valid: true},
			GroupName:        sql.NullString{String: groupName, Valid: true},
			SiteID:           uuid.NullUUID{UUID: siteID, Valid: true},
			SiteName:         sql.NullString{String: siteName, Valid: true},
			UnpluggedAt:      sql.NullTime{Time: from.Add(4 * time.Hour), Valid: true},
			EnergyTotal:      sql.NullString{String: strconv.FormatFloat(gofakeit.Float64Range(1.0, 999.99), 'g', 2, 64), Valid: true},
			SettlementAmount: sql.NullInt32{Int32: 42, Valid: true},
			ChargerType:      chargerType,
			ChargerID: sql.NullString{
				String: random.ChargerID(),
				Valid:  true,
			},
			UserIds:              []uuid.UUID{},
			ChargeDurationTotal:  sql.NullInt32{Int32: random.ChargeDurationTotal(), Valid: true},
			EnergyCost:           sql.NullInt32{Int32: random.EnergyCost(), Valid: true},
			RewardEligibleEnergy: "0.0",
		})

	return []sqlccharges.ProjectionsCharge{
		*chargeInTimeRange,
		*chargeAtToBoundary,
		*chargeInTimeRangeNotRevenueGenerating,
		*chargeInTimeRangeNotConfirmed,
		*chargeInTimeRangeNullConfirmed,
	}
}
