package billingauditor_test

import (
	"database/sql"
	"experience/libs/data-platform/event-sourcing/billingauditor"
	billingauditorinfra "experience/libs/data-platform/event-sourcing/billingauditor/infra"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/setup"
	"experience/libs/data-platform/test/sqlc"
	"experience/libs/shared/go/db/postgres/test"
	"fmt"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/google/uuid"
	"k8s.io/utils/ptr"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type RepositoryTestSuite struct {
	suite.Suite
	database     setup.PostgresTestDB
	queries      *sqlc.Queries
	underTest    billingauditor.AuditRepository
	location     sqlc.PodpointPodLocation
	unit         sqlc.PodpointPodUnit
	billingEvent sqlc.PodpointBillingEvent
	charge       sqlc.PodpointCharge
	db           *sql.DB
}

func TestRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}
	suite.Run(t, new(RepositoryTestSuite))
}

func (s *RepositoryTestSuite) SetupSuite() {
	s.database = setup.NewPostgresTestDB(s.T(), "file://../../../golang-migrate/migration", test.WithUser("xdp_async_processor"))
	s.queries = sqlc.New(s.database.DBHandleSqlx)
	s.underTest = billingauditorinfra.NewAuditableChargeRepository(s.database.ReadWriteDB.ReadDB, s.database.ReadWriteDB.WriteDB)
	s.db = s.database.DBHandle
}

func (s *RepositoryTestSuite) TearDownSuite() {
	require.NoError(s.T(), s.database.TestDB.Container.Terminate(s.T().Context()))
}

func (s *RepositoryTestSuite) SetupTest() {
	s.unit, _, s.location, _ = fixtures.PrepareChargeData(s.T().Context(), s.T(), s.queries, false, true)
	s.billingEvent, _ = s.queries.CreateBillingEvent(s.T().Context(), fixtures.CreateBillingEventParams(-6750))
}

func (s *RepositoryTestSuite) TearDownTest() {
	fixtures.CleanupAllTestData(s.T().Context(), s.T(), s.queries, nil, nil, nil)

	_, err := s.db.Exec("TRUNCATE TABLE projections.charges")
	s.NoError(err)
}

func (s *RepositoryTestSuite) TestNoChargeFound() {
	charge, err := s.underTest.FindByID(s.T().Context(), 0)
	require.ErrorIs(s.T(), err, billingauditor.ErrChargeNotFound)
	require.Nil(s.T(), charge)
}

func (s *RepositoryTestSuite) TestChargeIsNotFreeVendWhenPAYGIsTrue() {
	ctx := s.T().Context()

	s.charge, _ = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, s.location, s.unit, s.billingEvent, 150, "1.5", ptr.To(time.Now()), ptr.To(time.Now()), 0, 0, 1, 0))

	auditableCharge, err := s.underTest.FindByID(ctx, s.charge.ID)
	require.NoError(s.T(), err)
	require.NotNil(s.T(), *auditableCharge)
	require.Equal(s.T(), false, auditableCharge.IsFreeVend)
}

func (s *RepositoryTestSuite) TestChargeIsNotFreeVendWhenPAYGIsTrueAndRevenueSumGreaterThanZero() {
	ctx := s.T().Context()

	s.charge, _ = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, s.location, s.unit, s.billingEvent, 150, "1.5", ptr.To(time.Now()), ptr.To(time.Now()), 0, 0, 1, 0))

	auditableCharge, err := s.underTest.FindByID(ctx, s.charge.ID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), false, auditableCharge.IsFreeVend)
}

func (s *RepositoryTestSuite) TestChargeIsNotFreeVendWhenPAYGIsTrueAndRevenueSumIsZero() {
	ctx := s.T().Context()

	s.charge, _ = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, s.location, s.unit, s.billingEvent, 150, "1.5", ptr.To(time.Now()), ptr.To(time.Now()), 0, 0, 1, 0))

	auditableCharge, err := s.underTest.FindByID(ctx, s.charge.ID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), false, auditableCharge.IsFreeVend)
}

func (s *RepositoryTestSuite) TestChargeIsFreeVendWhenPAYGIsFalseAndRevenueSumIsZero() {
	ctx := s.T().Context()

	s.location, s.unit = fixtures.PrepareChargeDataWithRevenueProfiles(ctx, s.T(), s.queries, false, false, 0)
	s.charge, _ = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, s.location, s.unit, s.billingEvent, 150, "1.5", ptr.To(time.Now()), ptr.To(time.Now()), 0, 0, 1, 0))

	auditableCharge, err := s.underTest.FindByID(ctx, s.charge.ID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), true, auditableCharge.IsFreeVend)
}

func (s *RepositoryTestSuite) TestChargeIsHomeCharge() {
	ctx := s.T().Context()

	s.location, s.unit = fixtures.PrepareChargeDataWithRevenueProfiles(ctx, s.T(), s.queries, true, false, 0)
	s.charge, _ = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, s.location, s.unit, s.billingEvent, 150, "1.5", ptr.To(time.Now()), ptr.To(time.Now()), 0, 0, 1, 0))

	auditableCharge, err := s.underTest.FindByID(ctx, s.charge.ID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), true, auditableCharge.IsHome)
}

func (s *RepositoryTestSuite) TestChargeIsNotHomeCharge() {
	ctx := s.T().Context()

	s.charge, _ = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, s.location, s.unit, s.billingEvent, 150, "1.5", ptr.To(time.Now()), ptr.To(time.Now()), 0, 0, 1, 0))

	auditableCharge, err := s.underTest.FindByID(ctx, s.charge.ID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), false, auditableCharge.IsHome)
}

func (s *RepositoryTestSuite) TestChargeHasNoBillingEvent() {
	ctx := s.T().Context()

	s.charge, _ = s.queries.CreateCharge(ctx, fixtures.CreateChargeParamsNoBillingEvent(0, &s.location, &s.unit, 150, "1.5", ptr.To(time.Now()), ptr.To(time.Now())))

	auditableCharge, err := s.underTest.FindByID(ctx, s.charge.ID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), false, auditableCharge.HasBillingEvent)
}

func (s *RepositoryTestSuite) TestChargeHasBillingEvent() {
	ctx := s.T().Context()
	t := s.T()

	s.charge, _ = s.queries.CreateCharge(ctx, fixtures.CreateChargeParams(0, s.location, s.unit, s.billingEvent, 150, "1.5", ptr.To(time.Now()), ptr.To(time.Now()), 0, 0, 1, 0))

	auditableCharge, err := s.underTest.FindByID(ctx, s.charge.ID)
	require.NoError(t, err)
	require.Equal(t, true, auditableCharge.HasBillingEvent)
}

func (s *RepositoryTestSuite) TestChargeComesFromModelWhichSupportsOcppAsTrue() {
	ctx := s.T().Context()
	s.unit, _, s.location, _ = fixtures.PrepareChargeDataWithOcpp(ctx, s.T(), s.queries, true, true, 0, 1)
	s.charge, _ = s.queries.CreateCharge(ctx, fixtures.CreateChargeParamsNoBillingEvent(0, &s.location, &s.unit, 150, "1.5", ptr.To(time.Now()), ptr.To(time.Now())))
	auditableCharge, err := s.underTest.FindByID(ctx, s.charge.ID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), true, auditableCharge.SupportsOCPP)
}

func (s *RepositoryTestSuite) TestChargeComesFromModelWhichDoesNotSupportsOcppAsFalse() {
	ctx := s.T().Context()
	s.unit, _, s.location, _ = fixtures.PrepareChargeDataWithOcpp(ctx, s.T(), s.queries, true, true, 0, 0)
	s.charge, _ = s.queries.CreateCharge(ctx, fixtures.CreateChargeParamsNoBillingEvent(0, &s.location, &s.unit, 150, "1.5", ptr.To(time.Now()), ptr.To(time.Now())))
	auditableCharge, err := s.underTest.FindByID(ctx, s.charge.ID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), false, auditableCharge.SupportsOCPP)
}

func (s *RepositoryTestSuite) TestSaveAuditableCharge() {
	aggregateID := uuid.New()
	expected := anUnbilledCharge(aggregateID)
	actual, saveErr := s.underTest.SaveUnbilledCharge(s.T().Context(), expected)
	require.NoError(s.T(), saveErr)
	require.Equal(s.T(), expected, actual)
}

func (s *RepositoryTestSuite) TestSecondSaveUpdatesAuditableCharge() {
	aggregateID := uuid.New()

	expected := anUnbilledCharge(aggregateID)

	_, saveErr := s.underTest.SaveUnbilledCharge(s.T().Context(), expected)
	require.NoError(s.T(), saveErr)

	updated := anUnbilledCharge(aggregateID)
	expectedUpdate, err := s.underTest.SaveUnbilledCharge(s.T().Context(), updated)
	require.NoError(s.T(), err)
	require.Equal(s.T(), updated, expectedUpdate)
}

func (s *RepositoryTestSuite) TestFindAuditableChargeByAggregateID() {
	aggregateID := uuid.New()
	expected := anUnbilledCharge(aggregateID)

	actual, saveErr := s.underTest.SaveUnbilledCharge(s.T().Context(), expected)
	require.NoError(s.T(), saveErr)
	require.Equal(s.T(), expected, actual)

	actual, err := s.underTest.FindAuditableChargeByAggregateID(s.T().Context(), aggregateID)
	require.NoError(s.T(), err)
	require.Equal(s.T(), expected, actual)
}

func (s *RepositoryTestSuite) TestFindAuditableChargeByAggregateID_NotFound() {
	aggregateID := uuid.New()

	result, err := s.underTest.FindAuditableChargeByAggregateID(s.T().Context(), aggregateID)
	require.Error(s.T(), err)
	require.Nil(s.T(), result)
	require.ErrorContains(s.T(), err, "charge not found")
}

func anUnbilledCharge(aggregateID uuid.UUID) *billingauditor.UnbilledCharge {
	return &billingauditor.UnbilledCharge{
		AggregateID:       aggregateID,
		LocationID:        gofakeit.Int32(),
		ClaimChargeID:     gofakeit.Int32(),
		ChargingDuration:  gofakeit.Int32(),
		PluggedInDuration: gofakeit.Int32(),
		StartedAt:         ptr.To(gofakeit.Date().UTC().Round(time.Millisecond)),
		PluggedInAt:       gofakeit.Date().Round(time.Millisecond),
		Billed:            gofakeit.Bool(),
		EnergyKWh:         fmt.Sprintf("%.2f", gofakeit.Float64()),
		ChargeCycleID:     gofakeit.LetterN(10),
		Door:              gofakeit.RandomString([]string{"A", "B", "C"}),
		SupportsOCPP:      gofakeit.Bool(),
		GroupID:           ptr.To(uuid.New()),
		GroupName:         gofakeit.Company(),
		PPID:              random.ChargerID(),
		AuthoriserID:      gofakeit.Int32(),
		AuthoriserType:    gofakeit.RandomString([]string{"user", "app"}),
	}
}
