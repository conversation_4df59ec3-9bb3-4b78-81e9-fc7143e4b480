package chargecompleted_test

import (
	"experience/libs/data-platform/event-sourcing/chargecompleted"
	charge "experience/libs/data-platform/event-sourcing/domain/events/charges"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	"experience/libs/data-platform/test/random"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func TestToMessage(t *testing.T) {
	aggregateID := uuid.New()
	chargeRef := gofakeit.LetterN(10)
	chargeDurationTotal := nilable(ptr.To(gofakeit.Int32()))
	startedAt := nilable(ptr.To(gofakeit.Date()))
	endedAt := nilable(ptr.To(gofakeit.Date()))
	energyTotal := nilable(ptr.To(gofakeit.Float64Range(50.0, 150.0)))
	generationEnergyTotal := nilable(ptr.To(gofakeit.Float64Range(0.0, 100.0)))
	gridEnergyTotal := nilable(ptr.To(gofakeit.Float64Range(0.0, 100.0)))
	pluggedInAt := nilable(ptr.To(gofakeit.Date()))
	unpluggedAt := nilable(ptr.To(gofakeit.Date()))
	updatedAt := nilable(ptr.To(gofakeit.Date()))
	door := nilable(ptr.To(gofakeit.RandomString([]string{"A", "B", "C"})))
	chargerID := nilable(ptr.To(chargers.StationID(random.ChargerID())))
	energyCost := nilable(ptr.To(gofakeit.Int32()))
	currencyCode := nilable(ptr.To(gofakeit.Currency().Short))
	vehicleID := nilable(ptr.To(uuid.New()))

	aggregate := &charge.Aggregate{
		AggregateID:           aggregateID,
		Ref:                   chargeRef,
		ChargeDurationTotal:   chargeDurationTotal,
		StartedAt:             startedAt,
		EndedAt:               endedAt,
		EnergyTotal:           energyTotal,
		GenerationEnergyTotal: generationEnergyTotal,
		GridEnergyTotal:       gridEnergyTotal,
		PluggedInAt:           pluggedInAt,
		UnpluggedAt:           unpluggedAt,
		UpdatedAt:             updatedAt,
		Door:                  door,
		ChargerID:             chargerID,
		EnergyCost:            energyCost,
		EnergyCostCurrency:    currencyCode,
		VehicleID:             vehicleID,
	}

	confirmed := gofakeit.Bool()
	var (
		chargeAuthorisationID *uuid.UUID
		authoriserID          *string
		authoriserType        *string
	)
	if confirmed {
		chargeAuthorisationID = nilable(ptr.To(uuid.New()))
		authoriserID = nilable(ptr.To(uuid.NewString()))
		authoriserType = nilable(ptr.To(gofakeit.RandomString([]string{"app", "guest", "ocpi", "rfid", "user"})))

		aggregate.Confirmed = &confirmed
		aggregate.ChargeAuthorisationID = chargeAuthorisationID
		aggregate.AuthoriserID = authoriserID
		aggregate.AuthoriserType = authoriserType
	}

	rewardableEnergyAttributed := gofakeit.Bool()
	var rewardableEnergy *float32
	if rewardableEnergyAttributed {
		rewardableEnergy = nilable(ptr.To(gofakeit.Float32Range(0.0, 100.0)))

		aggregate.RewardableEnergyAttributed = true
		aggregate.RewardableEnergy = rewardableEnergy
	}

	billed := gofakeit.Bool()
	var (
		billingType        *string
		settlementAmount   *int32
		settlementCurrency *string
	)
	if billed == true {
		billingType = nilable(ptr.To(gofakeit.RandomString([]string{"guest", "ocpi", "wallet", "unsupported"})))
		settlementAmount = nilable(ptr.To(gofakeit.Int32()))
		settlementCurrency = nilable(ptr.To(gofakeit.Currency().Short))

		aggregate.Billed = true
		aggregate.BillingType = billingType
		aggregate.SettlementAmount = settlementAmount
		aggregate.SettlementCurrency = settlementCurrency
	}

	// Set the current time for the message
	now := gofakeit.Date()

	nowFunc := func() time.Time {
		return now
	}
	msg, err := chargecompleted.ToMessage(aggregate, nowFunc)
	require.NoError(t, err)

	assert.NotNil(t, msg.Charge)
	assert.Equal(t, ptr.To(aggregateID.String()), msg.Charge.Id)
	assert.Equal(t, chargeRef, *msg.Charge.Ref)
	assert.Equal(t, chargeDurationTotal, msg.Charge.ChargeDurationTotal)
	assert.Equal(t, startedAt, msg.Charge.StartedAt)
	assert.Equal(t, endedAt, msg.Charge.EndedAt)
	assert.Equal(t, energyTotal, msg.Charge.EnergyTotal)
	assert.Equal(t, generationEnergyTotal, msg.Charge.GenerationEnergyTotal)
	assert.Equal(t, gridEnergyTotal, msg.Charge.GridEnergyTotal)
	assert.Equal(t, pluggedInAt, msg.Charge.PluggedInAt)
	assert.Equal(t, unpluggedAt, msg.Charge.UnpluggedAt)
	assert.Equal(t, updatedAt, msg.Charge.UpdatedAt)

	assert.NotNil(t, msg.ChargingStation)
	assert.Equal(t, door, msg.ChargingStation.DoorId)
	stationIDAsStringPointer := chargecompleted.NilableStationIDAsStringPointer(chargerID)
	assert.Equal(t, stationIDAsStringPointer, msg.ChargingStation.Id)

	assert.NotNil(t, msg.Energy)
	asInt64, err := chargecompleted.NilableInt32AsInt64(energyCost)
	require.NoError(t, err)
	assert.Equal(t, asInt64, msg.Energy.Cost)
	assert.Equal(t, currencyCode, msg.Energy.CostCurrency)

	if confirmed {
		assert.NotNil(t, msg.Authorisation)
		assert.Equal(t, chargecompleted.NilableUUIDToStringPointer(chargeAuthorisationID), msg.Authorisation.Id)
		assert.Equal(t, authoriserID, msg.Authorisation.AuthoriserId)
		assert.Equal(t, authoriserType, msg.Authorisation.Type)
	} else {
		assert.Nil(t, msg.Authorisation)
	}

	if rewardableEnergyAttributed {
		assert.NotNil(t, msg.Rewards)
		assert.Equal(t, rewardableEnergy, msg.Rewards.EligibleEnergy)
		assert.Equal(t, chargecompleted.NilableUUIDToStringPointer(vehicleID), msg.Rewards.VehicleId)
	} else {
		assert.Nil(t, msg.Rewards)
	}

	if billed {
		assert.NotNil(t, msg.Revenue)
		assert.Equal(t, billingType, msg.Revenue.BillingType)
		assert.Equal(t, settlementAmount, msg.Revenue.SettlementAmount)
		assert.Equal(t, settlementCurrency, msg.Revenue.SettlementCurrency)
	} else {
		assert.Nil(t, msg.Revenue)
	}
	assert.NotNil(t, msg.PublishedAt)
	assert.Equal(t, now, *msg.PublishedAt)
}

func nilable[T any](v *T) *T {
	if gofakeit.Bool() {
		return v
	}
	return nil
}
