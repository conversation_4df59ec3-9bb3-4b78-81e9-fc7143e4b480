package api_test

import (
	"bytes"
	"encoding/json"
	contract "experience/libs/data-platform/api/contract/gen/drivers"
	"experience/libs/data-platform/api/contract/gen/http/drivers/server"
	"experience/libs/data-platform/event-sourcing/projection/drivers"
	"experience/libs/data-platform/event-sourcing/projection/drivers/api"
	mock_drivers "experience/libs/data-platform/event-sourcing/projection/drivers/mock"
	"experience/libs/data-platform/test/random"
	httphelper "experience/libs/shared/go/http"
	"experience/libs/shared/go/http/goaerror"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
	"github.com/ikawaha/goahttpcheck"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

const (
	from = "2023-10-09"
	to   = "2023-10-10"
)

type rcFixture struct {
	driverID     uuid.UUID
	chargeID     uuid.UUID
	mockResponse *drivers.Charge
	wantResponse *contract.DriversChargeResponse
}

func newRetrieveChargeFixture(t *testing.T) rcFixture {
	t.Helper()

	driverID := uuid.New()
	mr := random.CreateCharge(&driverID, nil, nil)
	wr, err := api.ToAPICharge(&mr)
	if err != nil {
		require.NoError(t, err)
	}

	driversChargeResponse := &contract.DriversChargeResponse{
		Data: wr,
		Meta: (*contract.Meta)(httphelper.PopulateMetaParams(contract.RetrieveChargePayload{
			DriverID: driverID.String(),
			ChargeID: mr.ID().String(),
		})),
	}

	return rcFixture{
		driverID:     driverID,
		chargeID:     mr.ID(),
		mockResponse: &mr,
		wantResponse: driversChargeResponse,
	}
}

func Test_driversSvc_RetrieveCharge(t *testing.T) {
	fixture := newRetrieveChargeFixture(t)

	var checker = goahttpcheck.New(goahttpcheck.Formatter(goaerror.FormatError))
	mockService := mock_drivers.NewMockService(gomock.NewController(t))
	checker.Mount(
		server.NewRetrieveChargeHandler,
		server.MountRetrieveChargeHandler,
		contract.NewRetrieveChargeEndpoint(api.NewService(mockService, httphelper.NewErrorMapper(api.ErrorMap))))

	mockService.EXPECT().RetrieveCharge(gomock.Any(), gomock.Eq(fixture.driverID), gomock.Eq(fixture.chargeID)).Return(fixture.mockResponse, nil).Times(1)

	checker.Test(t, http.MethodGet, pathToRetrieveChargeUUID(fixture.driverID, fixture.chargeID)).
		Check().
		HasStatus(http.StatusOK).
		Cb(func(r *http.Response) {
			b, _ := io.ReadAll(r.Body)
			var response contract.DriversChargeResponse
			if r.StatusCode == http.StatusOK {
				if err := json.Unmarshal(b, &response); err != nil {
					t.Fatalf("unexpected error, %v", err)
				}
			}
			assert.Equal(t, fixture.wantResponse, &response)
		})
}

func Test_driversSvc_RetrieveCharge_BadRequest(t *testing.T) {
	tests := []struct {
		name     string
		driverID string
		chargeID string
	}{
		{
			name:     "driver ID is invalid",
			driverID: "not valid",
			chargeID: uuid.New().String(),
		},
		{
			name:     "charge ID is invalid",
			driverID: uuid.New().String(),
			chargeID: "1234",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var checker = goahttpcheck.New()
			mockService := mock_drivers.NewMockService(gomock.NewController(t))
			checker.Mount(
				server.NewRetrieveChargeHandler,
				server.MountRetrieveChargeHandler,
				contract.NewRetrieveChargeEndpoint(api.NewService(mockService, httphelper.NewErrorMapper(api.ErrorMap))))

			mockService.EXPECT().RetrieveCharge(gomock.Any(), gomock.Any(), gomock.Any()).Times(0)

			checker.Test(t, http.MethodGet, pathToRetrieveCharge(tt.driverID, tt.chargeID)).
				Check().
				HasStatus(http.StatusBadRequest)
		})
	}
}

func Test_driversSvc_RetrieveCharge_NotFound(t *testing.T) {
	type errorResponse struct {
		Reason string `json:"reason,omitempty"`
		Status int    `json:"status,omitempty"`
	}
	tests := []struct {
		name            string
		mockResponseErr error
		want            errorResponse
	}{
		{
			name:            "no charge found",
			mockResponseErr: drivers.ErrChargeNotFound,
			want: errorResponse{
				Reason: "charge_not_found",
				Status: http.StatusNotFound,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var checker = goahttpcheck.New()
			mockService := mock_drivers.NewMockService(gomock.NewController(t))
			checker.Mount(
				server.NewRetrieveChargeHandler,
				server.MountRetrieveChargeHandler,
				contract.NewRetrieveChargeEndpoint(api.NewService(mockService, httphelper.NewErrorMapper(api.ErrorMap))))

			mockService.EXPECT().RetrieveCharge(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, tt.mockResponseErr).Times(1)
			b := new(bytes.Buffer)
			err := json.NewEncoder(b).Encode(tt.want)
			require.NoError(t, err)

			checker.Test(t, http.MethodGet, pathToRetrieveChargeUUID(uuid.New(), uuid.New())).
				Check().
				HasStatus(http.StatusNotFound).
				HasBody(b.Bytes())
		})
	}
}

func Test_driversSvc_RetrieveCharges_BadRequest(t *testing.T) {
	tests := []struct {
		name       string
		path       string
		wantStatus int
	}{
		{
			name:       "invalid driver id given",
			path:       fmt.Sprintf("/drivers/%s/charges", "invalid"),
			wantStatus: http.StatusBadRequest,
		},
		{
			name:       "no date parameters given (from, to)",
			path:       fmt.Sprintf("/drivers/%s/charges", uuid.New().String()),
			wantStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var checker = goahttpcheck.New()
			mockService := mock_drivers.NewMockService(gomock.NewController(t))
			checker.Mount(
				server.NewRetrieveChargesHandler,
				server.MountRetrieveChargesHandler,
				contract.NewRetrieveChargesEndpoint(api.NewService(mockService, httphelper.NewErrorMapper(api.ErrorMap))))

			mockService.EXPECT().RetrieveCharges(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(0)

			checker.Test(t, http.MethodGet, tt.path).
				Check().
				HasStatus(http.StatusBadRequest)
		})
	}
}

type rcsFixture struct {
	name         string
	driverID     uuid.UUID
	path         string
	mockResponse []drivers.Charge
	wantResponse *contract.DriversChargesResponse
}

func newRetrieveChargesFixture(t *testing.T, name string, count uint) rcsFixture {
	t.Helper()

	driverID := uuid.New()
	path := fmt.Sprintf("/drivers/%s/charges?from=%s&to=%s", driverID, from, to)
	mr := random.TimesCreateCharges(count, &driverID)
	wr, err := api.ToAPICharges(mr)
	if err != nil {
		require.NoError(t, err)
	}
	return rcsFixture{
		name:         name,
		driverID:     driverID,
		path:         path,
		mockResponse: mr,
		wantResponse: &contract.DriversChargesResponse{
			Data: &contract.Charges{
				Count:   len(wr),
				Charges: wr,
			},
			Meta: (*contract.Meta)(httphelper.PopulateMetaParams(contract.RetrieveChargesPayload{
				DriverID: driverID.String(),
				From:     from,
				To:       to,
			})),
		},
	}
}

func Test_driversSvc_RetrieveCharges(t *testing.T) {
	tests := []rcsFixture{
		newRetrieveChargesFixture(t, "no charges", 0),
		newRetrieveChargesFixture(t, "single charge", 1),
		newRetrieveChargesFixture(t, "multiple charges", random.TwoTo(100)),
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var checker = goahttpcheck.New()
			mockService := mock_drivers.NewMockService(gomock.NewController(t))
			checker.Mount(
				server.NewRetrieveChargesHandler,
				server.MountRetrieveChargesHandler,
				contract.NewRetrieveChargesEndpoint(api.NewService(mockService, httphelper.NewErrorMapper(api.ErrorMap))))

			mockService.EXPECT().RetrieveCharges(gomock.Any(), gomock.Eq(tt.driverID), gomock.Any(), gomock.Any()).Return(tt.mockResponse, nil).Times(1)

			checker.Test(t, http.MethodGet, tt.path).
				Check().
				HasStatus(http.StatusOK).
				Cb(func(r *http.Response) {
					b, _ := io.ReadAll(r.Body)
					var response contract.DriversChargesResponse
					if r.StatusCode == http.StatusOK {
						if err := json.Unmarshal(b, &response); err != nil {
							t.Fatalf("unexpected error, %v", err)
						}
					}
					assert.Equal(t, tt.wantResponse, &response)
				})
		})
	}
}

type statsFixture struct {
	name                  string
	driverID              uuid.UUID
	interval              *string
	path                  string
	mockResponse          drivers.StatsSummary
	mockResponseIntervals []*drivers.IntervalStats
	wantResponse          *contract.DriverStatsResponse
}

func newRetrieveStatsFixture(t *testing.T, name, from, to string, interval *string) statsFixture {
	t.Helper()

	driverID := uuid.New()
	path := fmt.Sprintf("/drivers/%s/stats?from=%s&to=%s", driverID, from, to)
	if interval != nil {
		path = fmt.Sprintf("%s&interval=%s", path, *interval)
	}

	mr := random.CreateAPIStatsSummary()
	var mir []*drivers.IntervalStats
	if interval != nil {
		f, _ := time.Parse(time.DateOnly, from)
		t, _ := time.Parse(time.DateOnly, to)
		mir = random.CreateIntervals(f, t, &mr)
	}
	wr, err := api.ToAPIStatsResponse(&mr, mir)
	if err != nil {
		require.NoError(t, err)
	}

	if interval == nil {
		wr.Meta = (*contract.Meta)(httphelper.PopulateMetaParams(contract.RetrieveStatsPayload{
			DriverID: driverID.String(),
			From:     from,
			To:       to,
		}))
	} else {
		wr.Meta = (*contract.Meta)(httphelper.PopulateMetaParams(contract.RetrieveStatsPayload{
			DriverID: driverID.String(),
			From:     from,
			To:       to,
			Interval: interval,
		}))
	}

	return statsFixture{
		name:                  name,
		driverID:              driverID,
		interval:              interval,
		path:                  path,
		mockResponse:          mr,
		mockResponseIntervals: mir,
		wantResponse:          wr,
	}
}

func Test_driversSvc_RetrieveStats(t *testing.T) {
	tests := []statsFixture{
		newRetrieveStatsFixture(t, "random stats", from, to, nil),
		newRetrieveStatsFixture(t, "random stats, with day interval", from, to, ptr.To("day")),
		newRetrieveStatsFixture(t, "random stats, with week interval", "2023-11-09", "2023-11-16", ptr.To("week")),
		newRetrieveStatsFixture(t, "random stats, with month interval", from, to, ptr.To("month")),
	}

	for i := range tests {
		tt := tests[i]
		t.Run(tt.name, func(t *testing.T) {
			var checker = goahttpcheck.New()
			mockService := mock_drivers.NewMockService(gomock.NewController(t))
			checker.Mount(
				server.NewRetrieveStatsHandler,
				server.MountRetrieveStatsHandler,
				contract.NewRetrieveStatsEndpoint(api.NewService(mockService, httphelper.NewErrorMapper(api.ErrorMap))))

			mockService.EXPECT().RetrieveStats(gomock.Any(), gomock.Eq(tt.driverID), gomock.Any(), gomock.Any(), gomock.Eq(tt.interval)).
				Return(&tt.mockResponse, tt.mockResponseIntervals, nil).
				Times(1)

			checker.Test(t, http.MethodGet, tt.path).
				Check().
				HasStatus(http.StatusOK).
				Cb(func(r *http.Response) {
					b, _ := io.ReadAll(r.Body)
					var response contract.DriverStatsResponse
					if r.StatusCode == http.StatusOK {
						if err := json.Unmarshal(b, &response); err != nil {
							t.Fatalf("unexpected error, %v", err)
						}
					}
					assert.Equal(t, tt.wantResponse, &response)
				})
		})
	}
}

func Test_driversSvc_RetrieveStats_Error(t *testing.T) {
	type errorResponse struct {
		Reason string `json:"reason,omitempty"`
		Status int    `json:"status,omitempty"`
	}

	tests := []struct {
		name             string
		path             string
		mockResponseErr  error
		mockServiceCalls int
		want             errorResponse
		wantStatus       int
		wantBody         *string
	}{
		{
			name:             "invalid from date format",
			path:             fmt.Sprintf("/drivers/%s/stats?from=%s&to=%s", uuid.New().String(), "invalid date", to),
			mockServiceCalls: 0,
			wantStatus:       http.StatusBadRequest,
		},
		{
			name:             "invalid to date format",
			path:             fmt.Sprintf("/drivers/%s/stats?from=%s&to=%s", uuid.New().String(), from, "invalid date"),
			mockServiceCalls: 0,
			wantStatus:       http.StatusBadRequest,
		},
		{
			name:             "from date as datetime fails validation",
			path:             fmt.Sprintf("/drivers/%s/stats?from=%s&to=%s", uuid.New().String(), from+" 12:34:56", to),
			mockServiceCalls: 0,
			wantStatus:       http.StatusBadRequest,
		},
		{
			name:             "to date as datetime fails validation",
			path:             fmt.Sprintf("/drivers/%s/stats?from=%s&to=%s", uuid.New().String(), from, to+" 12:34:56"),
			mockServiceCalls: 0,
			wantStatus:       http.StatusBadRequest,
		},
		{
			name:             "invalid interval",
			path:             fmt.Sprintf("/drivers/%s/stats?from=%s&to=%s&interval=%s", uuid.New().String(), from, to, "biweekly"),
			mockServiceCalls: 0,
			wantStatus:       http.StatusBadRequest,
			wantBody:         ptr.To("value of interval must be one of \\\"day\\\", \\\"week\\\", \\\"month\\\" but got value \\\"biweekly\\\""),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var checker = goahttpcheck.New()
			var err error
			mockService := mock_drivers.NewMockService(gomock.NewController(t))
			checker.Mount(
				server.NewRetrieveStatsHandler,
				server.MountRetrieveStatsHandler,
				contract.NewRetrieveStatsEndpoint(api.NewService(mockService, httphelper.NewErrorMapper(api.ErrorMap))))

			mockService.EXPECT().RetrieveStats(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil, nil, tt.mockResponseErr).
				Times(tt.mockServiceCalls)

			b := new(bytes.Buffer)

			if tt.wantStatus == http.StatusNotFound {
				err = json.NewEncoder(b).Encode(tt.want)
			}
			if tt.wantBody != nil {
				b.WriteString(*tt.wantBody)
			}
			if tt.wantStatus == http.StatusBadRequest && tt.wantBody == nil {
				b.WriteString("400 Bad Request")
			}

			require.NoError(t, err)

			checker.Test(t, http.MethodGet, tt.path).
				Check().
				HasStatus(tt.wantStatus).
				ContainsBody(b.Bytes())
		})
	}
}

func pathToRetrieveChargeUUID(driverID, chargeID uuid.UUID) string {
	return pathToRetrieveCharge(driverID.String(), chargeID.String())
}

func pathToRetrieveCharge(driverID, chargeID string) string {
	return fmt.Sprintf("/drivers/%s/charges/%s", driverID, chargeID)
}
