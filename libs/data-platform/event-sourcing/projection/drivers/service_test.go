package drivers_test

import (
	"context"
	"database/sql"
	"errors"
	"experience/libs/data-platform/event-sourcing/projection/drivers"
	mock_drivers "experience/libs/data-platform/event-sourcing/projection/drivers/mock"
	"experience/libs/data-platform/test/random"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func TestRetrieveCharge(t *testing.T) {
	ctx := context.Background()
	chargeID := uuid.New()
	driverID := uuid.New()
	want := random.CreateCharge(&driverID, &chargeID, nil)
	mockRepository, s := setupFixtures(t)

	mockRepository.EXPECT().FindByChargeIDUserID(gomock.Eq(ctx), gomock.Eq(chargeID), gomock.Eq(driverID)).Times(1).Return(&want, nil)

	got, err := s.RetrieveCharge(ctx, driverID, chargeID)

	require.NoError(t, err)
	require.NotNil(t, got)
	require.Equal(t, want, *got)
}

func TestRetrieveCharge_NotFound_ErrChargeNotFound(t *testing.T) {
	ctx := context.Background()
	chargeID := uuid.New()
	driverID := uuid.New()
	mockRepository, s := setupFixtures(t)

	mockRepository.EXPECT().FindByChargeIDUserID(gomock.Eq(ctx), gomock.Eq(chargeID), gomock.Eq(driverID)).Times(1).Return(nil, nil)

	got, err := s.RetrieveCharge(ctx, driverID, chargeID)

	require.Nil(t, got)
	require.Error(t, err)
	require.Equal(t, drivers.ErrChargeNotFound, err)
}

func TestRetrieveCharge_ErrorUnhandled(t *testing.T) {
	ctx := context.Background()
	chargeID := uuid.New()
	driverID := uuid.New()
	wantError := sql.ErrConnDone
	mockRepository, s := setupFixtures(t)

	mockRepository.EXPECT().FindByChargeIDUserID(gomock.Eq(ctx), gomock.Eq(chargeID), gomock.Eq(driverID)).Times(1).Return(nil, wantError)

	charge, err := s.RetrieveCharge(ctx, driverID, chargeID)

	require.Nil(t, charge)
	require.NotNil(t, err)
	require.Equal(t, wantError, err)
}

func TestRetrieveCharges(t *testing.T) {
	ctx := context.Background()
	driverID := uuid.New()
	want := random.TimesCreateCharges(random.OneTo(10), &driverID)
	mockRepository, s := setupFixtures(t)

	mockRepository.EXPECT().RetrieveByUserID(gomock.Eq(ctx), gomock.Eq(driverID), gomock.Any(), gomock.Any()).Times(1).Return(want, nil)

	got, err := s.RetrieveCharges(ctx, driverID, time.Now(), time.Now())

	require.NoError(t, err)
	require.NotNil(t, got)
	require.Equal(t, len(want), len(got))
	require.Equal(t, want, got)
}

func TestRetrieveCharges_Error(t *testing.T) {
	ctx := context.Background()
	driverID := uuid.New()
	wantError := sql.ErrConnDone
	mockRepository, s := setupFixtures(t)

	mockRepository.EXPECT().RetrieveByUserID(gomock.Eq(ctx), gomock.Eq(driverID), gomock.Any(), gomock.Any()).Times(1).Return(nil, wantError)

	charge, err := s.RetrieveCharges(ctx, driverID, time.Now(), time.Now())

	require.Nil(t, charge)
	require.NotNil(t, err)
	require.Equal(t, wantError, err)
}

func TestRetrieveCharges_ErrUnhandled(t *testing.T) {
	ctx := context.Background()
	driverID := uuid.New()
	wantError := errors.New("an error")
	mockRepository, s := setupFixtures(t)

	mockRepository.EXPECT().RetrieveByUserID(gomock.Eq(ctx), gomock.Eq(driverID), gomock.Any(), gomock.Any()).Times(1).Return(nil, wantError)

	charge, err := s.RetrieveCharges(ctx, driverID, time.Now(), time.Now())

	require.Nil(t, charge)
	require.NotNil(t, err)
	require.Equal(t, wantError, err)
}

func TestRetrieveStats(t *testing.T) {
	ctx := context.Background()
	driverID := uuid.New()
	want := ptr.To(random.CreateAPIStatsSummary())
	mockRepository, s := setupFixtures(t)

	mockRepository.EXPECT().RetrieveSummary(gomock.Eq(ctx), gomock.Eq(driverID), gomock.Any(), gomock.Any()).Times(1).Return(want, nil)

	got, _, err := s.RetrieveStats(ctx, driverID, time.Now(), time.Now(), nil)

	require.NoError(t, err)
	require.NotNil(t, got)
	require.Equal(t, want, got)
}

func TestRetrieveStats_WithIntervals(t *testing.T) {
	ctx := context.Background()
	driverID := uuid.New()
	from := time.Now()
	to := from.Add(time.Hour * 24 * 5)
	interval := "week"
	want := ptr.To(random.CreateAPIStatsSummary())
	wantIntervals := random.CreateIntervals(from, to, want)
	mockRepository, s := setupFixtures(t)

	mockRepository.EXPECT().RetrieveSummary(gomock.Eq(ctx), gomock.Eq(driverID), gomock.Eq(from), gomock.Eq(to)).Times(1).Return(want, nil)
	mockRepository.EXPECT().RetrieveSummariesByInterval(gomock.Eq(ctx), gomock.Eq(driverID), gomock.Eq(interval), gomock.Eq(from), gomock.Eq(to)).Times(1).Return(wantIntervals, nil)

	got, gotIntervals, err := s.RetrieveStats(ctx, driverID, from, to, ptr.To(interval))

	require.NoError(t, err)
	require.NotNil(t, got)
	require.NotNil(t, gotIntervals)
	require.Equal(t, want, got)
	require.Equal(t, wantIntervals, gotIntervals)
}

func TestRetrieveStats_ErrorUnhandled(t *testing.T) {
	ctx := context.Background()
	driverID := uuid.New()
	wantError := sql.ErrConnDone
	mockRepository, s := setupFixtures(t)

	mockRepository.EXPECT().RetrieveSummary(gomock.Eq(ctx), gomock.Eq(driverID), gomock.Any(), gomock.Any()).Times(1).Return(nil, wantError)

	got, _, err := s.RetrieveStats(ctx, driverID, time.Now(), time.Now(), nil)

	require.Nil(t, got)
	require.NotNil(t, err)
	require.Equal(t, wantError, err)
}

func setupFixtures(t *testing.T) (*mock_drivers.MockRepository, drivers.Service) {
	t.Helper()

	ctrl := gomock.NewController(t)
	mockRepository := mock_drivers.NewMockRepository(ctrl)
	return mockRepository, drivers.NewService(mockRepository)
}
