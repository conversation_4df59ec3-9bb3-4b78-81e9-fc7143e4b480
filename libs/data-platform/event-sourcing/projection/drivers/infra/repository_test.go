package infra_test

import (
	"context"
	"database/sql"
	"experience/libs/data-platform/event-sourcing/projection/drivers"
	"experience/libs/data-platform/event-sourcing/projection/drivers/infra"
	"experience/libs/data-platform/event-sourcing/projection/drivers/infra/test"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/data-platform/test/fixtures"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/setup"
	"experience/libs/shared/go/db"
	postgres "experience/libs/shared/go/db/postgres/test"
	"experience/libs/shared/go/service/domain"
	sharedtest "experience/libs/shared/go/test"
	"log"
	"strconv"
	"testing"
	"time"

	"github.com/shopspring/decimal"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type RepositoryTestSuite struct {
	suite.Suite
	testDB    *postgres.Database
	dbh       *sql.DB
	queries   *sqlc.Queries
	underTest drivers.Repository
}

func TestRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping DriversRepository integration test suite")
	}
	suite.Run(t, new(RepositoryTestSuite))
}

func (s *RepositoryTestSuite) SetupSuite() {
	database := setup.NewPostgresTestDB(s.T(), "file://../../../../golang-migrate/migration")
	s.testDB = database.TestDB
	s.dbh = database.DBHandle
	s.queries = sqlc.New(database.DBHandle)
	s.underTest = infra.NewRepository(database.ReadWriteDB.ReadDB)
}

func (s *RepositoryTestSuite) TearDownSuite() {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

type fFixture struct {
	name     string
	userID   uuid.UUID
	chargeID uuid.UUID
	want     *sqlc.CreateChargeProjectionParams
	others   []sqlc.CreateChargeProjectionParams
	sameUser []sqlc.CreateChargeProjectionParams
}

func newFindFixture(name string, wantCount, otherCount uint) fFixture {
	userID := uuid.New()
	chargeID := uuid.New()
	params := random.CreateChargeProjectionParamsWithUser(&userID, &chargeID, nil, false)

	want := params
	want.StartedAt = params.PluggedInAt

	return fFixture{
		name:     name,
		userID:   userID,
		chargeID: chargeID,
		want:     &want,
		others:   random.TimesCreateChargeProjectionParams(otherCount, nil, false),
		sameUser: random.TimesCreateChargeProjectionParams(wantCount-1, &userID, false),
	}
}

func (s *RepositoryTestSuite) TestFindByChargeIDUserID() {
	t := s.T()

	tests := []fFixture{
		newFindFixture("single charge", 1, 0),
		newFindFixture("single other users charge available", 1, 1),
		newFindFixture("two charges available for same user and one for another user", 2, 1),
		newFindFixture("multiple other charges available for another user", 1, random.TwoTo(100)),
		newFindFixture("multiple other charges available for both user and another user", 1+random.TwoTo(100), random.TwoTo(100)),
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()

			createCharges(ctx, t, s.queries, tt.others)
			createCharges(ctx, t, s.queries, tt.sameUser)
			random.CreateProjectionsCharge(ctx, t, s.queries, tt.want)

			got, err := s.underTest.FindByChargeIDUserID(ctx, tt.chargeID, tt.userID)

			require.NoError(t, err)
			chargeMatchesParamValues(t, tt.want, got)
		})
	}
}

func (s *RepositoryTestSuite) TestFindByChargeIDUserID_NotFound_ReturnsNil() {
	t := s.T()

	ctx := context.Background()
	chargeID := uuid.New()
	userID := uuid.New()

	got, err := s.underTest.FindByChargeIDUserID(ctx, chargeID, userID)

	require.NoError(t, err)
	require.Nil(t, got)
}

type rFixture struct {
	name     string
	userID   uuid.UUID
	expected []sqlc.CreateChargeProjectionParams
	others   []sqlc.CreateChargeProjectionParams
}

func newRetrieveFixture(name string, wantCount, otherCount uint) rFixture {
	userID := uuid.New()
	return rFixture{
		name:     name,
		userID:   userID,
		expected: random.TimesCreateChargeProjectionParams(wantCount, &userID, true),
		others:   random.TimesCreateChargeProjectionParams(otherCount, nil, true),
	}
}

type riFixture struct {
	name        string
	interval    string
	userID      uuid.UUID
	userCharges []sqlc.CreateChargeProjectionParams
	others      []sqlc.CreateChargeProjectionParams
}

func newRetrieveIntervalsFixture(name, interval string, wantCount, otherCount uint, seed []random.ChargeParamsSeed) riFixture {
	userID := uuid.New()
	return riFixture{
		name:        name,
		interval:    interval,
		userID:      userID,
		userCharges: random.TimesCreateChargeProjectionParamsWithSeed(wantCount, &userID, seed),
		others:      random.TimesCreateChargeProjectionParams(otherCount, nil, true),
	}
}

func (s *RepositoryTestSuite) TestRetrieveByUserID() {
	t := s.T()

	from, to := fixtures.GenerateDateRange()

	tests := []rFixture{
		newRetrieveFixture("no charges", 0, 0),
		newRetrieveFixture("single charge", 1, 0),
		newRetrieveFixture("single other users charge available", 1, 1),
		newRetrieveFixture("one charge for user and multiple for others", 1, random.TwoTo(100)),
		newRetrieveFixture("multiple charge for user and multiple for others", random.TwoTo(100), random.TwoTo(100)),
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()

			createCharges(ctx, t, s.queries, tt.expected)
			createCharges(ctx, t, s.queries, tt.others)

			actual, err := s.underTest.RetrieveByUserID(ctx, tt.userID, from, to)

			require.NoError(t, err)
			chargesMatchesParamValues(t, tt.expected, actual)
		})
	}
}

func (s *RepositoryTestSuite) TestRetrieveByUserID_NotFound_ReturnsEmptyArray() {
	t := s.T()

	ctx := context.Background()
	userID := uuid.New()
	fromDate := time.Date(2023, time.October, 10, 19, 10, 0, 0, time.UTC)
	toDate := time.Date(2023, time.October, 11, 19, 23, 0, 0, time.UTC)
	want := make([]drivers.Charge, 0)

	got, err := s.underTest.RetrieveByUserID(ctx, userID, fromDate, toDate)

	require.NoError(t, err)
	require.Equal(t, want, got)
}

func (s *RepositoryTestSuite) TestRetrieveByUserID_fallsBackToQueryByPluggedInAtIfStartedAtNotPresent() {
	t := s.T()

	firstChargeEnergyTotal := int64(12)
	secondChargeEnergyTotal := int64(6)

	ctx := context.Background()
	userID := uuid.New()

	chargesParams := []sqlc.CreateChargeProjectionParams{
		randomCreateHomeChargeProjectionParams(userID),
		randomCreateHomeChargeProjectionParams(userID),
	}

	// The first item only has a started_at, no plugged_in_at
	firstChargeStartedAt := time.Now().Add(time.Hour * 24 * -7).UTC()
	chargesParams[0].StartedAt = sql.NullTime{
		Time:  firstChargeStartedAt,
		Valid: true,
	}
	chargesParams[0].PluggedInAt = sql.NullTime{}
	chargesParams[0].EnergyTotal = sql.NullString{
		Valid:  true,
		String: strconv.FormatInt(firstChargeEnergyTotal, 10),
	}

	// The second item only has a plugged_in_at, no started_at
	secondChargePluggedInAt := time.Now().Add(time.Hour * 24 * -6).UTC()
	chargesParams[1].PluggedInAt = sql.NullTime{
		Time:  secondChargePluggedInAt,
		Valid: true,
	}
	chargesParams[1].StartedAt = sql.NullTime{}
	chargesParams[1].EnergyTotal = sql.NullString{
		Valid:  true,
		String: strconv.FormatInt(secondChargeEnergyTotal, 10),
	}

	createCharges(ctx, t, s.queries, chargesParams)

	from, to := fixtures.GenerateDateRange()
	got, err := s.underTest.RetrieveByUserID(ctx, userID, from, to)

	require.NoError(t, err)
	// Both charges are returned
	require.Len(t, got, 2)
	// The second charge comes first because its date was more recent
	require.Equal(t, float64(secondChargeEnergyTotal), *got[0].EnergyTotal())
	require.Equal(t, secondChargePluggedInAt.Format(time.DateTime), got[0].StartedAt().Format(time.DateTime))
	require.Equal(t, float64(firstChargeEnergyTotal), *got[1].EnergyTotal())
	require.Equal(t, firstChargeStartedAt.Format(time.DateTime), got[1].StartedAt().Format(time.DateTime))
}

func (s *RepositoryTestSuite) TestRetrieveDriverStats() {
	t := s.T()
	from, to := fixtures.GenerateDateRange()

	tests := []rFixture{
		newRetrieveFixture("no charges for user", 0, 0),
		newRetrieveFixture("multiple charges for user and multiple for others", random.TwoTo(100), random.TwoTo(100)),
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()

			createCharges(ctx, t, s.queries, tt.expected)
			createCharges(ctx, t, s.queries, tt.others)

			actual, err := s.underTest.RetrieveSummary(ctx, tt.userID, from, to)

			require.NoError(t, err)
			summaryMatchesParamValues(t, tt.expected, actual, from, to)
		})
	}
}

func (s *RepositoryTestSuite) TestFindByChargeIDUserIDReturnsChargeWithStartedAtSetToPluggedInAtIfStartedAtWasNotGiven() {
	t := s.T()

	ctx := context.Background()
	chargeID := uuid.New()
	userID := uuid.New()

	pluggedInAtTime := time.Now().UTC()

	createCharges(ctx, t, s.queries, []sqlc.CreateChargeProjectionParams{
		{
			PluggedInAt: sql.NullTime{
				Time:  pluggedInAtTime,
				Valid: true,
			},
			ChargeUUID:           chargeID,
			UserIds:              []uuid.UUID{userID},
			RewardEligibleEnergy: "0.0",
		},
	})

	got, err := s.underTest.FindByChargeIDUserID(ctx, chargeID, userID)

	require.NoError(t, err)
	require.NotNil(t, got.StartedAt())
	require.Equal(t, pluggedInAtTime.Format(time.DateTime), got.StartedAt().Format(time.DateTime))
}

func randomCreateHomeChargeProjectionParams(userID uuid.UUID) sqlc.CreateChargeProjectionParams {
	return random.CreateChargeProjectionParamsWithUser(&userID, nil, &random.ChargeParamsSeed{
		ChargerType:     ptr.To(sqlc.AccessHome),
		ChargerTimezone: ptr.To("UTC"),
	}, true)
}

func (s *RepositoryTestSuite) TestRetrieveSummaryFallsBackToQueryingForPluggedInAtIfStartedAtIsNotPresent() {
	t := s.T()
	userID := uuid.New()

	firstChargeEnergyTotal := int64(12)
	secondChargeEnergyTotal := int64(12)

	chargesParams := []sqlc.CreateChargeProjectionParams{
		randomCreateHomeChargeProjectionParams(userID),
		randomCreateHomeChargeProjectionParams(userID),
	}

	// The first item only has a started_at, no plugged_in_at
	chargesParams[0].StartedAt = sql.NullTime{
		Time:  time.Now().Add(time.Hour * 24 * -31).UTC(),
		Valid: true,
	}
	chargesParams[0].PluggedInAt = sql.NullTime{}
	chargesParams[0].EnergyTotal = sql.NullString{
		Valid:  true,
		String: strconv.FormatInt(firstChargeEnergyTotal, 10),
	}

	// The second item only has a plugged_in_at, no started_at
	chargesParams[1].PluggedInAt = sql.NullTime{
		Time:  time.Now().Add(time.Hour * 24 * -31).UTC(),
		Valid: true,
	}
	chargesParams[1].StartedAt = sql.NullTime{}
	chargesParams[1].EnergyTotal = sql.NullString{
		Valid:  true,
		String: strconv.FormatInt(secondChargeEnergyTotal, 10),
	}

	ctx := context.Background()

	createCharges(ctx, t, s.queries, chargesParams)

	from, to := fixtures.GenerateDateRange()
	actual, err := s.underTest.RetrieveSummary(ctx, userID, from, to)

	require.NoError(t, err)
	require.Equal(t, float64(firstChargeEnergyTotal+secondChargeEnergyTotal), actual.Energy.Home.Total)
}

func (s *RepositoryTestSuite) TestRetrieveSummariesByInterval() {
	from, to := fixtures.GenerateDateRange()

	tests := []riFixture{
		newRetrieveIntervalsFixture("no charges for user", "month", 0, 0, []random.ChargeParamsSeed{}),
		newRetrieveIntervalsFixture("monthly intervals for 3 user charges over 2 calendar months", "month", 3, random.TwoTo(100), []random.ChargeParamsSeed{
			{
				PluggedInAt: ptr.To(time.Date(2023, 10, 11, 12, 23, 59, 0, time.UTC)),
				UnpluggedAt: ptr.To(time.Date(2023, 10, 11, 15, 23, 59, 0, time.UTC)),
			},
			{
				PluggedInAt: ptr.To(time.Date(2023, 11, 11, 12, 23, 59, 0, time.UTC)),
				UnpluggedAt: ptr.To(time.Date(2023, 11, 11, 15, 23, 59, 0, time.UTC)),
			},
			{
				PluggedInAt: ptr.To(time.Date(2023, 11, 13, 12, 23, 59, 0, time.UTC)),
				UnpluggedAt: ptr.To(time.Date(2023, 11, 13, 15, 23, 59, 0, time.UTC)),
			},
		}),
		newRetrieveIntervalsFixture("weekly intervals for 3 user charges over 3 calendar weeks", "week", 3, random.TwoTo(100), []random.ChargeParamsSeed{
			{
				PluggedInAt: ptr.To(time.Date(2023, 10, 11, 12, 23, 59, 0, time.UTC)),
				UnpluggedAt: ptr.To(time.Date(2023, 10, 11, 15, 23, 59, 0, time.UTC)),
			},
			{
				PluggedInAt: ptr.To(time.Date(2023, 11, 11, 12, 23, 59, 0, time.UTC)),
				UnpluggedAt: ptr.To(time.Date(2023, 11, 11, 15, 23, 59, 0, time.UTC)),
			},
			{
				PluggedInAt: ptr.To(time.Date(2023, 11, 13, 12, 23, 59, 0, time.UTC)),
				UnpluggedAt: ptr.To(time.Date(2023, 11, 13, 15, 23, 59, 0, time.UTC)),
			},
		}),
		newRetrieveIntervalsFixture("daily intervals for 3 user charges over 3 calendar days", "day", 3, random.TwoTo(100), []random.ChargeParamsSeed{
			{
				PluggedInAt: ptr.To(time.Date(2023, 10, 11, 12, 23, 59, 0, time.UTC)),
				UnpluggedAt: ptr.To(time.Date(2023, 10, 11, 15, 23, 59, 0, time.UTC)),
			},
			{
				PluggedInAt: ptr.To(time.Date(2023, 11, 11, 12, 23, 59, 0, time.UTC)),
				UnpluggedAt: ptr.To(time.Date(2023, 11, 11, 15, 23, 59, 0, time.UTC)),
			},
			{
				PluggedInAt: ptr.To(time.Date(2023, 11, 13, 12, 23, 59, 0, time.UTC)),
				UnpluggedAt: ptr.To(time.Date(2023, 11, 13, 15, 23, 59, 0, time.UTC)),
			},
		}),
		newRetrieveIntervalsFixture("charges are not bucketed into UTC timezones", "day", 1, 0, []random.ChargeParamsSeed{
			{
				PluggedInAt:     ptr.To(time.Date(2025, 4, 29, 0, 0, 0, 0, time.UTC)),
				UnpluggedAt:     ptr.To(time.Date(2025, 4, 29, 5, 0, 0, 0, time.UTC)),
				ChargerTimezone: ptr.To("Europe/London"),
			},
		}),
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			ctx := context.Background()

			createCharges(ctx, t, s.queries, tt.userCharges)
			createCharges(ctx, t, s.queries, tt.others)

			actual, err := s.underTest.RetrieveSummariesByInterval(ctx, tt.userID, tt.interval, from, to)

			require.NoError(t, err)
			intervalStatsMatchParamValues(t, tt.userCharges, actual, from, to, tt.interval)
		})
	}
}

func randomCreateHomeChargeProjectionParamsWithPluggedInTimes(
	userID uuid.UUID,
	pluggedInAt time.Time,
	unpluggedAt time.Time,
) sqlc.CreateChargeProjectionParams {
	return random.CreateChargeProjectionParamsWithUser(&userID, nil, &random.ChargeParamsSeed{
		ChargerType:     ptr.To(sqlc.AccessHome),
		PluggedInAt:     ptr.To(pluggedInAt),
		UnpluggedAt:     ptr.To(unpluggedAt),
		ChargerTimezone: ptr.To(time.UTC.String()),
	}, false)
}

func (s *RepositoryTestSuite) TestRetrieveSummariesByIntervalFallsBackToQueryingForPluggedInAtIfStartedAtIsNotPresent() {
	t := s.T()
	userID := uuid.New()

	firstChargeEnergyTotal := int64(12)
	secondChargeEnergyTotal := int64(12)

	chargesParams := []sqlc.CreateChargeProjectionParams{
		randomCreateHomeChargeProjectionParamsWithPluggedInTimes(
			userID,
			time.Now().Add(time.Hour*24*-7).UTC(),
			time.Now().Add(time.Hour*24*-6).UTC(),
		),
		randomCreateHomeChargeProjectionParamsWithPluggedInTimes(
			userID,
			time.Now().Add(time.Hour*24*-7).UTC(),
			time.Now().Add(time.Hour*24*-6).UTC(),
		),
	}

	// The first item only has a started_at, no plugged_in_at
	chargesParams[0].StartedAt = sql.NullTime{
		Time:  time.Now().Add(time.Hour * 24 * -7).UTC(),
		Valid: true,
	}
	chargesParams[0].PluggedInAt = sql.NullTime{}
	chargesParams[0].EnergyTotal = sql.NullString{
		Valid:  true,
		String: strconv.FormatInt(firstChargeEnergyTotal, 10),
	}

	// The second item only has a plugged_in_at, no started_at
	chargesParams[1].PluggedInAt = sql.NullTime{
		Time:  time.Now().Add(time.Hour * 24 * -7).UTC(),
		Valid: true,
	}
	chargesParams[1].StartedAt = sql.NullTime{}
	chargesParams[1].EnergyTotal = sql.NullString{
		Valid:  true,
		String: strconv.FormatInt(secondChargeEnergyTotal, 10),
	}

	ctx := context.Background()

	createCharges(ctx, t, s.queries, chargesParams)

	from, to := fixtures.GenerateDateRange()
	actual, err := s.underTest.RetrieveSummariesByInterval(ctx, userID, "month", from, to)

	require.NoError(t, err)
	require.Equal(t, float64(firstChargeEnergyTotal+secondChargeEnergyTotal), actual[0].Stats.Energy.Home.Total)
}

func (s *RepositoryTestSuite) AfterTest(_, _ string) {
	prepare, _ := s.dbh.Prepare("TRUNCATE TABLE projections.charges")
	_, _ = prepare.Exec()
}

func chargesMatchesParamValues(t *testing.T, want []sqlc.CreateChargeProjectionParams, got []drivers.Charge) {
	t.Helper()

	matcher := ChargeMatcher[sqlc.CreateChargeProjectionParams, drivers.Charge]{}
	comparison := sharedtest.Contains[sqlc.CreateChargeProjectionParams, drivers.Charge](t, matcher, &want, &got)
	require.Condition(t, comparison)

	for i := 0; i < len(want); i++ {
		require.NotNil(t, got[i].ID)
	}
}

func chargeMatchesParamValues(t *testing.T, want *sqlc.CreateChargeProjectionParams, got *drivers.Charge) {
	t.Helper()

	require.NotNil(t, got.ID)

	matcher := ChargeMatcher[sqlc.CreateChargeProjectionParams, drivers.Charge]{}
	require.True(t, matcher.Matches(*want, *got))
}

func summaryMatchesParamValues(t *testing.T, params []sqlc.CreateChargeProjectionParams, summary *drivers.StatsSummary, from, to time.Time) {
	t.Helper()
	expectedStats := drivers.StatsSummary{
		Cost: drivers.CostSummary{
			Home:    []domain.Money64{{Amount: 0, Currency: "GBP"}},
			Private: []domain.Money64{{Amount: 0, Currency: "GBP"}},
			Public:  []domain.Money64{{Amount: 0, Currency: "GBP"}},
		},
		Energy: drivers.EnergySummary{
			Home: drivers.EnergyBreakdown{
				Grid:       ptr.To(0.0),
				Generation: ptr.To(0.0),
				Total:      0,
			},
		},
	}
	for i := range params {
		param := params[i]
		if param.UnpluggedAt.Valid && param.PluggedInAt.Time.After(from) && param.PluggedInAt.Time.Before(to) {
			switch param.ChargerType.Access {
			case sqlc.AccessHome:
				expectedStats.Energy.Home.Total = decimal.NewFromFloat(expectedStats.Energy.Home.Total + db.NullStringToFloat64(param.EnergyTotal)).Round(1).InexactFloat64()
				*expectedStats.Energy.Home.Grid = decimal.NewFromFloat(*expectedStats.Energy.Home.Grid + db.NullStringToFloat64(param.GridEnergyTotal)).Round(1).InexactFloat64()
				*expectedStats.Energy.Home.Generation = decimal.NewFromFloat(*expectedStats.Energy.Home.Generation + db.NullStringToFloat64(param.GenerationEnergyTotal)).Round(1).InexactFloat64()
				expectedStats.Cost.Home[0].Amount += int64(db.NullInt32ToInt(param.EnergyCost))
				expectedStats.Duration.Home += db.NullInt32ToInt(param.ChargeDurationTotal)
			case sqlc.AccessPrivate:
				expectedStats.Energy.Private.Total = decimal.NewFromFloat(expectedStats.Energy.Private.Total + db.NullStringToFloat64(param.EnergyTotal)).Round(1).InexactFloat64()
				expectedStats.Cost.Private[0].Amount += int64(db.NullInt32ToInt(param.SettlementAmount))
				expectedStats.Duration.Private += db.NullInt32ToInt(param.ChargeDurationTotal)
			case sqlc.AccessPublic:
				expectedStats.Energy.Public.Total = decimal.NewFromFloat(expectedStats.Energy.Public.Total + db.NullStringToFloat64(param.EnergyTotal)).Round(1).InexactFloat64()
				expectedStats.Cost.Public[0].Amount += int64(db.NullInt32ToInt(param.SettlementAmount))
				expectedStats.Duration.Public += db.NullInt32ToInt(param.ChargeDurationTotal)
			}
		}
	}

	require.Equal(t, expectedStats.Energy, summary.Energy)
	require.Equal(t, expectedStats.Cost, summary.Cost)
	require.Equal(t, expectedStats.Duration, summary.Duration)
}

func intervalStatsMatchParamValues(t *testing.T, userCharges []sqlc.CreateChargeProjectionParams, intervalStats []*drivers.IntervalStats, from, to time.Time, interval string) {
	t.Helper()
	expectedStats := make([]*drivers.IntervalStats, 0, len(userCharges))

buildStatForCharge:
	for i := range userCharges {
		charge := userCharges[i]
		if !charge.UnpluggedAt.Valid || charge.UnpluggedAt.Time.Before(from) || charge.UnpluggedAt.Time.After(to) {
			continue
		}

		chargeIntervalStart := test.IntervalStart(t, charge.PluggedInAt.Time, interval, nullable(charge.ChargerTimezone))

		for j, stat := range expectedStats {
			if expectedStats[j].From.Equal(chargeIntervalStart) {
				updateIntervalStatValues(&charge, stat)
				continue buildStatForCharge
			}
		}
		chargeIntervalEnd := addInterval(chargeIntervalStart, interval)
		stat := drivers.IntervalStats{
			From: chargeIntervalStart,
			To:   chargeIntervalEnd,
		}
		updateIntervalStatValues(&charge, &stat)
		expectedStats = append(expectedStats, &stat)
	}

	require.Equal(t, expectedStats, intervalStats)
}

func nullable(timezone sql.NullString) *string {
	if timezone.Valid {
		return &timezone.String
	}
	return nil
}

func updateIntervalStatValues(charge *sqlc.CreateChargeProjectionParams, stat *drivers.IntervalStats) {
	if stat.Stats.Cost.Home == nil && stat.Stats.Cost.Private == nil && stat.Stats.Cost.Public == nil {
		stat.Stats.Cost = drivers.CostSummary{
			Home:    []domain.Money64{{Amount: 0, Currency: "GBP"}},
			Private: []domain.Money64{{Amount: 0, Currency: "GBP"}},
			Public:  []domain.Money64{{Amount: 0, Currency: "GBP"}},
		}
	}
	if stat.Stats.Energy.Home.Grid == nil {
		stat.Stats.Energy.Home.Grid = ptr.To(0.0)
	}
	if stat.Stats.Energy.Home.Generation == nil {
		stat.Stats.Energy.Home.Generation = ptr.To(0.0)
	}

	switch charge.ChargerType.Access {
	case sqlc.AccessHome:
		stat.Stats.Energy.Home.Total = decimal.NewFromFloat(stat.Stats.Energy.Home.Total + db.NullStringToFloat64(charge.EnergyTotal)).Round(1).InexactFloat64()
		*stat.Stats.Energy.Home.Grid = decimal.NewFromFloat(*stat.Stats.Energy.Home.Grid + db.NullStringToFloat64(charge.GridEnergyTotal)).Round(1).InexactFloat64()
		*stat.Stats.Energy.Home.Generation = decimal.NewFromFloat(*stat.Stats.Energy.Home.Generation + db.NullStringToFloat64(charge.GenerationEnergyTotal)).Round(1).InexactFloat64()
		stat.Stats.Cost.Home[0].Amount += int64(db.NullInt32ToInt(charge.EnergyCost))
		stat.Stats.Duration.Home += db.NullInt32ToInt(charge.ChargeDurationTotal)
	case sqlc.AccessPrivate:
		stat.Stats.Energy.Private.Total = decimal.NewFromFloat(stat.Stats.Energy.Private.Total + db.NullStringToFloat64(charge.EnergyTotal)).Round(1).InexactFloat64()
		stat.Stats.Cost.Private[0].Amount += int64(db.NullInt32ToInt(charge.SettlementAmount))
		stat.Stats.Duration.Private += db.NullInt32ToInt(charge.ChargeDurationTotal)
	case sqlc.AccessPublic:
		stat.Stats.Energy.Public.Total = decimal.NewFromFloat(stat.Stats.Energy.Public.Total + db.NullStringToFloat64(charge.EnergyTotal)).Round(1).InexactFloat64()
		stat.Stats.Cost.Public[0].Amount += int64(db.NullInt32ToInt(charge.SettlementAmount))
		stat.Stats.Duration.Public += db.NullInt32ToInt(charge.ChargeDurationTotal)
	}
}

func createCharges(ctx context.Context, t *testing.T, queries *sqlc.Queries, params []sqlc.CreateChargeProjectionParams) {
	t.Helper()

	if params == nil {
		return
	}
	for i := 0; i < len(params); i++ {
		random.CreateProjectionsCharge(ctx, t, queries, &params[i])
	}
}

func addInterval(t1 time.Time, interval string) time.Time {
	switch interval {
	case "day":
		return t1.Add(time.Hour * 24)
	case "week":
		return t1.Add(time.Hour * 24 * 7)
	case "month":
		return time.Date(t1.Year(), t1.Month()+1, t1.Day(), t1.Hour(), t1.Minute(), t1.Second(), 0, t1.Location())
	default:
		panic("unsupported date addition")
	}
}
