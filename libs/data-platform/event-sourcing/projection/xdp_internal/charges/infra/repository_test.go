package infra_test

import (
	"context"
	"database/sql"
	"errors"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra"
	"experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/data-platform/test/random"
	cfg "experience/libs/shared/go/db"
	migrate "experience/libs/shared/go/db-migrate/postgres"
	"experience/libs/shared/go/db/postgres"
	"experience/libs/shared/go/db/postgres/test"
	"log"
	"testing"

	"github.com/google/uuid"
	"github.com/jinzhu/copier"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type ChargeProjectionsRepositoryTestSuite struct {
	suite.Suite
	testDB    *test.Database
	dbh       *sql.DB
	queries   *sqlc.Queries
	underTest charges.Repository
}

func TestChargeProjectionsRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping ChargeProjectionsRepository integration test suite")
	}
	suite.Run(t, new(ChargeProjectionsRepositoryTestSuite))
}

func (s *ChargeProjectionsRepositoryTestSuite) SetupSuite() {
	t := s.T()
	var err error

	s.testDB = test.NewDatabase(t, test.WithUser("xdp_events_queue_worker"))
	passwordConfig := s.testDB.PasswordConfig(t)

	s.dbh, err = postgres.NewPasswordDB(passwordConfig, true)
	require.NoError(t, err)

	_, err = migrate.MigrateUp(
		"file://../../../../../golang-migrate/migration",
		s.testDB.PasswordConfig(t),
		nil,
		true,
	)
	require.NoError(t, err)

	config := s.testDB.IamConfig(t)
	rwDB := postgres.NewReadWriteDB(&cfg.ServiceDatasource{
		ReadConfig:  cfg.ReadConfig{IAMConfig: config},
		WriteConfig: cfg.WriteConfig{IAMConfig: config},
	}, true)
	s.queries = sqlc.New(s.dbh)
	s.underTest = infra.NewRepository(rwDB.ReadDB)
}

func (s *ChargeProjectionsRepositoryTestSuite) TearDownSuite() {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

func (s *ChargeProjectionsRepositoryTestSuite) TestSaveChargeProjectionInsert() {
	t := s.T()

	ctx := context.Background()
	projections := random.TimesCreateChargeProjections(100)

	for i := 0; i < len(projections); i++ {
		want := projections[i]
		chargeID := want.ChargeUUID

		tx, _ := s.dbh.Begin()
		_ = s.underTest.Save(ctx, &want)
		_ = tx.Commit()

		got, err := s.FindByChargeID(ctx, chargeID)

		require.NoError(t, err)
		require.NotNil(t, got)
		require.Equal(t, want, *got)
	}
}

func (s *ChargeProjectionsRepositoryTestSuite) TestSaveChargeProjectionUpdate() {
	t := s.T()

	ctx := context.Background()
	projections := random.TimesCreateProjectionsCharges(ctx, t, 100, s.queries)

	for i := 0; i < len(projections); i++ {
		cp := projections[i]
		chargeID := cp.ChargeUUID
		want, _ := random.CreateChargeProjection(&chargeID, nil)

		tx, _ := s.dbh.Begin()
		_ = s.underTest.Save(ctx, &want)
		_ = tx.Commit()

		got, err := s.FindByChargeID(ctx, chargeID)

		require.NoError(t, err)
		require.NotNil(t, got)
		require.Equal(t, want, *got)
	}
}

func (s *ChargeProjectionsRepositoryTestSuite) AfterTest(_, _ string) {
	s.clearChargeProjectionsTable()
}

func (s *ChargeProjectionsRepositoryTestSuite) clearChargeProjectionsTable() {
	prepare, _ := s.dbh.Prepare("TRUNCATE TABLE projections.charges")
	_, _ = prepare.Exec()
}

func (s *ChargeProjectionsRepositoryTestSuite) FindByChargeID(ctx context.Context, chargeID uuid.UUID) (*charges.Projection, error) {
	row, err := s.queries.FindByChargeID(ctx, chargeID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, charges.ErrChargeProjectionNotFound
		}
		return nil, err
	}
	return toChargeProjection(&row)
}

func toChargeProjection(row *sqlc.ProjectionsCharge) (*charges.Projection, error) {
	var result charges.Projection
	err := copier.CopyWithOption(&result, row, copier.Option{
		Converters: []copier.TypeConverter{
			cfg.NullStringToFloatPointerConverter,
			cfg.NullUUIDToUUIDPointerConverter,
		},
	})
	if err != nil {
		return nil, err
	}
	return &result, nil
}
