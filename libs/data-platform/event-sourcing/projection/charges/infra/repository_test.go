package infra_test

import (
	"context"
	"database/sql"
	"experience/libs/data-platform/event-sourcing/projection/charges"
	"experience/libs/data-platform/event-sourcing/projection/charges/infra"
	"experience/libs/data-platform/event-sourcing/projection/charges/infra/sqlc"
	random2 "experience/libs/data-platform/event-sourcing/projection/charges/random"
	testChargeProjection "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges"
	testChargeProjectionInfra "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/setup"
	"experience/libs/shared/go/db/postgres/test"
	"log"
	"testing"
	"time"

	"github.com/google/uuid"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"k8s.io/utils/ptr"
)

type ProjectionChargeRepositoryTestSuite struct {
	suite.Suite
	testDB               *test.Database
	db                   *sql.DB
	queries              *sqlc.Queries
	underTest            charges.Repository
	testChargeProjection testChargeProjection.Repository
}

func TestProjectionChargeRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping ChargeProjectionsRepository integration test suite")
	}
	suite.Run(t, new(ProjectionChargeRepositoryTestSuite))
}

func (s *ProjectionChargeRepositoryTestSuite) SetupSuite() {
	db := setup.NewPostgresTestDB(s.T(), "file://../../../../golang-migrate/migration")
	s.testDB = db.TestDB
	s.db = db.DBHandle
	s.queries = sqlc.New(db.DBHandle)
	s.underTest = infra.NewRepository(db.ReadWriteDB.ReadDB)
	s.testChargeProjection = testChargeProjectionInfra.NewRepository(db.ReadWriteDB.ReadDB)
}

func (s *ProjectionChargeRepositoryTestSuite) TearDownSuite() {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

func (s *ProjectionChargeRepositoryTestSuite) AfterTest(_, _ string) {
	prepare, _ := s.db.Prepare("TRUNCATE TABLE projections.charges")
	_, _ = prepare.Exec()
}

func (s *ProjectionChargeRepositoryTestSuite) TearDownSubTest() {
	prepare, _ := s.db.Prepare("TRUNCATE TABLE projections.charges")
	_, _ = prepare.Exec()
}

func (s *ProjectionChargeRepositoryTestSuite) createProjections(seed *random2.ChargeParamsSeed) ([]charges.Projection, error) {
	noOfCharges := random.TwoTo(2)
	want := random2.TimesCreateChargeProjections(noOfCharges, seed)
	reducedWant := make([]charges.Projection, len(want))

	for i := 0; i < len(want); i++ {
		tx, err := s.db.Begin()
		if err != nil {
			return nil, err
		}

		err = s.testChargeProjection.Save(context.Background(), &want[i])
		if err != nil {
			return nil, err
		}

		err = tx.Commit()
		if err != nil {
			return nil, err
		}

		reducedWant[i] = charges.Projection{
			ID:                    want[i].ChargeUUID,
			SiteName:              want[i].SiteName,
			ChargerID:             want[i].ChargerID,
			ChargerName:           want[i].ChargerName,
			PluggedInAt:           want[i].PluggedInAt,
			StartedAt:             want[i].StartedAt,
			EndedAt:               want[i].EndedAt,
			UnpluggedAt:           want[i].UnpluggedAt,
			EnergyTotal:           want[i].EnergyTotal,
			GenerationEnergyTotal: want[i].GenerationEnergyTotal,
			GridEnergyTotal:       want[i].GridEnergyTotal,
			Door:                  want[i].Door,
			DriverIDs:             want[i].UserIDs,
			RevenueGenerated:      want[i].SettlementAmount,
			ChargeDurationTotal:   want[i].ChargeDurationTotal,
			EnergyCost:            want[i].EnergyCost,
			Confirmed:             ptr.To(true),
		}
	}

	return reducedWant, nil
}

func (s *ProjectionChargeRepositoryTestSuite) TestRetrieveProjectionCharges() {
	fromDate := time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC)
	toDate := time.Date(2024, 2, 2, 0, 0, 0, 0, time.UTC)
	groupID := ptr.To(uuid.New())
	siteID := ptr.To(uuid.New())
	driverID := uuid.New()

	tests := []struct {
		name   string
		seed   []*random2.ChargeParamsSeed
		params charges.RetrieveProjectionChargesParams
	}{
		{
			name: "Pass in GroupID",
			seed: createSeedParams(fromDate, groupID, nil, nil, driverID, nil),
			params: charges.RetrieveProjectionChargesParams{
				FromDate:  fromDate,
				ToDate:    toDate,
				GroupID:   groupID,
				SiteID:    nil,
				ChargerID: nil,
			},
		},
		{
			name: "Pass in SiteID",
			seed: createSeedParams(fromDate, nil, siteID, nil, driverID, nil),
			params: charges.RetrieveProjectionChargesParams{
				FromDate:  fromDate,
				ToDate:    toDate,
				GroupID:   nil,
				SiteID:    siteID,
				ChargerID: nil,
			},
		},
		{
			name: "Pass in ChargerID",
			seed: createSeedParams(fromDate, nil, nil, ptr.To("charger id"), driverID, nil),
			params: charges.RetrieveProjectionChargesParams{
				FromDate:  fromDate,
				ToDate:    toDate,
				GroupID:   nil,
				SiteID:    nil,
				ChargerID: ptr.To("charger id"),
			},
		},
		{
			name: "Pass in GroupID, SiteID, ChargerID",
			seed: createSeedParams(fromDate, groupID, siteID, ptr.To("charger id"), driverID, nil),
			params: charges.RetrieveProjectionChargesParams{
				FromDate:  fromDate,
				ToDate:    toDate,
				GroupID:   groupID,
				SiteID:    siteID,
				ChargerID: ptr.To("charger id"),
			},
		},
		{
			name: "Test date boundaries - expect data only for fromDate (toDate is exclusive)",
			seed: createSeedParams(fromDate, groupID, siteID, ptr.To("charger id"), driverID, ptr.To(toDate)),
			params: charges.RetrieveProjectionChargesParams{
				FromDate:  fromDate,
				ToDate:    toDate,
				GroupID:   groupID,
				SiteID:    siteID,
				ChargerID: ptr.To("charger id"),
			},
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			t := s.T()
			ctx := context.Background()

			seededCharges := make([][]charges.Projection, len(tt.seed))

			for i, seed := range tt.seed {
				data, err := s.createProjections(seed)
				if err != nil {
					t.Errorf("createProjections() error = %v", err)
					return
				}
				seededCharges[i] = data
			}

			reducedWant := seededCharges[0]

			got, err := s.underTest.RetrieveProjectionCharges(ctx, tt.params)

			require.NoError(t, err)
			require.NotNil(t, got)
			require.ElementsMatch(t, reducedWant, got)
		})
	}
}

func createSeedParams(fromDate time.Time, groupID, siteID *uuid.UUID, chargerID *string, driverID uuid.UUID, toDate *time.Time) []*random2.ChargeParamsSeed {
	if chargerID == nil {
		chargerID = ptr.To("charger id")
	}

	seed := &random2.ChargeParamsSeed{
		SiteName:              ptr.To("site name"),
		ChargerID:             chargerID,
		ChargerName:           ptr.To("charger name"),
		PluggedInAt:           ptr.To(fromDate),
		StartedAt:             ptr.To(fromDate.Add(time.Hour)),
		EndedAt:               ptr.To(fromDate.Add(2 * time.Hour)),
		UnpluggedAt:           ptr.To(fromDate.Add(3 * time.Hour)),
		EnergyTotal:           ptr.To(float32(10.0)),
		GenerationEnergyTotal: ptr.To(float32(3.5)),
		GridEnergyTotal:       ptr.To(float32(6.5)),
		Door:                  ptr.To("A"),
		DriverID:              driverID,
		RevenueGenerated:      ptr.To(int32(100)),
		ChargeDurationTotal:   ptr.To(int32(2)),
		EnergyCost:            ptr.To(int32(10)),
		GroupID:               groupID,
		SiteID:                siteID,
		Confirmed:             ptr.To(true),
		Points:                ptr.To(float32(10.5)),
		PointsExpiryDate:      ptr.To(fromDate.AddDate(1, 0, 0)),
		PointsRedeemedAt:      ptr.To(fromDate.AddDate(0, 1, 0)),
	}

	seeds := []*random2.ChargeParamsSeed{seed}

	if toDate != nil {
		anotherSeed := &random2.ChargeParamsSeed{
			SiteName:              seed.SiteName,
			ChargerID:             seed.ChargerID,
			ChargerName:           seed.ChargerName,
			PluggedInAt:           toDate,
			StartedAt:             ptr.To(toDate.Add(time.Hour)),
			EndedAt:               ptr.To(toDate.Add(2 * time.Hour)),
			UnpluggedAt:           ptr.To(toDate.Add(3 * time.Hour)),
			EnergyTotal:           seed.EnergyTotal,
			GenerationEnergyTotal: seed.GenerationEnergyTotal,
			GridEnergyTotal:       seed.GridEnergyTotal,
			Door:                  seed.Door,
			DriverID:              seed.DriverID,
			RevenueGenerated:      seed.RevenueGenerated,
			ChargeDurationTotal:   seed.ChargeDurationTotal,
			EnergyCost:            seed.EnergyCost,
			GroupID:               seed.GroupID,
			SiteID:                seed.SiteID,
			Confirmed:             ptr.To(true),
			Points:                ptr.To(float32(10.5)),
			PointsExpiryDate:      ptr.To(toDate.AddDate(1, 0, 0)),
			PointsRedeemedAt:      ptr.To(toDate.AddDate(0, 1, 0)),
		}
		seeds = append(seeds, anotherSeed)
	}

	return seeds
}
