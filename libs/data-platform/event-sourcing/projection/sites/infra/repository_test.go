package infra_test

import (
	"database/sql"
	"experience/libs/data-platform/event-sourcing/projection/sites"
	"experience/libs/data-platform/event-sourcing/projection/sites/infra"
	sqlcsites "experience/libs/data-platform/event-sourcing/projection/sites/infra/sqlc"
	sqlccharges "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	"experience/libs/shared/go/db/postgres/test"
	"log"
	"strconv"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

var from = time.Now().UTC().Add(-24 * time.Hour)
var to = time.Now().UTC()

type SitesRepositoryTestSuite struct {
	suite.Suite
	testDB         *test.Database
	dbh            *sql.DB
	queries        *sqlcsites.Queries
	queriesCharges *sqlccharges.Queries
	underTest      sites.Repository
}

func TestSitesRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping SitesRepository integration test suite")
	}
	suite.Run(t, new(SitesRepositoryTestSuite))
}

func (s *SitesRepositoryTestSuite) SetupSuite() {
	db := setup.NewPostgresTestDB(s.T(), "file://../../../../golang-migrate/migration")
	s.testDB = db.TestDB
	s.dbh = db.DBHandle
	s.queries = sqlcsites.New(db.DBHandle)
	s.queriesCharges = sqlccharges.New(db.DBHandle)
	s.underTest = infra.NewRepository(db.ReadWriteDB.ReadDB)
}

func (s *SitesRepositoryTestSuite) TearDownSuite() {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

func (s *SitesRepositoryTestSuite) AfterTest(_, _ string) {
	prepare, _ := s.dbh.Prepare("TRUNCATE TABLE projections.charges")
	_, _ = prepare.Exec()
}

func (s *SitesRepositoryTestSuite) TestRetrieveChargeStatsGroupedBySite() {
	t := s.T()
	ctx := s.T().Context()
	groupID := uuid.New()
	groupName := random.SiteName(t)
	siteID := uuid.New()
	siteName := random.SiteName(t)
	chargerType := random.CommercialChargerTypeNullable()

	chargesSeeded, _, _ := seeding.ChargesToRetrieveBySite(ctx, t, s.queriesCharges, groupID, siteID, from, siteName, groupName, chargerType, ptr.To(10))
	want := s.prepareExpectedResult(siteID, &groupID, siteName, groupName, chargesSeeded)

	got, err := s.underTest.RetrieveChargeStatsGroupedBySite(ctx, from, to)

	require.NoError(t, err)
	require.Len(t, got, 1)
	require.Equal(t, want, got)
}

func (s *SitesRepositoryTestSuite) TestRetrieveChargeStatsGroupedBySiteNilSettlement() {
	t := s.T()
	ctx := s.T().Context()
	groupID := uuid.New()
	groupName := ""
	siteID := uuid.New()
	siteName := ""
	chargerType := random.CommercialChargerTypeNullable()

	chargesSeeded, _, anotherSiteID := seeding.ChargesToRetrieveBySite(ctx, t, s.queriesCharges, groupID, siteID, from, siteName, groupName, chargerType, nil)
	want := s.prepareExpectedResult(siteID, &groupID, siteName, groupName, chargesSeeded)
	anotherSiteStat := sites.SiteStats{
		SiteID:           anotherSiteID.UUID,
		SiteName:         nil,
		GroupID:          ptr.To(groupID),
		GroupName:        nil,
		EnergyTotal:      1.1,
		RevenueGenerated: 0,
	}

	want = append([]sites.SiteStats{anotherSiteStat}, want...)

	got, err := s.underTest.RetrieveChargeStatsGroupedBySite(ctx, from, to)

	require.NoError(t, err)
	require.Len(t, got, 2)
	require.ElementsMatch(t, want, got)
}

func (s *SitesRepositoryTestSuite) TestFilterHomeChargeStats() {
	t := s.T()
	ctx := s.T().Context()
	groupID := uuid.New()
	groupName := random.SiteName(t)
	siteID := uuid.New()
	siteName := random.SiteName(t)
	chargerType := sqlccharges.NullAccess{
		Access: sqlccharges.Access(sqlcsites.AccessHome),
		Valid:  true,
	}

	chargesSeeded, _, _ := seeding.ChargesToRetrieveBySite(ctx, t, s.queriesCharges, groupID, siteID, from, siteName, groupName, chargerType, ptr.To(10))
	_ = s.prepareExpectedResult(siteID, &groupID, siteName, groupName, chargesSeeded)

	got, err := s.underTest.RetrieveChargeStatsGroupedBySite(ctx, from, to)

	require.NoError(t, err)
	require.Len(t, got, 0)
}

func (s *SitesRepositoryTestSuite) TestRetrieveChargeStatsGroupedBySiteInTimeRange() {
	t := s.T()
	ctx := s.T().Context()
	groupID := uuid.New()
	groupName := random.SiteName(t)
	siteID := uuid.New()
	siteName := random.SiteName(t)
	chargerType := random.CommercialChargerTypeNullable()

	chargesSeeded := seeding.ChargesToRetrieveBySiteInTimeRange(ctx, t, s.queriesCharges, groupID, siteID, from, siteName, groupName, chargerType)
	want := s.prepareExpectedResult(siteID, &groupID, siteName, groupName, chargesSeeded)

	got, err := s.underTest.RetrieveChargeStatsGroupedBySite(ctx, from, to)

	require.NoError(t, err)
	require.Len(t, got, 1)
	require.Equal(t, want, got)
}

func (s *SitesRepositoryTestSuite) prepareExpectedResult(siteID uuid.UUID, groupID *uuid.UUID, siteName, groupName string, chargesSeeded []sqlccharges.ProjectionsCharge) []sites.SiteStats {
	energyTotal := 0.0
	revenueGenerated := 0

	for i := 0; i < len(chargesSeeded); i++ {
		energy, _ := strconv.ParseFloat(chargesSeeded[i].EnergyTotal.String, 64)
		energyTotal += energy
		revenueGenerated += int(chargesSeeded[i].SettlementAmount.Int32)
	}

	return []sites.SiteStats{
		{
			SiteID:           siteID,
			SiteName:         &siteName,
			GroupID:          groupID,
			GroupName:        &groupName,
			EnergyTotal:      energyTotal,
			RevenueGenerated: revenueGenerated,
		},
	}
}
