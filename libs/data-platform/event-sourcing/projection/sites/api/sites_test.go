package api_test

import (
	"encoding/json"
	"experience/libs/data-platform/api/contract/gen/http/sites/server"
	contract "experience/libs/data-platform/api/contract/gen/sites"
	"experience/libs/data-platform/event-sourcing/projection/sites"
	"experience/libs/data-platform/event-sourcing/projection/sites/api"
	mocksites "experience/libs/data-platform/event-sourcing/projection/sites/mock"
	"experience/libs/data-platform/test/random"
	errormapper "experience/libs/shared/go/http"
	"experience/libs/shared/go/http/goaerror"
	"experience/libs/shared/go/service/utils"
	"fmt"
	"io"
	"net/http"
	"testing"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"github.com/ikawaha/goahttpcheck"
	"go.uber.org/mock/gomock"
)

func Test_sitesSvc_RetrieveChargeStatsGroupedBySite_OK(t *testing.T) {
	siteID := utils.FabricateUUIDFromNumericID(234)
	siteName := random.SiteName(t)
	groupID := uuid.New()
	groupName := random.SiteName(t)

	tests := []struct {
		name                string
		path                string
		mockServiceResponse []sites.SiteStats
		wantResponse        *contract.SiteStatsResponse
	}{
		{
			name: "valid dates",
			path: fmt.Sprintf("/sites?from=%s&to=%s", "2023-01-01", "2023-01-31"),
			mockServiceResponse: []sites.SiteStats{
				{
					SiteID:           siteID,
					SiteName:         &siteName,
					GroupID:          ptr.To(groupID),
					GroupName:        &groupName,
					EnergyTotal:      4.5,
					RevenueGenerated: 20,
				},
			},
			wantResponse: &contract.SiteStatsResponse{
				Count: 1,
				From:  "2023-01-01",
				To:    "2023-01-31",
				Data: []*contract.SiteStats{
					{
						ID:               "234",
						Name:             ptr.To(siteName),
						GroupID:          ptr.To(groupID.String()),
						GroupName:        ptr.To(groupName),
						TotalEnergy:      4.5,
						RevenueGenerated: 20,
					},
				},
			},
		},
	}

	for _, tt := range tests {
		var checker = goahttpcheck.New(goahttpcheck.Formatter(goaerror.FormatError))
		mockService := mocksites.NewMockService(gomock.NewController(t))
		checker.Mount(
			server.NewRetrieveChargeStatsGroupedBySiteHandler,
			server.MountRetrieveChargeStatsGroupedBySiteHandler,
			contract.NewRetrieveChargeStatsGroupedBySiteEndpoint(api.NewService(mockService, errormapper.NewErrorMapper(make(map[error]error)))))

		mockService.EXPECT().RetrieveChargeStatsGroupedBySite(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).Return(tt.mockServiceResponse, nil)

		checker.Test(t, http.MethodGet, tt.path).Check().HasStatus(http.StatusOK).
			Cb(func(r *http.Response) {
				b, _ := io.ReadAll(r.Body)
				var response contract.SiteStatsResponse
				if err := json.Unmarshal(b, &response); err != nil {
					t.Fatalf("unexpected error, %v", err)
				}
				assert.Equal(t, tt.wantResponse, &response)
			})
	}
}

func Test_sitesSvc_RetrieveChargeStatsGroupedBySite_BadRequest(t *testing.T) {
	tests := []struct {
		name string
		path string
	}{
		{
			name: "invalid dates",
			path: fmt.Sprintf("/sites?from=%s&to=%s", "invalid", "invalid"),
		},
		{
			name: "invalid from date",
			path: fmt.Sprintf("/sites?from=%s&to=%s", "invalid", "2023-01-31"),
		},
		{
			name: "invalid to date",
			path: fmt.Sprintf("/sites?from=%s&to=%s", "2023-01-31", "invalid"),
		},
	}

	for _, tt := range tests {
		var checker = goahttpcheck.New()
		mockService := mocksites.NewMockService(gomock.NewController(t))
		checker.Mount(
			server.NewRetrieveChargeStatsGroupedBySiteHandler,
			server.MountRetrieveChargeStatsGroupedBySiteHandler,
			contract.NewRetrieveChargeStatsGroupedBySiteEndpoint(api.NewService(mockService, errormapper.NewErrorMapper(make(map[error]error)))))

		mockService.EXPECT().RetrieveChargeStatsGroupedBySite(gomock.Any(), gomock.Any(), gomock.Any()).Times(0)

		checker.Test(t, http.MethodGet, tt.path).Check().HasStatus(http.StatusBadRequest)
	}
}
