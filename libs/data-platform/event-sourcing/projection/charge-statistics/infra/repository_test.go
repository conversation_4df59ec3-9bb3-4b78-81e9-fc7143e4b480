package infra_test

import (
	"cmp"
	"context"
	"database/sql"
	chargestatistics "experience/libs/data-platform/event-sourcing/projection/charge-statistics"
	"experience/libs/data-platform/event-sourcing/projection/charge-statistics/infra"
	sqlcchargestatistics "experience/libs/data-platform/event-sourcing/projection/charge-statistics/infra/sqlc"
	sqlccharges "experience/libs/data-platform/event-sourcing/projection/xdp_internal/charges/infra/sqlc"
	"experience/libs/data-platform/test/random"
	"experience/libs/data-platform/test/seeding"
	"experience/libs/data-platform/test/setup"
	"experience/libs/shared/go/db/postgres/test"
	"log"
	"math"
	"slices"
	"strconv"
	"testing"
	"time"

	"github.com/jinzhu/now"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

const (
	day   = "day"
	week  = "week"
	month = "month"
)

type ChargeStatisticsRepositoryTestSuite struct {
	suite.Suite
	testDB         *test.Database
	dbh            *sql.DB
	queries        *sqlcchargestatistics.Queries
	queriesCharges *sqlccharges.Queries
	underTest      chargestatistics.Repository
}

func TestChargeStatisticsRepositoryTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping ChargeStatisticsRepository integration test suite")
	}
	suite.Run(t, new(ChargeStatisticsRepositoryTestSuite))
}

func (s *ChargeStatisticsRepositoryTestSuite) SetupSuite() {
	db := setup.NewPostgresTestDB(s.T(), "file://../../../../golang-migrate/migration")
	s.testDB = db.TestDB
	s.dbh = db.DBHandle
	s.queries = sqlcchargestatistics.New(db.DBHandle)
	s.queriesCharges = sqlccharges.New(db.DBHandle)
	s.underTest = infra.NewRepository(db.ReadWriteDB.ReadDB)
}

func (s *ChargeStatisticsRepositoryTestSuite) TearDownSuite() {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

func (s *ChargeStatisticsRepositoryTestSuite) AfterTest(_, _ string) {
	prepare, _ := s.dbh.Prepare("TRUNCATE TABLE projections.charges")
	_, _ = prepare.Exec()
}

func (s *ChargeStatisticsRepositoryTestSuite) prepareExpectedChargeStatistics(chargesSeeded []sqlccharges.ProjectionsCharge) chargestatistics.SiteStatistics {
	chargerIDs := make([]string, 0)
	chargingDuration := 0
	claimedEnergyUsage := 0.0
	energyCost := 0
	energyTotal := 0.0
	revenueGenerated := 0
	revenueGeneratingClaimedEnergyUsage := 0.0
	unclaimedEnergyUsage := 0.0
	userIDs := make([]string, 0)

	for i := 0; i < len(chargesSeeded); i++ {
		chargerIDs = append(chargerIDs, chargesSeeded[i].ChargerID.String)
		chargingDuration += int(chargesSeeded[i].ChargeDurationTotal.Int32)
		energy, _ := strconv.ParseFloat(chargesSeeded[i].EnergyTotal.String, 64)
		energyCost += int(chargesSeeded[i].EnergyCost.Int32)
		energyTotal += energy
		revenueGenerated += int(chargesSeeded[i].SettlementAmount.Int32)

		for _, userID := range chargesSeeded[i].UserIds {
			userIDs = append(userIDs, userID.String())
		}

		if chargesSeeded[i].Confirmed.Bool {
			claimedEnergyUsage += energy
			if chargesSeeded[i].SettlementAmount.Int32 > 0 {
				revenueGeneratingClaimedEnergyUsage += energy
			}
		}

		if !chargesSeeded[i].Confirmed.Bool || !chargesSeeded[i].Confirmed.Valid {
			unclaimedEnergyUsage += energy
		}
	}

	// Only leave distinct entries
	slices.Sort(userIDs)
	userIDs = slices.Compact(userIDs)
	slices.Sort(chargerIDs)
	chargerIDs = slices.Compact(chargerIDs)

	return chargestatistics.SiteStatistics{
		ChargingDuration: chargingDuration,
		NumberOfUsers:    len(userIDs),
		Energy: roundEnergy(chargestatistics.EnergyStatistics{
			TotalUsage:                    energyTotal,
			ClaimedUsage:                  claimedEnergyUsage,
			RevenueGeneratingClaimedUsage: revenueGeneratingClaimedEnergyUsage,
			UnclaimedUsage:                unclaimedEnergyUsage,
			Cost:                          energyCost,
		}),
		NumberOfChargers: len(chargerIDs),
		NumberOfCharges:  len(chargesSeeded),
		RevenueGenerated: revenueGenerated,
	}
}

func (s *ChargeStatisticsRepositoryTestSuite) TestRetrieveChargeStatisticsBySite() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	groupName := random.SiteName(t)
	siteID := uuid.New()
	siteName := random.SiteName(t)
	chargerType := random.CommercialChargerTypeNullable()
	from := time.Date(2024, 8, 1, 0, 0, 0, 0, time.UTC)
	to := time.Date(2024, 8, 31, 0, 0, 0, 0, time.UTC)

	chargesSeeded, _, _ := seeding.ChargesToRetrieveBySiteForChargeStatistics(ctx, t, s.queriesCharges, groupID, siteID, from, to, siteName, groupName, chargerType, ptr.To(10))
	want := s.prepareExpectedChargeStatistics(chargesSeeded)

	got, err := s.underTest.RetrieveChargeStatisticsBySite(ctx, siteID, from, to)

	require.NoError(t, err)
	require.Equal(t, want, got)
}

func (s *ChargeStatisticsRepositoryTestSuite) TestRetrieveChargeStatisticsBySite_coalescing() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	groupName := random.SiteName(t)
	siteID := uuid.New()
	siteName := random.SiteName(t)
	chargerType := random.CommercialChargerTypeNullable()
	from := time.Now().UTC().Add(-24 * time.Hour)
	to := time.Now().UTC()

	seeding.ChargesToRetrieveBySiteWithNullValues(ctx, t, s.queriesCharges, groupID, siteID, from, siteName, groupName, chargerType, ptr.To(10))

	got, err := s.underTest.RetrieveChargeStatisticsBySite(ctx, uuid.New(), from, to)

	require.NoError(t, err)
	require.Equal(t, chargestatistics.SiteStatistics{}, got)
}

func (s *ChargeStatisticsRepositoryTestSuite) TestRetrieveChargeStatisticsByGroup() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	endDate := time.Date(2024, time.May, 31, 0, 0, 0, 0, time.UTC)
	startDate := endDate.Add(-24 * time.Hour * 90)

	siteID := uuid.NullUUID{
		UUID:  uuid.New(),
		Valid: true,
	}
	chargesSeeded := seeding.ChargeProjections(ctx, t, s.queriesCharges, groupID, uuid.New(), siteID, sql.NullString{}, startDate)
	expected := prepareExpectedGroupChargeStatistics(chargesSeeded)

	got, err := s.underTest.RetrieveChargeStatisticsByGroup(ctx, groupID, startDate, endDate)
	require.NoError(t, err)
	require.Equal(t, expected, got)
}

func prepareExpectedGroupChargeStatistics(charges []*sqlccharges.ProjectionsCharge) *chargestatistics.GroupStatistics {
	var (
		result   chargestatistics.GroupStatistics
		chargers = make(map[string]bool)
		sites    = make(map[string]bool)
		users    = make(map[string]bool)
	)

	for _, charge := range charges {
		result.NumberOfCharges++
		if charge.SiteID.Valid {
			sites[charge.SiteID.UUID.String()] = true
		}
		if charge.ChargerID.Valid {
			chargers[charge.ChargerID.String] = true
		}
		for _, user := range charge.UserIds {
			users[user.String()] = true
		}
		if charge.EnergyTotal.Valid {
			energyUsed, err := strconv.ParseFloat(charge.EnergyTotal.String, 64)
			if err != nil {
				panic("invalid energy value when parsing string as float for expected group statistics")
			}
			result.Energy.TotalUsage += energyUsed

			if charge.Confirmed.Valid && charge.Confirmed.Bool {
				result.Energy.ClaimedUsage += energyUsed
				if charge.SettlementAmount.Valid && charge.SettlementAmount.Int32 > 0 {
					result.Energy.RevenueGeneratingClaimedUsage += energyUsed
				}
			} else {
				result.Energy.UnclaimedUsage += energyUsed
			}
		}
		if charge.ChargeDurationTotal.Valid {
			result.ChargingDuration += int(charge.ChargeDurationTotal.Int32)
		}
		if charge.SettlementAmount.Valid {
			result.RevenueGenerated += int(charge.SettlementAmount.Int32)
		}
		if charge.EnergyCost.Valid {
			result.Energy.Cost += int(charge.EnergyCost.Int32)
		}
	}
	result.NumberOfSites = len(sites)
	result.NumberOfChargers = len(chargers)
	result.NumberOfUsers = len(users)

	result.Energy = roundEnergy(result.Energy)
	return &result
}

func (s *ChargeStatisticsRepositoryTestSuite) TestRetrieveChargeStatisticsByCharger() {
	t := s.T()
	ctx := context.Background()
	endDate := time.Date(2024, time.May, 31, 0, 0, 0, 0, time.UTC)
	startDate := endDate.Add(-24 * time.Hour * 90)
	chargerID := string(random.CommercialChargerID())

	chargesSeeded := seeding.ChargeProjectionsByCharger(ctx, t, s.queriesCharges, chargerID, startDate)
	expected := prepareExpectedChargerChargeStatistics(chargesSeeded)

	got, err := s.underTest.RetrieveChargeStatisticsByCharger(ctx, chargerID, startDate, endDate)
	require.NoError(t, err)
	require.Equal(t, expected, got)
}

func prepareExpectedChargerChargeStatistics(charges []*sqlccharges.ProjectionsCharge) *chargestatistics.ChargerStatistics {
	var (
		result chargestatistics.ChargerStatistics
		users  = make(map[string]bool)
	)

	for _, charge := range charges {
		result.NumberOfCharges++
		for _, user := range charge.UserIds {
			users[user.String()] = true
		}
		if charge.EnergyTotal.Valid {
			energyUsed, err := strconv.ParseFloat(charge.EnergyTotal.String, 64)
			if err != nil {
				panic("invalid energy value when parsing string as float for expected group statistics")
			}
			result.Energy.TotalUsage += energyUsed

			if charge.Confirmed.Valid && charge.Confirmed.Bool {
				result.Energy.ClaimedUsage += energyUsed
				if charge.SettlementAmount.Valid && charge.SettlementAmount.Int32 > 0 {
					result.Energy.RevenueGeneratingClaimedUsage += energyUsed
				}
			} else {
				result.Energy.UnclaimedUsage += energyUsed
			}
		}
		if charge.ChargeDurationTotal.Valid {
			result.ChargingDuration += int(charge.ChargeDurationTotal.Int32)
		}
		if charge.SettlementAmount.Valid {
			result.RevenueGenerated += int(charge.SettlementAmount.Int32)
		}
		if charge.EnergyCost.Valid {
			result.Energy.Cost += int(charge.EnergyCost.Int32)
		}
	}
	result.NumberOfUsers = len(users)

	result.Energy = roundEnergy(result.Energy)
	return &result
}

func (s *ChargeStatisticsRepositoryTestSuite) TestRetrieveGroupIntervalStatsByDay() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	siteID := uuid.NullUUID{
		UUID:  uuid.New(),
		Valid: true,
	}
	interval := day
	to := time.Date(2024, time.May, 31, 0, 0, 0, 0, time.UTC)
	from := to.Add(-24 * time.Hour * 30)

	chargesSeeded := seeding.ChargeProjections(ctx, t, s.queriesCharges, groupID, uuid.New(), siteID, sql.NullString{}, from)
	want := s.prepareExpectedGroupIntervalStatistics(chargesSeeded, interval, from, to)

	got, err := s.underTest.RetrieveBasicUsageByGroup(ctx, groupID, uuid.NullUUID{}, sql.NullString{}, interval, from, to)

	require.NoError(t, err)
	require.Equal(t, want, got)
}

func (s *ChargeStatisticsRepositoryTestSuite) TestRetrieveGroupIntervalStatsByWeek() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	siteID := uuid.NullUUID{
		UUID:  uuid.New(),
		Valid: true,
	}
	interval := week
	to := time.Date(2024, time.May, 31, 0, 0, 0, 0, time.UTC)
	from := to.Add(-24 * time.Hour * 30)
	chargesSeeded := seeding.ChargeProjections(ctx, t, s.queriesCharges, groupID, uuid.New(), siteID, sql.NullString{}, from)
	want := s.prepareExpectedGroupIntervalStatistics(chargesSeeded, interval, from, to)

	got, err := s.underTest.RetrieveBasicUsageByGroup(ctx, groupID, uuid.NullUUID{}, sql.NullString{}, interval, from, to)

	require.NoError(t, err)
	require.Equal(t, want, got)
}

func (s *ChargeStatisticsRepositoryTestSuite) TestRetrieveGroupIntervalStatsByMonth() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	siteID := uuid.NullUUID{
		UUID:  uuid.New(),
		Valid: true,
	}
	interval := month
	to := time.Date(2024, time.May, 31, 0, 0, 0, 0, time.UTC)
	from := to.Add(-24 * time.Hour * 30)

	chargesSeeded := seeding.ChargeProjections(ctx, t, s.queriesCharges, groupID, uuid.New(), siteID, sql.NullString{}, from)
	want := s.prepareExpectedGroupIntervalStatistics(chargesSeeded, interval, from, to)

	got, err := s.underTest.RetrieveBasicUsageByGroup(ctx, groupID, uuid.NullUUID{}, sql.NullString{}, interval, from, to)

	require.NoError(t, err)
	require.Equal(t, want, got)
}

func (s *ChargeStatisticsRepositoryTestSuite) TestRetrieveGroupAndSiteIntervalStatsByDay() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	siteID := uuid.NullUUID{
		UUID:  uuid.New(),
		Valid: true,
	}
	interval := day
	to := time.Date(2024, time.May, 31, 0, 0, 0, 0, time.UTC)
	from := to.Add(-24 * time.Hour * 30)

	chargesSeeded := seeding.ChargeProjections(ctx, t, s.queriesCharges, groupID, uuid.New(), siteID, sql.NullString{}, from)
	secondSite := uuid.NullUUID{
		UUID:  uuid.New(),
		Valid: true,
	}
	_ = seeding.ChargeProjections(ctx, t, s.queriesCharges, groupID, uuid.New(), secondSite, sql.NullString{}, from) // same group and different site
	want := s.prepareExpectedGroupIntervalStatistics(chargesSeeded, interval, from, to)

	got, err := s.underTest.RetrieveBasicUsageByGroup(ctx, groupID, siteID, sql.NullString{}, interval, from, to)

	require.NoError(t, err)
	require.Equal(t, want, got)
}

func (s *ChargeStatisticsRepositoryTestSuite) TestRetrieveGroupAndSiteIntervalStatsByWeek() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	siteID := uuid.NullUUID{
		UUID:  uuid.New(),
		Valid: true,
	}
	interval := week
	to := time.Date(2024, time.May, 31, 0, 0, 0, 0, time.UTC)
	from := to.Add(-24 * time.Hour * 30)

	chargesSeeded := seeding.ChargeProjections(ctx, t, s.queriesCharges, groupID, uuid.New(), siteID, sql.NullString{}, from)
	secondSite := uuid.NullUUID{
		UUID:  uuid.New(),
		Valid: true,
	}
	_ = seeding.ChargeProjections(ctx, t, s.queriesCharges, groupID, uuid.New(), secondSite, sql.NullString{}, from)
	want := s.prepareExpectedGroupIntervalStatistics(chargesSeeded, interval, from, to)

	got, err := s.underTest.RetrieveBasicUsageByGroup(ctx, groupID, siteID, sql.NullString{}, interval, from, to)

	require.NoError(t, err)
	require.Equal(t, want, got)
}

func (s *ChargeStatisticsRepositoryTestSuite) TestRetrieveGroupAndSiteIntervalStatsByMonth() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	siteID := uuid.NullUUID{
		UUID:  uuid.New(),
		Valid: true,
	}
	interval := month
	to := time.Date(2024, time.May, 31, 0, 0, 0, 0, time.UTC)
	from := to.Add(-24 * time.Hour * 30)

	chargesSeeded := seeding.ChargeProjections(ctx, t, s.queriesCharges, groupID, uuid.New(), siteID, sql.NullString{}, from)
	secondSite := uuid.NullUUID{
		UUID:  uuid.New(),
		Valid: true,
	}
	_ = seeding.ChargeProjections(ctx, t, s.queriesCharges, groupID, uuid.New(), secondSite, sql.NullString{}, from) // same group and different site
	want := s.prepareExpectedGroupIntervalStatistics(chargesSeeded, interval, from, to)

	got, err := s.underTest.RetrieveBasicUsageByGroup(ctx, groupID, siteID, sql.NullString{}, interval, from, to)

	require.NoError(t, err)
	require.Equal(t, want, got)
}

func (s *ChargeStatisticsRepositoryTestSuite) prepareExpectedGroupIntervalStatistics(chargesSeeded []*sqlccharges.ProjectionsCharge, interval string, from, to time.Time) []chargestatistics.GroupIntervalStatistics {
	// populate all applicable interval keys
	intervals := generateIntervals(interval, from, to)
	intervalStartDateToStatisticsMap := make(map[string]*chargestatistics.GroupIntervalStatistics, len(intervals))
	for _, i := range intervals {
		intervalStartDateToStatisticsMap[i] = nil
	}

	// value accumulators
	var (
		energyTotal            float64
		revenueGenerated, cost int32
	)

	// for each seeded charge projection
	for i := 0; i < len(chargesSeeded); i++ {
		// discard projections outside the time range
		if s.chargeIsOutsideDateRange(chargesSeeded[i], from, to) {
			continue
		}

		// derive the interval start date
		intervalStartDate := chargesSeeded[i].UnpluggedAt.Time.Format(time.DateOnly)
		now.WeekStartDay = time.Monday
		if interval == week {
			intervalStartDate = now.With(chargesSeeded[i].UnpluggedAt.Time).BeginningOfWeek().Format(time.DateOnly)
		}
		if interval == month {
			intervalStartDate = now.With(chargesSeeded[i].UnpluggedAt.Time).BeginningOfMonth().Format(time.DateOnly)
		}

		// if this is a new interval reset accumulators
		v := intervalStartDateToStatisticsMap[intervalStartDate]
		if v == nil {
			energyTotal = 0
			revenueGenerated = 0
			cost = 0
		}

		// accumulate values
		energy, _ := strconv.ParseFloat(chargesSeeded[i].EnergyTotal.String, 64)
		energyTotal += energy
		revenueGenerated += chargesSeeded[i].SettlementAmount.Int32
		cost += chargesSeeded[i].EnergyCost.Int32

		// replace new accumulated values for the interval
		intervalStartDateToStatisticsMap[intervalStartDate] = &chargestatistics.GroupIntervalStatistics{
			IntervalStartDate: intervalStartDate,
			TotalUsage:        round(energyTotal),
			RevenueGenerated:  revenueGenerated,
			Cost:              cost,
		}
	}

	// move accumulated intervals into a sorted slice
	result := make([]chargestatistics.GroupIntervalStatistics, 0)
	for intervalStartDate := range intervalStartDateToStatisticsMap {
		groupStats := ptr.Deref(intervalStartDateToStatisticsMap[intervalStartDate], chargestatistics.GroupIntervalStatistics{IntervalStartDate: intervalStartDate})
		result = append(result, groupStats)
	}
	slices.SortFunc(result, func(a, b chargestatistics.GroupIntervalStatistics) int {
		return cmp.Compare(a.IntervalStartDate, b.IntervalStartDate)
	})
	return result
}

func (s *ChargeStatisticsRepositoryTestSuite) chargeIsOutsideDateRange(chargesSeed *sqlccharges.ProjectionsCharge, from, to time.Time) bool {
	return chargesSeed.UnpluggedAt.Time.Before(from) || chargesSeed.UnpluggedAt.Time.After(to.AddDate(0, 0, 1))
}

func generateIntervals(interval string, from, to time.Time) []string {
	now.WeekStartDay = time.Monday
	var result []string
	intervalStartDate := from
	for intervalStartDate.Before(to.AddDate(0, 0, 1)) {
		if interval == "day" {
			result = append(result, intervalStartDate.Format(time.DateOnly))
			intervalStartDate = intervalStartDate.AddDate(0, 0, 1)
		}
		if interval == "week" {
			intervalStartDate = now.With(intervalStartDate).BeginningOfWeek()
			result = append(result, intervalStartDate.Format(time.DateOnly))
			intervalStartDate = intervalStartDate.AddDate(0, 0, 7)
		}
		if interval == "month" {
			intervalStartDate = now.With(intervalStartDate).BeginningOfMonth()
			result = append(result, intervalStartDate.Format(time.DateOnly))
			intervalStartDate = intervalStartDate.AddDate(0, 1, 0)
		}
	}
	return result
}

func round(f float64) float64 {
	return math.Round(f*100) / 100
}

func roundEnergy(s chargestatistics.EnergyStatistics) chargestatistics.EnergyStatistics {
	return chargestatistics.EnergyStatistics{
		TotalUsage:                    round(s.TotalUsage),
		ClaimedUsage:                  round(s.ClaimedUsage),
		RevenueGeneratingClaimedUsage: round(s.RevenueGeneratingClaimedUsage),
		UnclaimedUsage:                round(s.UnclaimedUsage),
		Cost:                          s.Cost,
	}
}

func (s *ChargeStatisticsRepositoryTestSuite) TestRetrieveGroupAndChargerIntervalStatsByDay() {
	t := s.T()
	ctx := context.Background()
	groupID := uuid.New()
	siteID := uuid.NullUUID{
		UUID:  uuid.New(),
		Valid: true,
	}
	chargerID := sql.NullString{
		String: "PSL-53934",
		Valid:  true,
	}
	interval := day
	to := time.Date(2024, time.May, 31, 0, 0, 0, 0, time.UTC)
	from := to.Add(-24 * time.Hour * 30)

	chargesSeeded := seeding.ChargeProjections(ctx, t, s.queriesCharges, groupID, uuid.New(), siteID, chargerID, from)
	// same group and site but different charger
	_ = seeding.ChargeProjections(
		ctx,
		t,
		s.queriesCharges,
		groupID,
		uuid.New(),
		siteID,
		sql.NullString{
			String: "PSL-20909",
			Valid:  true,
		},
		from)
	want := s.prepareExpectedGroupIntervalStatistics(chargesSeeded, interval, from, to)

	got, err := s.underTest.RetrieveBasicUsageByGroup(ctx, groupID, uuid.NullUUID{}, chargerID, interval, from, to)

	require.NoError(t, err)
	require.Equal(t, want, got)
}
