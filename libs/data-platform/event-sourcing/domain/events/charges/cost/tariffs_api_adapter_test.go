package cost_test

import (
	"errors"
	energycost "experience/libs/data-platform/cost-calculation/energy-cost"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/cost"
	"experience/libs/data-platform/test/random"
	tariffsAPI "experience/libs/shared/go/external/tariffs-api-client/src"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"
)

func TestToEnergyCostTariffs(t *testing.T) {
	location, _ := random.Location()
	tests := []struct {
		name  string
		input *tariffsAPI.ChargingStationTariffSearchResponseDto
		want  *energycost.Tariff
		err   error
	}{
		{
			name:  "error when no payload",
			input: nil,
			err:   errors.New("no payload"),
		},
		{
			name:  "error when no tariffs found",
			input: &tariffsAPI.ChargingStationTariffSearchResponseDto{},
			err:   cost.ErrNoTariffsFound,
		},
		{
			name: "error when parsing start date",
			input: &tariffsAPI.ChargingStationTariffSearchResponseDto{
				Data: []tariffsAPI.PersistedTariffRowDto{
					{
						TariffInfo: []tariffsAPI.TariffInfoDto{
							{
								Start: "invalid-date",
							},
						},
					},
				},
			},
			err: errors.New("parse start date invalid-date for tariff tier 0: parse date invalid-date: parsing time \"invalid-date\" as \"15:04:05\": cannot parse \"invalid-date\" as \"15\""),
		},
		{
			name: "error when parsing end date",
			input: &tariffsAPI.ChargingStationTariffSearchResponseDto{
				Data: []tariffsAPI.PersistedTariffRowDto{
					{
						TariffInfo: []tariffsAPI.TariffInfoDto{
							{
								Start: "00:00:00",
								End:   "invalid-date",
							},
						},
					},
				},
			},
			err: errors.New("parse end date invalid-date for tariff tier 0: parse date invalid-date: parsing time \"invalid-date\" as \"15:04:05\": cannot parse \"invalid-date\" as \"15\""),
		},
		{
			name: "maps days correctly",
			input: &tariffsAPI.ChargingStationTariffSearchResponseDto{
				Data: []tariffsAPI.PersistedTariffRowDto{
					{
						TariffInfo: []tariffsAPI.TariffInfoDto{
							{
								Start: "00:00:00",
								End:   "00:00:00",
								Days:  []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"},
							},
						},
					},
				},
			},
			want: energycost.NewTariff([]energycost.TariffTier{
				{
					BeginTime: ptr.To(must(time.Parse(time.TimeOnly, "00:00:00"))),
					EndTime:   ptr.To(must(time.Parse(time.TimeOnly, "00:00:00"))),
					Days:      []time.Weekday{time.Monday, time.Tuesday, time.Wednesday, time.Thursday, time.Friday, time.Saturday, time.Sunday},
				},
			}, time.UTC),
		},
		{
			name: "parses timezone correctly",
			input: &tariffsAPI.ChargingStationTariffSearchResponseDto{
				Data: []tariffsAPI.PersistedTariffRowDto{
					{
						TariffInfo: []tariffsAPI.TariffInfoDto{
							{
								Start: "00:00:00",
								End:   "00:00:00",
							},
						},
						Timezone: location.String(),
					},
				},
			},
			want: energycost.NewTariff([]energycost.TariffTier{
				{
					BeginTime: ptr.To(must(time.Parse(time.TimeOnly, "00:00:00"))),
					EndTime:   ptr.To(must(time.Parse(time.TimeOnly, "00:00:00"))),
					Days:      []time.Weekday{},
				},
			}, location),
		},
		{
			name: "converts rate from string to int64",
			input: &tariffsAPI.ChargingStationTariffSearchResponseDto{
				Data: []tariffsAPI.PersistedTariffRowDto{
					{
						TariffInfo: []tariffsAPI.TariffInfoDto{
							{
								Start: "00:00:00",
								End:   "00:00:00",
								Price: float32(0.1234),
							},
						},
					},
				},
			},
			want: energycost.NewTariff([]energycost.TariffTier{
				{
					BeginTime: ptr.To(must(time.Parse(time.TimeOnly, "00:00:00"))),
					EndTime:   ptr.To(must(time.Parse(time.TimeOnly, "00:00:00"))),
					Days:      []time.Weekday{},
					Rate:      123400, // 0.1234 in int64 cents
				},
			}, time.UTC),
		},
		{
			name: "multiple tariffs with different times and days",
			input: &tariffsAPI.ChargingStationTariffSearchResponseDto{
				Data: []tariffsAPI.PersistedTariffRowDto{
					{
						TariffInfo: []tariffsAPI.TariffInfoDto{
							{
								Start: "00:00:00",
								End:   "12:00:00",
								Days:  []string{"Monday", "Tuesday"},
								Price: float32(0.10),
							},
							{
								Start: "12:00:00",
								End:   "23:59:59",
								Days:  []string{"Wednesday", "Thursday", "Friday"},
								Price: float32(0.15),
							},
							{
								Start: "00:00:00",
								End:   "23:59:59",
								Days:  []string{"Saturday", "Sunday"},
								Price: float32(0.20),
							},
						},
					},
				},
			},
			want: energycost.NewTariff([]energycost.TariffTier{
				{
					BeginTime: ptr.To(must(time.Parse(time.TimeOnly, "00:00:00"))),
					EndTime:   ptr.To(must(time.Parse(time.TimeOnly, "12:00:00"))),
					Days:      []time.Weekday{time.Monday, time.Tuesday},
					Rate:      100000, // 0.10 in int64 cents
				},
				{
					BeginTime: ptr.To(must(time.Parse(time.TimeOnly, "12:00:00"))),
					EndTime:   ptr.To(must(time.Parse(time.TimeOnly, "23:59:59"))),
					Days:      []time.Weekday{time.Wednesday, time.Thursday, time.Friday},
					Rate:      150000, // 0.15 in int64 cents
				},
				{
					BeginTime: ptr.To(must(time.Parse(time.TimeOnly, "00:00:00"))),
					EndTime:   ptr.To(must(time.Parse(time.TimeOnly, "23:59:59"))),
					Days:      []time.Weekday{time.Saturday, time.Sunday},
					Rate:      200000, // 0.20 in int64 cents
				},
			}, time.UTC),
		},
		{
			name: "multiple effective tariffs found error - error contains criteria",
			input: &tariffsAPI.ChargingStationTariffSearchResponseDto{
				Data: []tariffsAPI.PersistedTariffRowDto{
					{},
					{},
				},
				Metadata: tariffsAPI.ChargingStationTariffSearchMetadataDto{
					Criteria: tariffsAPI.ChargingStationTariffSearchCriteriaDto{
						Ppid:          "PP-12345",
						EffectiveFrom: ptr.To(must(time.Parse(time.DateOnly, "2006-01-02"))),
						EffectiveTo:   ptr.To(must(time.Parse(time.DateOnly, "2006-01-03"))),
					},
				},
			},
			err: errors.New("multiple tariff rows found, expected only one for criteria: map[effectiveFrom:2006-01-02 00:00:00 +0000 UTC effectiveTo:2006-01-03 00:00:00 +0000 UTC ppid:PP-12345]"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := cost.ToEnergyCostTariffs(tt.input)
			if err != nil && tt.err == nil {
				t.Errorf("expected no error, want %v", err)
			}
			if err == nil && tt.err != nil {
				t.Errorf("expected error %v, want nil", tt.err)
			}
			if err != nil && tt.err != nil && err.Error() != tt.err.Error() {
				t.Errorf("expected error %v, want %v", tt.err, err)
			}
			require.EqualValues(t, tt.want, got)
		})
	}
}

func must(parse time.Time, _ error) time.Time {
	return parse
}
