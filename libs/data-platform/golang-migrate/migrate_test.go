package dbmigrate_test

import (
	dbmigrate "experience/libs/data-platform/golang-migrate"
	"experience/libs/shared/go/aws"
	migrate "experience/libs/shared/go/db-migrate/postgres"
	"experience/libs/shared/go/db/postgres/test"
	"log"
	"os"
	"testing"

	"github.com/brianvoe/gofakeit/v7"
	"github.com/stretchr/testify/suite"

	"github.com/stretchr/testify/require"
)

const (
	expectedUpVersion        uint = 137
	sourceURL                     = "file://./migration"
	nonProdExpectedUpVersion uint = 12
	nonProdSourceURL              = "file://./non-prod/migration"
)

type MigrateTestSuite struct {
	suite.Suite
	testDB              *test.Database
	migrationDatasource migrate.MigrateDatasource
	migrationConfig     *dbmigrate.MigrateConfig
}

func TestMigrateSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping TestMigrateTestSuite integration test")
	}
	suite.Run(t, new(MigrateTestSuite))
}

func (s *MigrateTestSuite) BeforeTest(_, _ string) {
	s.testDB = test.NewDatabase(s.T())
	passwordConfig := s.testDB.PasswordConfig(s.T())

	s.migrationDatasource = migrate.MigrateDatasource{
		PasswordConfig: passwordConfig,
	}

	s.migrationConfig = dbmigrate.NewMigrateConfig(aws.Config{
		Environment: "local",
	}, s.migrationDatasource)

	s.migrationConfig.SourceURL = sourceURL
	s.migrationConfig.NonProdSourceURL = nonProdSourceURL
}

func (s *MigrateTestSuite) AfterTest(_, _ string) {
	err := s.testDB.Container.Terminate(s.T().Context())
	if err != nil {
		log.Fatalf("error terminating postgres container: %s", err)
	}
}

func (s *MigrateTestSuite) TestMigrateDown() {
	logger := log.New(os.Stderr, "", log.Ltime)

	version, nversion := dbmigrate.MigrateUp(s.migrationConfig, logger)
	require.Equal(s.T(), expectedUpVersion, version)
	require.Equal(s.T(), nonProdExpectedUpVersion, nversion)

	version, nversion = dbmigrate.MigrateDown(s.migrationConfig, logger)
	require.Equal(s.T(), uint(0), version)
	require.Equal(s.T(), uint(0), nversion)
}

func (s *MigrateTestSuite) TestMigrateUp() {
	logger := log.New(os.Stderr, "", log.Ltime)

	version, nversion := dbmigrate.MigrateUp(s.migrationConfig, logger)
	require.Equal(s.T(), expectedUpVersion, version)
	require.Equal(s.T(), nonProdExpectedUpVersion, nversion)
}

func (s *MigrateTestSuite) TestArbitraryMigrate() {
	arbitraryVersion := gofakeit.UintRange(1, expectedUpVersion)

	version, err := migrate.Migrate(sourceURL, s.testDB.PasswordConfig(s.T()), nil, true, arbitraryVersion)
	require.NoError(s.T(), err)
	require.Equal(s.T(), arbitraryVersion, version)
}
