# This file is automatically generated by the crowdin.update.sh script
project_id_env: CROWDIN_PROJECT_ID
api_token_env: CROWDIN_PERSONAL_TOKEN
base_url: https://pod-point.api.crowdin.com
preserve_hierarchy: true
files:
  - source: /apps/support/support-tool/webapp/messages/en.json
    translation: /apps/support/support-tool/webapp/messages/%two_letters_code%.json
  - source: /assets/billing-api/i18n/en/emails.json
    translation: /assets/billing-api/i18n/%two_letters_code%/emails.json
  - source: /assets/driver-account-api/i18n/en/auth-emails.json
    translation: /assets/driver-account-api/i18n/%two_letters_code%/auth-emails.json
  - source: /assets/driver-account-api/i18n/en/emails.json
    translation: /assets/driver-account-api/i18n/%two_letters_code%/emails.json
  - source: /assets/installer-api/i18n/en/auth-emails.json
    translation: /assets/installer-api/i18n/%two_letters_code%/auth-emails.json
  - source: /assets/installer-api/i18n/en/emails.json
    translation: /assets/installer-api/i18n/%two_letters_code%/emails.json
  - source: /assets/installer-bff/i18n/en/emails.json
    translation: /assets/installer-bff/i18n/%two_letters_code%/emails.json
  - source: /assets/mobile-api/i18n/en/locales.json
    translation: /assets/mobile-api/i18n/%two_letters_code%/locales.json
  - source: /assets/mobile-api/i18n/en/main.json
    translation: /assets/mobile-api/i18n/%two_letters_code%/main.json
  - source: /assets/notifications-api/i18n/en/notifications.json
    translation: /assets/notifications-api/i18n/%two_letters_code%/notifications.json
  - source: /libs/commercial/site-admin/maizzle/messages/en.json
    translation: /libs/commercial/site-admin/maizzle/messages/%two_letters_code%.json
  - source: /libs/mobile/account/maizzle/messages/classic/en.json
    translation: /libs/mobile/account/maizzle/messages/classic/%two_letters_code%.json
  - source: /libs/mobile/api/maizzle/messages/en.json
    translation: /libs/mobile/api/maizzle/messages/%two_letters_code%.json
  - source: /libs/mobile/billing/maizzle/messages/en.json
    translation: /libs/mobile/billing/maizzle/messages/%two_letters_code%.json
  - source: /libs/mobile/identity/next/pages/src/messages/en.json
    translation: /libs/mobile/identity/next/pages/src/messages/%two_letters_code%.json
  - source: /libs/mobile/installer/api/maizzle/messages/en.json
    translation: /libs/mobile/installer/api/maizzle/messages/%two_letters_code%.json
  - source: /libs/mobile/installer/bff/maizzle/messages/en.json
    translation: /libs/mobile/installer/bff/maizzle/messages/%two_letters_code%.json
  - source: /libs/mobile/nest/auth/messages/en/auth-emails.json
    translation: /libs/mobile/nest/auth/messages/%two_letters_code%/auth-emails.json
  - source: /libs/mobile/nest/auth/messages/en/telephone-codes.json
    translation: /libs/mobile/nest/auth/messages/%two_letters_code%/telephone-codes.json
